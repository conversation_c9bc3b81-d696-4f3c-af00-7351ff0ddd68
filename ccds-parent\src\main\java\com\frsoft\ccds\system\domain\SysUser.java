package com.frsoft.ccds.system.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 系统用户实体类
 * 对应数据库表: sys_user
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class SysUser {

    /** 用户编码 */
    private String userCode;

    /** 登录名 */
    private String loginName;

    /** 密码 */
    private String password;

    /** 用户姓名 */
    private String userName;

    /** 是否启用 */
    private String enabled;

    /** CTI服务器 */
    private String ctiServer;

    /** CTI电话 */
    private String ctiPhone;

    /** 短信最大发送数 */
    private Integer smsMaxNum;
}