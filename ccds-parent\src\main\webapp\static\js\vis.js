function getVrRs(){
	var txts = ["有效","无效"];
	var values = ["1","0"];
	return [txts,values];
}
function getVrRsStr(vrRs){
	return getVrRsStr(getVrRs(),vrRs);
}
function loadVrRs(tdId,name,dfValue){
	var showTxts = [];
	showTxts[0] = getVrRs()[0][0]+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
	showTxts[1] = getVrRs()[0][1]+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
	createRadio(tdId,name,showTxts,getVrRs()[1],dfValue);
}

/* 
	弹出层录入,编辑
*/
function visPopDiv(n,arg1,arg2,arg3,arg4,arg5,arg6){
	var width;
	var height;
	var idPre="";
	var titleTxt="";
	var tips="（带<span class='red'>*</span>为必填字段）";
	var hasParent = false;
	var hasScroll = false;
	var hasCover = true;
	var url;
	
	switch(n){
	    case 1:
	        height =260;
			titleTxt="修改外访排程"+tips;
			url="visRecordAction.do?op=toModTime&id="+arg1;
			hasScroll = false;
			break;
			
		case 3:
	        height =350;
			titleTxt="完成外访"+tips;
			url="visRecordAction.do?op=toReportVis&vrid="+arg1;
			hasScroll = false;
			break;
			
		case 4:
			height =260;
			titleTxt="批量重新排程";
			url="visRecordAction.do?op=toBatchVisRec&delType=1";
			hasScroll = false;
			break;
		
	    case 5:
			height =260;
			titleTxt="外访排程"+tips;
			url="visRecordAction.do?op=toAgrVis&id="+arg1;
			hasScroll = false;
			break;
		
		case 6:
			height =260;
			titleTxt="批量排程"+tips;
			url="visRecordAction.do?op=toBatchVisP";
			hasScroll = false;
			break;
		case 7:
			height=400;
			titleTxt = "导出查询结果";
			//url="visRecordAction.do?op=toOutVis";
			url="visRecordAction.do?op=toOutVisList";
			break;
		case 8:
			height=110;
			width = 290;
			titleTxt = "导出外访记录表";
			url="visRecordAction.do?op=toOutVR&listType="+arg1;
			break;	
		case 9:
			height =260;
			titleTxt="外访排程"+tips;
			url="visRecordAction.do?op=toAgrVis&id="+arg1;
			hasScroll = false;
			break;
			
		case 10:
			height=110;
			width = 290;
			titleTxt = "导出信函";
			url="visRecordAction.do?op=toOutMail";
			break;
		case 11:
			width = 'half';
			height =135;
			titleTxt="批量完成外访"+tips;
			url="visRecordAction.do?op=toBatEndVis";
			hasScroll = false;
			break;
		case 12:
			width = 'half';
			height =170;
			titleTxt="修改外访地区"+tips;
			url="visRecordAction.do?op=toBatUpdVisArea";
			hasScroll = false;
			break;
		case 13:
		    height=405;
		    titleTxt="修改外访申请"+tips;
		    url="visRecordAction.do?op=toUpdVisRec&vrId="+arg1;
			break;	
			
	}
	if(hasParent){
		parent.createPopWindow(idPre,titleTxt,url,width,height,false,hasScroll);
	}
	else{
		createPopWindow(idPre,titleTxt,url,width,height,hasCover,hasScroll);
	}
}