package com.frsoft.ccds.system.controller;

import com.frsoft.ccds.common.core.controller.BaseController;
import com.frsoft.ccds.common.core.domain.AjaxResult;
import com.frsoft.ccds.common.core.page.TableDataInfo;
import com.frsoft.ccds.system.domain.BankCase;
import com.frsoft.ccds.system.service.IBankCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.util.List;

/**
 * 银行案件控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/case")
public class BankCaseController extends BaseController {
    
    @Autowired
    private IBankCaseService bankCaseService;
    
    /**
     * 案件列表页面
     */
    @GetMapping("/listCase")
    public String listCase(Model model, HttpSession session) {
        // 检查用户是否已登录
        Object user = session.getAttribute("user");
        if (user == null) {
            return "redirect:/login";
        }
        
        return "case/listCase";
    }
    
    /**
     * 获取案件列表数据
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(BankCase bankCase) {
        startPage();
        List<BankCase> list = bankCaseService.selectBankCaseList(bankCase);
        return getDataTable(list);
    }
    
    /**
     * 案件详情页面
     */
    @GetMapping("/detail/{casId}")
    public String detail(@PathVariable("casId") Long casId, Model model) {
        BankCase bankCase = bankCaseService.selectBankCaseByCasId(casId);
        model.addAttribute("bankCase", bankCase);
        return "case/detail";
    }
    
    /**
     * 新增案件页面
     */
    @GetMapping("/add")
    public String add() {
        return "case/add";
    }
    
    /**
     * 新增保存案件
     */
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody BankCase bankCase) {
        return toAjax(bankCaseService.insertBankCase(bankCase));
    }
    
    /**
     * 修改案件页面
     */
    @GetMapping("/edit/{casId}")
    public String edit(@PathVariable("casId") Long casId, Model model) {
        BankCase bankCase = bankCaseService.selectBankCaseByCasId(casId);
        model.addAttribute("bankCase", bankCase);
        return "case/edit";
    }
    
    /**
     * 修改保存案件
     */
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody BankCase bankCase) {
        return toAjax(bankCaseService.updateBankCase(bankCase));
    }
    
    /**
     * 删除案件
     */
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(bankCaseService.deleteBankCaseByCasIds(convertToLongArray(ids)));
    }
    
    /**
     * 分配案件页面
     */
    @GetMapping("/assign")
    public String assign() {
        return "case/assign";
    }
    
    /**
     * 分配案件给员工
     */
    @PostMapping("/assign")
    @ResponseBody
    public AjaxResult assignCase(@RequestParam String casIds, 
                                @RequestParam Long seNo,
                                @RequestParam String assignUser) {
        Long[] casIdArray = convertToLongArray(casIds);
        return toAjax(bankCaseService.assignBankCaseToEmployee(casIdArray, seNo, assignUser));
    }
    
    /**
     * 根据员工编号查询案件
     */
    @GetMapping("/listByEmployee/{seNo}")
    @ResponseBody
    public TableDataInfo listByEmployee(@PathVariable("seNo") Long seNo) {
        List<BankCase> list = bankCaseService.selectBankCaseListBySeNo(seNo);
        return getDataTable(list);
    }
    
    /**
     * 根据案件状态查询案件
     */
    @GetMapping("/listByState/{casState}")
    @ResponseBody
    public TableDataInfo listByState(@PathVariable("casState") Integer casState) {
        List<BankCase> list = bankCaseService.selectBankCaseListByState(casState);
        return getDataTable(list);
    }
    
    /**
     * 搜索案件
     */
    @PostMapping("/search")
    @ResponseBody
    public TableDataInfo search(@RequestParam(required = false) String casName,
                               @RequestParam(required = false) String casPhone) {
        List<BankCase> list;
        if (casName != null && !casName.isEmpty()) {
            list = bankCaseService.selectBankCaseListByName(casName);
        } else if (casPhone != null && !casPhone.isEmpty()) {
            list = bankCaseService.selectBankCaseListByPhone(casPhone);
        } else {
            list = bankCaseService.selectBankCaseList(new BankCase());
        }
        return getDataTable(list);
    }
    
    /**
     * 案件统计
     */
    @GetMapping("/statistics")
    @ResponseBody
    public AjaxResult statistics() {
        // 这里可以添加统计逻辑
        return AjaxResult.success();
    }
}
