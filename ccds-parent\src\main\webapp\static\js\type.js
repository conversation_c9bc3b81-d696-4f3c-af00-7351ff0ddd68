function toUpdTypeListConnect(){
	waitSubmit('updTypeListConnectBtn','正在保存');
	var url = 'typeAction.do';
	var pars = $("updTypeListConnectForm").serialize(true);
	new Ajax.Request(url,{
		method : 'post',
		parameters : pars,
		onSuccess : function(response){
			var resp = response.responseText;
			if(resp=="suc"){
				alert("保存成功");
			}
			restoreSubmit('updTypeListConnectBtn','保存');
		},
		onfailure : function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}
function setTypeListConnectItem(jsonData){
	var listHTML = [];
	if(jsonData!=""&&jsonData.list&&jsonData.list.length>0){
		listHTML.push("<div style='text-align:center'><input class='butSize1' id='updTypeListConnectBtn' type='button' class='inputBoxAlign' onclick='toUpdTypeListConnect()' value='保存' /></div>");
		var listObj = jsonData.list;
		var pageObj = jsonData.page;
		listHTML.push("<ul>");
		for(var i=0; i<listObj.length; i++){
			var obj = listObj[i];
			listHTML.push("<li><input type='checkbox' name='childIds' "+obj.enabledType+" value='"+obj.typId+"' id='childIds"+obj.typId+"' /><label for='childIds"+obj.typId+"'>"+obj.typName+"</label></li>");
		}
		listHTML.push("</ul></form>");
		
	}
	else{
		listHTML.push("<div style='text-align:center; color:#666'>无数据</div></form>");
	}
	return listHTML.join('');
}
function showExcLimOfBank(arg){
	if($('childIdsOfBankLayer')!=null){
		floatTipsLayer('childIdsOfBankLayer',-80,10);
	}
	listExcLimOfBank(arg);
}
function showConRsOfBank(arg){
	if($('childIdsOfBankLayer')!=null){
		floatTipsLayer('childIdsOfBankLayer',-80,10);
	}
	listConRsOfBank(arg);
}
function listExcLimOfBank(arg){
	showAjaxList('选择委托方账龄','childIdsOfBankLayer','200px',
		'typeAction.do','op=listExcLimOfBank&bankId='+arg,
		setTypeListConnectItem,"<form id='updTypeListConnectForm' action='typeAction.do' style='font-size:12px; margin:0; padding:2px 0 2px 0;'><input type='hidden' name='op' value='updExcLimOfBank'/><input type='hidden' name='parId' value='"+arg+"'/>",-80,10);
}
function listConRsOfBank(arg){
	showAjaxList('选择委托方催收结果','childIdsOfBankLayer','200px',
		'typeAction.do','op=listConRsOfBank&bankId='+arg,
		setTypeListConnectItem,"<form id='updTypeListConnectForm' action='typeAction.do' style='font-size:12px; margin:0; padding:2px 0 2px 0;'><input type='hidden' name='op' value='updConRsOfBank'/><input type='hidden' name='parId' value='"+arg+"'/>",-80,10);
}

/* 提交表单 */
function doSubmit(){
	var types = document.getElementsByName("typeName");
	for(var i=0;i<types.length;i++){
		if(types[i].value==""){
			alert("类别名称不能为空！");
			types[i].focus();
			return false;
		}
	}
	waitSubmit("doSave","保存中...");
	$("typeSaveForm").submit();
}

function nameCheck(obj){
	autoShort(obj,50);
}

/* 
	弹出层
*/
function addDivNew(n,arg1,arg2){
	var width=300;
	var height=140;
	var idPre="";
	var titleTxt="";
	var tips="（带<span class='red'>*</span>为必填字段）";

	switch(n){	
		case 1:
			titleTxt="添加"+arg1;
			url="typeManage/addType.jsp";
			break;	
		case 2:	
			height=110;
			titleTxt="添加商品类别";
			url="typeManage/addProType.jsp";
			break;
		case 3:	
			titleTxt="添加省份";
			url="typeManage/addCountry.jsp";
			break;
		case 4:	
			height=200;
			titleTxt="添加区县";
			if(arg1!=null){
				url="typeAction.do?op=toAddCity&id="+arg1;
			}
			else{
				url="typeAction.do?op=toAddCity";
			}
			break;
		case 5:	
			height=170;
			titleTxt="添加城市";
			if(arg1!=null){
				url="typeAction.do?op=toAddPro&id="+arg1;
			}
			else{
				url="typeAction.do?op=toAddPro";
			}
			break;
		case 6:	
			height=400;
			titleTxt="设置账龄";
			url="typeAction.do?op=toListExcLimByBank&bankId="+arg1;
			break;
	}
	
	createPopWindow(idPre,titleTxt,url,width,height);
}