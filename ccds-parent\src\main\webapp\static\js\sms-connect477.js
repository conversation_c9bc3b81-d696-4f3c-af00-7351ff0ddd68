function execSend(){
	if($("msgContent").value!=""){
		waitSubmit("save");
		waitSubmit("doCancel");
		var url="connectSMS.do";
		$("op").value="connectToSHBST";
		var args= $("sendForm").serialize(true);
//		args.user="";
//		args.pwd="";
//		args.serverIP="portal.b114.com.cn";
		args.serverIP="*************:18000";
		args.longcode="801";
		new Ajax.Request(url,{
			method		:	'post',
			parameters	:	args,
			onSuccess	:	function(response){
				var rs = response.responseText;
				var rsMsg = "";
				if(rs.indexOf("{")==0){
					rs = rs.evalJSON();
					if(rs.success){
						rsMsg = "发送成功";
					}
					else{
						rsMsg = "发送失败:"+rs.error;
					}
				}
				else{
					rsMsg = rs;
				}
				$("rsLayer").innerHTML = rsMsg+"<div class='deepBlue'>[<a href='javascript:void(0)' onclick='cancel()'>请点此关闭</a>]</div>";
				$("rsLayer").show();
				//setTimeout("cancel()",3000);
			},
			onfailure 	: 	function(response){
				if (transport.status == 404)
					alert("您访问的url地址不存在！");
				else
					alert("Error: status code is " + transport.status);
			}
		});
	}
	else{
		alert("未填写短信内容！");
	}
}