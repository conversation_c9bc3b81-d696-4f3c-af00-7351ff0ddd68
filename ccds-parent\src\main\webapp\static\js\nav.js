/************ nav ******************/
var FST_MENU_INDEX = 2;//功能菜单起始索引值
var ITEM_DESK_INDEX = 1;
var MOD_CASE_INDEX = 0,MOD_CL_INDEX = 1,MOD_ASS_INDEX = 2,MOD_VIS_INDEX = 3,MOD_STAT_INDEX = 4,MOD_OA_INDEX = 5,MOD_EMP_INDEX = 6,MOD_LOAN_INDEX = 7,MOD_CUS_INDEX = 8,MOD_ORD_INDEX = 9,MOD_LAW_INDEX = 10,MOD_SYS_INDEX = 11,MOD_REC_INDEX = 12;//模块数组索引(NAV_MOD_MAPPING中的索引值)
var NAV_MOD_MAPPING = [["bankCase","ca016"],["phoCl","hu029"],["ass","cp006"],["vis","vi009"],["stat","b028"],["oa","c025"],["hr","h008"],["loan","loan001"],["cus","r030"],["sal","s029"],["law","law001"],["sys","sy017"],["rec","r_rec000"]];//模块对应信息(模块ID+模块权限码+模块顺序号)

function getNavMenuItemNum(){
	if(SYS_MOD!=null){
		return (SYS_MOD[0].length+1);
	}
	else {
		return 0;
	}
}
function getNavMenuWidth(){
	return (VER_ID=="1"?84:75);
}
function getNavMenuBorderWidth(){
	return (VER_ID=="1"?1:0);
}
function getNavRight(){
	var menuRigs = "";
	if(SYS_MOD!=null){
		var menuRigsArr = [];
		for(var j=0; j<SYS_MOD[0].length; j++){
			for(var i=0; i<NAV_MOD_MAPPING.length; i++){
				if(SYS_MOD[0][j]==NAV_MOD_MAPPING[i][0]){
					menuRigsArr.push(NAV_MOD_MAPPING[i][1]);
					NAV_MOD_MAPPING[i].push(j+FST_MENU_INDEX);
				}
			}
		}
		menuRigs = menuRigsArr.join("|");
	}
	return menuRigs;
}
function getNavMenuItem(){
	var menuHtmls = new Array();
	if(SYS_MOD!=null){
		for(var i=0; i<SYS_MOD[0].length; i++){
			menuHtmls.push("<td id='menu"+(i+FST_MENU_INDEX)+"' class='menuGray'>"+SYS_MOD[1][i]+"</td>");
		}
	}
	return menuHtmls;
}	
//得到未读信息条数	
var isNoRead=0;//标识是否有未读信息
function getNoReadMesCount(){
	var url = "messageAction.do";
	var pars = "op=getNoReadMesCount&ran=" + Math.random();/*ran为随机数*/

	new Ajax.Request(url,{
		method:'get',
		parameters: pars,
		onSuccess:function(transport){
			var response = transport.responseText.split(",");
			if(!isNaN(response[0])&&response!=""){//检查out是否正常返回
				if($("mailLi")!=null&&$("mailLi").style.display!="none"){
					if(response[0]!=null&&response[0]!="0"){
						response[0]=response[0].toString();
						$("mailLi").style.width=55+response[0].length*6+"px";
						$("mesCount").innerHTML="<label class='small' style='padding-left:1px'>("+response[0]+")</label>";
						$("mailIcon").title="您有"+response[0]+"封新消息";
						isNoRead=1;
						//iconBlink();
				   }
				   else{
						$("mailLi").style.width="50px";
						$("mesCount").innerHTML="";
						$("mailIcon").title="我的消息";
						isNoRead=0;
						//$("mailIcon").src="images/icon/nav/mail.gif";
				   }
			   }
			   if($("schLi")!=null&&$("schLi").style.display!="none"){
				   if(response[1]!=null&&response[1]!="0"){
						response[1]=response[1].toString();
						$("schLi").style.width=55+response[1].length*6+"px";
						$("schCount").innerHTML="<label class='small' style='padding-left:1px'>("+response[1]+")</label>";
						$("schIcon").title="您今天有"+response[1]+"个日程安排";
						
				   }
					else{
						$("schLi").style.width="50px";
						$("schCount").innerHTML="";
						$("schIcon").title="我的日程";
						
				   }
			  }
			  if($("taskLi")!=null&&$("taskLi").style.display!="none"){
				   if(response[2]!=null&&response[2]!="0"){
						response[2]=response[2].toString();
						$("taskLi").style.width=55+response[2].length*6+"px";
						$("taskCount").innerHTML="<label class='small' style='padding-left:1px'>("+response[2]+")</label>";
						$("taskIcon").title="您有"+response[2]+"个待办工作";
						
				   }
					else{
						$("taskLi").style.width="50px";
						$("taskCount").innerHTML="";
						$("taskIcon").title="我的工作安排";
						
				   }
				}
				if($("repLi")!=null&&$("repLi").style.display!="none"){
				   if(response[3]!=null&&response[3]!="0"){
						response[3]=response[3].toString();
						$("repLi").style.width=55+response[3].length*6+"px";
						$("repCount").innerHTML="<label class='small' style='padding-left:1px'>("+response[3]+")</label>";
						$("repIcon").title="您有"+response[3]+"个待批报告";
						
				   }
				   else{
						$("repLi").style.width="50px";
						$("repCount").innerHTML="";
						$("repIcon").title="我的报告";
						
				   }
				}
			}
		},
		onFailure:function(transport){
			//if (transport.status == 404)
				//alert("您访问的url地址不存在！");
			//else
				//alert("Error: status code is " + transport.status);
		}
	});
}
/*		
var speed=500;//闪烁速度
function iconBlink(){
	if(isNoRead!=0){
		$("mailIcon").src="images/icon/nav/mail_new.gif";
		setTimeout("iconBlink2()",speed)
	}
}

function iconBlink2(){
	if(isNoRead!=0){
		$("mailIcon").src="images/icon/nav/mail.gif";
		setTimeout("iconBlink()",speed)
	}
}*/


//选项卡切换
function loadMainMenuItem(){
	var menuWidth=0;
	var maxN = MENU_ITEM_NUM;
	for(var i=1;i<=maxN;i++){
		var o=$("menu"+i);
		if(o!=null){
			o.onmouseover=function(){
				if(this.className!="menuCur")
					this.className="menuOver";
			}
			
			o.onmouseout=function(){
				if(this.className!="menuCur")
					this.className="menuNormal";
			}
			
			o.onclick=function(){
				var n=this.id.substring(4);
				for(var j=1;j<=maxN;j++){
					var obj=$("menu"+j);
					if(obj!=null){
						obj.className="menuNormal";
						if(j==n)
							obj.className="menuCur";
					}
				}
				menuJump(n);
			}
			if(menuWidth==0){
				o.click();
			}
			menuWidth+=getNavMenuWidth();
		}
	}
	$("funMenus").style.width=(menuWidth+getNavMenuBorderWidth())+"px";
}

//选项卡跳转
function menuJump(n){
	switch(parseInt(n)){
			case ITEM_DESK_INDEX://工作台
				parent.mainFrame.location="userAction.do?op=toIndex";
				break;

			case NAV_MOD_MAPPING[MOD_CASE_INDEX][2]://数据管理
				parent.mainFrame.location="case/caseDeskTop.jsp";
				break;
			
			case NAV_MOD_MAPPING[MOD_CL_INDEX][2]://催收管理
				parent.mainFrame.location= "hurry/hurDeskTop.jsp";
				break;
				
			case NAV_MOD_MAPPING[MOD_ASS_INDEX][2]://客服协助
				parent.mainFrame.location="cp/chDeskTop.jsp";
				break;
				
			case NAV_MOD_MAPPING[MOD_VIS_INDEX][2]://外访管理
				parent.mainFrame.location="visRecord/visDeskTop.jsp";
				break;
				
			case NAV_MOD_MAPPING[MOD_STAT_INDEX][2]://报表管理
				parent.mainFrame.location="statCl/statDeskTop.jsp";
				break;
			
			case NAV_MOD_MAPPING[MOD_OA_INDEX][2]://协同办公
				parent.mainFrame.location="oa/oaDeskTop.jsp";
				break;
			
			case NAV_MOD_MAPPING[MOD_EMP_INDEX][2]://人员管理
				parent.mainFrame.location="hr/hrDeskTop.jsp";
				break;
			
			case NAV_MOD_MAPPING[MOD_LOAN_INDEX][2]://贷后检查
				parent.mainFrame.location="loan/loanDeskTop.jsp";
				break;
			
			case NAV_MOD_MAPPING[MOD_CUS_INDEX][2]://客户管理
				parent.mainFrame.location="customer/cusDeskTop.jsp";
				break;
				
			case NAV_MOD_MAPPING[MOD_ORD_INDEX][2]://合同管理
				parent.mainFrame.location="sal/salDeskTop.jsp";
				break;
			
			
			case NAV_MOD_MAPPING[MOD_LAW_INDEX][2]://诉讼管理
				parent.mainFrame.location="law/lawDeskTop.jsp";
				break;
			
			case NAV_MOD_MAPPING[MOD_SYS_INDEX][2]://系统设置
				parent.mainFrame.location="system/sysDeskTop.jsp";
				break;
				
			case NAV_MOD_MAPPING[MOD_REC_INDEX][2]://录音管理
				parent.mainFrame.location="bfmod/recDeskTop.jsp";
				break;
		}
}


//最小化
function hideNav(isHiden){
	if(isHiden==1){
		$("contentbox").show();
		$("showNav").hide();
		parent.indexFrmSet.rows="84,*";
	}	
	else {
		$("contentbox").hide();
		$("showNav").show();
		parent.indexFrmSet.rows="28,*";
	}
}

//还原滑过
function showRestore(isShow){
	if(isShow==1){
		$("restoreNav").className="showOver";
		$("restoreNav").innerHTML="还原";
	}
	else{
		$("restoreNav").className="";
		$("restoreNav").innerHTML="";
	}
}
//收起滑过
function showHide(isShow){
	if(isShow==1){
		$("hideNav").className="showOver";
		$("hideNav").innerHTML="收起";
	}
	else{
		$("hideNav").className="";
		$("hideNav").innerHTML="";
	}
}


//小图标跳转
function optionJump(n){
	var maxOption=MENU_ITEM_NUM;
	switch(n){
		case 0:
			//选项卡切换到工作台
			for(var j=1;j<=maxOption;j++){
				var obj=$("menu"+j);
				if(obj!=null){
					obj.className="menuNormal";
					if(j==ITEM_DESK_INDEX)
						obj.className="menuCur";
				}
			}
			parent.mainFrame.location="userAction.do?op=toIndex";
			break;
			
		case 1:
			//选项卡切换到协同办公
			for(var j=1;j<=maxOption;j++){
				var obj=$("menu"+j);
				if(obj!=null){
					obj.className="menuNormal";
					if(j==NAV_MOD_MAPPING[MOD_OA_INDEX][2])
						obj.className="menuCur";
				}
			}
			parent.mainFrame.location="messageAction.do?op=toListAllMess&mesType=all";
			break;
			
		case 2:
			//选项卡切换到协同办公
			for(var j=1;j<=maxOption;j++){
				var obj=$("menu"+j);
				if(obj!=null){
					obj.className="menuNormal";
					if(j==NAV_MOD_MAPPING[MOD_OA_INDEX][2])
						obj.className="menuCur";
				}
			}
			parent.mainFrame.location="messageAction.do?op=toShowSchList";
			break;
		
		case 3:
			//选项卡切换到协同办公
			for(var j=1;j<=maxOption;j++){
				var obj=$("menu"+j);
				if(obj!=null){
					obj.className="menuNormal";
					if(j==NAV_MOD_MAPPING[MOD_OA_INDEX][2])
						obj.className="menuCur";
				}
			}
			parent.mainFrame.location="salTaskAction.do?op=toListMyTaskSearch&tip=tip";
			break;
		
		case 4:
			//选项卡切换到协同办公
			for(var j=1;j<=maxOption;j++){
				var obj=$("menu"+j);
				if(obj!=null){
					obj.className="menuNormal";
					if(j==NAV_MOD_MAPPING[MOD_OA_INDEX][2])
						obj.className="menuCur";
				}
			}
			parent.mainFrame.location="messageAction.do?op=toListApproRep&isApped=a";
			break;
			
		case 5:
			for(var i=1;i<=maxOption;i++){
				var obj=$("menu"+i);
				if(obj!=null){
					obj.className="menuNormal";
				}
			}
			parent.mainFrame.location="userAction.do?op=goUpdatePwd";
			break;
			
		case 6:
			for(var i=1;i<=maxOption;i++){
				var obj=$("menu"+i);
				if(obj!=null){
					obj.className="menuNormal";
				}
			}
			parent.mainFrame.location="system/sysDeskTop.jsp";
			break;
			
		case 7:
			for(var i=1;i<=maxOption;i++){
				var obj=$("menu"+i);
				if(obj!=null){
					obj.className="menuNormal";
				}
			}
			if(confirm('用户即将退出，确定退出该页面吗？'))
				parent.location.href="sessiondel.jsp";
			break;
			
	}
}

//加载小图标事件
function loadMenuEvent(){
	var imgs=$$("img");
	for(var i=0;i<imgs.length;i++){
		if(imgs[i].id!="comLogo"){
			imgs[i].className="iconGray";
			imgs[i].onmouseover=function(){
				this.className="iconOver";
			}
			imgs[i].onmouseout=function(){
				this.className="iconGray";
			}
		}
	}
	
}

//加载logo
function loadNavLogo(){
	var url='empAction.do';
	var pars = 'op=getTopOrgLogo&ran=' + Math.random();
	new Ajax.Request(url,{
		method:'get',
		parameters: pars,
		onSuccess: function(transport){
			var logoPath = transport.responseText;
			if(logoPath=='null'||logoPath==''){
				logoPath = 'images/nav/nav_logo.png';
			}
			if(getBrowser()=='IE:6.0'){
				$('navLogo').setStyle({
					'filter': 'progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled=true, sizingMethod=noscale, src=\"'+ logoPath +'\")'
				});
			}
			else{
				$('navLogo').setStyle({
					'background':'url('+ logoPath +') no-repeat left center'
				});
			}
			
		},
		onFailure: function(transport){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}

function setNavLogoPath(logoPath){
	if(logoPath==undefined||logoPath=='null'||logoPath==''){
		logoPath = 'images/nav/nav_logo.png';
	}
	if(getBrowser()=='IE:6.0'){
		$('navLogo').setStyle({
			'filter': 'progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled=true, sizingMethod=noscale, src=\"'+ logoPath +'\")'
		});
	}
	else{
		$('navLogo').setStyle({
			'background':'url('+ logoPath +') no-repeat left center'
		});
	}
}
