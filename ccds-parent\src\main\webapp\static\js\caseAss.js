function toBatchPass(){
	if(checkBoxIsEmpty("priKey")){
		var assIds = getBacthIds("-",false)[0];
		if(assIds!=''){
			audAss(assIds);
		}
	}
}

function audAss(assId){
	if(confirm("是否确定通过申请？")){
		var url = "assitanceAction.do";
		var pars = [];
		pars.assId = assId;
		pars.op = "audAss";
		new Ajax.Request(url,{
			method:"post",
			parameters : pars,
			onSuccess: function(transport){
				var isEnd = transport.responseText;
				if(isEnd=='1'){
					loadList();
				}
			},
			onFailure: function(transport){
				alert("保存失败["+transport.status+"]，请重试。");
			}
		});
	}
	
}

function loadAssType(selectorId,typeNames, typeValues,dfValue){
	createSelector(selectorId,typeNames.split(','),typeValues.split(','),dfValue);
}

/* 
	弹出层录入,编辑
*/
function assPopDiv(n,arg){
	var width;
	var height;
	var idPre="";
	var titleTxt="";
	var tips="（带<span class='red'>*</span>为必填字段）";
	var url;
	hasScroll = false;
	hasTitle = true;
	
	switch(n){
	    case 1:
	        height =270;
			titleTxt="完成协催"+tips;
			url="assitanceAction.do?op=toAssRes&assId="+arg[0]+"&assType="+arg[1];
			break;
		case 11:
		 	height=440;
		 	titleTxt="新建还款记录"+tips;
		 	url="cpListAction.do?op=toNewPaid";	
		 	break;
		case 12:
		 	height=300;
			width=360;
			titleTxt="导入还款记录";
			url="cpListAction.do?op=toImportCasePaids";
			break;
		case 13:
		 	height=300;
			width=360;
			titleTxt="导入CP";
			url="cpListAction.do?op=toImportCaseCP";
			break;
		case 14:
		 	height=300;
			width=360;
			titleTxt="导入确认登账";
			url="cpListAction.do?op=toImportConfirmPaid";
			break;
		 case 2:
	        height =400;
			titleTxt="确认登账"+tips;
			url="cpListAction.do?op=toConCP&paId="+arg;
			break;
		case 3:
	        height =310;
			titleTxt="修改还款";
			url="cpListAction.do?op=toModCP&paId="+arg;
			break;
		case 4:
			height=380;
			titleTxt = "选择要导出的字段";
			url="cpListAction.do?op=toOutCp&isAll="+arg;
			break;
		case 5:
			height=80;
			width=200;
			titleTxt = "导出对应的案件协催";
			url="assitanceAction.do?op=toOutCaseAss";
			break;
		case 6:
			height=110;
			width = 290;
			titleTxt = "导出信函";
			url="assitanceAction.do?op=toOutMail";
			break;
		case 7:
			height=300;
			width=360;
			titleTxt="导入协催记录";
			url="assitanceAction.do?op=toImportCaseAss";
			break;
		case 8:
			height=300;
			width=360;
			titleTxt="导入案人数据";
			url="extraInfAction.do?op=toImportExtraInf";
			break;
		case 9:
			height=320;
		 	titleTxt="新建案人数据"+tips;
		 	url="extraInfAction.do?op=toSaveExtraInf";	
		 	break;
		case 'SMS':
		   	height=258;
		   	titleTxt="发送短信";
		   	url="assitanceAction.do?op=toSendSmsAfApp&chId="+arg;	
		   	break;
			
		case 'UPD_ASS':
	        height =260;
			titleTxt="修改协催申请"+tips;
			url="assitanceAction.do?op=toUpdAss&assId="+arg;
			break;
    }
	createPopWindow(idPre,titleTxt,url,width,height,true,hasScroll,hasTitle);
}

/*
	判断批量驳回时是否已经勾选要删除的内容
	type:要删除的实体类型
*/
function cancelBatchAss(divType,delName){
	var priKey= $N("priKey");
	var flag=false;
	for(var i=0;i<priKey.length;i++){ 
		if(priKey[i].checked==true){ 
			flag=true; 
		}
	} 
	if(!flag){
		alert("请选择要操作的"+delName+"！");
		return false;	
	}
	 batchAddDiv1(divType);
}
function batchAddDiv1(n){
	var width=230;
	var height=120;
	var idPre="";
	var titleTxt="";
	switch(n){
		case 1:
			titleTxt="批量撤销案件协催";
			url="assitanceAction.do?op=toBatchCancelAss";
			break;
		case 2:
			titleTxt="作废CP";
			url="cpListAction.do?op=toBatchDropCP&type=1";
			break;
		case 3:
			titleTxt="撤销还款";
			url="cpListAction.do?op=toBatchDropCP&type=2";
			break;
		case 4:
			titleTxt="删除案人数据";
			url="extraInfAction.do?op=toDelMultipleExtraInf";
			break;
	}
	createPopWindow(idPre,titleTxt,url,width,height,true,false);
}