-- MySQL兼容的大量测试数据导入脚本
-- 基于CCDS_data.sql转换并扩展
USE ccds;

-- 清理现有数据（可选）
-- DELETE FROM pho_red;
-- DELETE FROM case_paid;
-- DELETE FROM bank_case;

-- 插入系统偏好设置
INSERT IGNORE INTO sys_pref(syp_name,syp_is_def,syp_is_app,syp_pwd_len,syp_pwd_rule,syp_pwd_upd_days,syp_login_fail,syp_offline_days,syp_has_captcha) 
VALUES('系统默认',1,1,6,'0',0,5,0,1);

-- 插入操作权限数据
INSERT IGNORE INTO lim_operate (ope_code, ope_desc) VALUES 
('case001', '导入案件'),
('case002', '删除批次'),
('case003', '编辑批次'),
('case004', '编辑案件'),
('case005', '退案'),
('case006', '分配案件'),
('case007', '暂停案件'),
('case008', '关闭案件'),
('case009', '恢复案件'),
('case010', '批量评语'),
('case011', '导出案件'),
('case012', '导出催收记录'),
('casehp001', '确认核查'),
('casehp002', '确认登帐'),
('casehp003', '修改CP金额'),
('casehp004', '新建登帐'),
('fun001', '添加权限'),
('fun002', '删除权限'),
('fun003', '修改权限'),
('fun004', '查看详情权限'),
('fun005', '访问权限'),
('fun006', '查看全部'),
('fun007', '管理权限'),
('fun008', '审核权限'),
('fun009', '导入权限'),
('fun010', '添加修改权限'),
('hurr001', '添加评语'),
('hurr002', '添加警告'),
('hurr003', '修改催收小结'),
('hurr004', '修改案件地区'),
('hurr017', '查看部门案件'),
('hurr022', '添加电催记录'),
('hurr031', '添加电话'),
('hurr032', '添加地址'),
('hurr033', '添加辅助催记'),
('hurr034', '添加协催记录'),
('hurr035', '添加案人数据'),
('hurr036', '删除案件附件'),
('hurr037', '访问共债案件'),
('hurr038', '批量标色'),
('hurr039', '发送短信'),
('visR001', '外访审核、撤销、排程'),
('visR003', '修改排程时间'),
('visR005', '完成外访'),
('law001', '访问案件办理记录'),
('law002', '添加修改办理记录'),
('law003', '删除办理记录'),
('law004', '访问案件收费记录'),
('law005', '添加修改收费记录'),
('law006', '删除收费记录'),
('law007', '访问关联催收案件'),
('sys001', '锁定账号'),
('sys002', '查看账号日志'),
('sys003', '删除账号日志'),
('sys004', '设置短信额度'),
('file001', '上传附件'),
('file002', '查看附件');

-- 插入省份数据
INSERT IGNORE INTO cus_province (prv_id, prv_area_id, prv_name, prv_isenabled) VALUES 
(1, 1, '请选择', '1'),
(2, 3, '江苏省', '1'),
(3, 4, '安徽省', '1'),
(4, 5, '浙江省', '1'),
(5, 6, '上海市', '1'),
(6, 7, '北京市', '1'),
(7, 8, '广东省', '1'),
(8, 9, '山东省', '1'),
(9, 10, '河南省', '1'),
(10, 11, '四川省', '1');

-- 插入地区数据
INSERT IGNORE INTO cus_area (are_id, are_name, are_isenabled) VALUES 
(1, '请选择', '1'),
(3, '江苏', '1'),
(4, '安徽', '1'),
(5, '浙江', '1'),
(6, '上海', '1'),
(7, '北京', '1'),
(8, '广东', '1'),
(9, '山东', '1'),
(10, '河南', '1'),
(11, '四川', '1');

-- 插入城市数据
INSERT IGNORE INTO cus_city (city_id, city_prv_id, city_name, city_isenabled) VALUES 
(1, 1, '请选择', '1'),
(2, 2, '南京市', '1'),
(3, 2, '苏州市', '1'),
(4, 2, '无锡市', '1'),
(5, 3, '合肥市', '1'),
(6, 3, '芜湖市', '1'),
(7, 4, '杭州市', '1'),
(8, 4, '宁波市', '1'),
(9, 5, '上海市', '1'),
(10, 6, '北京市', '1');

-- 插入类型列表数据
INSERT IGNORE INTO type_list (typ_name, typ_desc, typ_type, typ_isenabled) VALUES 
-- 工作类型
('日常工作', NULL, '21', '1'),
('会议', NULL, '21', '1'),
('出差', NULL, '21', '1'),
('培训', NULL, '21', '1'),
('来访接待', NULL, '21', '1'),
('外出活动', NULL, '21', '1'),
('商务餐饮', NULL, '21', '1'),
('电话', NULL, '21', '1'),
('上门', NULL, '21', '1'),
('其他', NULL, '21', '1'),
-- 催收类型
('电话', NULL, '22', '1'),
('上门', NULL, '22', '1'),
('来访接待', NULL, '22', '1'),
('会议', NULL, '22', '1'),
('培训', NULL, '22', '1'),
('商务餐饮', NULL, '22', '1'),
('外出活动', NULL, '22', '1'),
('其他', NULL, '22', '1'),
-- 报告类型
('工作日报', NULL, '23', '1'),
('工作周报', NULL, '23', '1'),
('工作月报', NULL, '23', '1'),
('工作年报', NULL, '23', '1'),
('工作报告', NULL, '23', '1'),
('其他报告', NULL, '23', '1'),
-- 案件状态类型
('暂停催收', 'lock_stop_cl', 'caseState', '1'),
('要求退案', 'lock_back_case', 'caseState', '1'),
('PTP', NULL, 'caseState', '1'),
('每日必须跟进', NULL, 'caseState', '1'),
('重点跟进', NULL, 'caseState', '1'),
('跳票', NULL, 'caseState', '1'),
('部分还款', NULL, 'caseState', '1'),
('查账', NULL, 'caseState', '1'),
('有可靠线索', NULL, 'caseState', '1'),
('所有信息均无效', NULL, 'caseState', '1'),
-- 电话状态类型
('无效电话', 'lock_invalid', 'casePhone', '1'),
('可联本人', NULL, 'casePhone', '1'),
('可联第三人', NULL, 'casePhone', '1'),
('可联家人', NULL, 'casePhone', '1'),
('可联村委', NULL, 'casePhone', '1'),
('网搜无效', NULL, 'casePhone', '1'),
('114查询无效', NULL, 'casePhone', '1'),
-- 批次类型
('长帐龄', 'houseLoan', 'caseBat', '1'),
('短账龄', NULL, 'caseBat', '1'),
('一手单', NULL, 'excLimType', '1'),
-- 银行类型
('中国银行', NULL, 'caseBank', '1'),
('工商银行', NULL, 'caseBank', '1'),
('建设银行', NULL, 'caseBank', '1'),
('农业银行', NULL, 'caseBank', '1'),
('交通银行', NULL, 'caseBank', '1'),
('招商银行', NULL, 'caseBank', '1'),
('民生银行', NULL, 'caseBank', '1'),
('兴业银行', NULL, 'caseBank', '1'),
('浦发银行', NULL, 'caseBank', '1'),
('中信银行', NULL, 'caseBank', '1'),
('光大银行', NULL, 'caseBank', '1'),
('华夏银行', NULL, 'caseBank', '1'),
('平安银行', NULL, 'caseBank', '1'),
('广发银行', NULL, 'caseBank', '1'),
('邮储银行', NULL, 'caseBank', '1');

-- 插入功能模块数据
INSERT IGNORE INTO lim_function (fun_code, fun_desc, fun_type) VALUES 
('c000', '访问权限', 'case'),
('c001', '批次管理', 'case'),
('c002', '案件管理', 'case'),
('c003', '催记管理', 'case'),
('c004', '导出', 'case'),
('c005', '案件详情', 'case'),
('hurry000', '访问权限', 'hurry'),
('hurry001', '我的案件', 'hurry'),
('hurry002', '来电查询', 'hurry'),
('hurry003', '主管协催', 'hurry'),
('sys000', '访问权限', 'sys'),
('sys001', '帐号设置', 'sys'),
('sys002', '职位设置', 'sys'),
('sys003', '部门设置', 'sys'),
('sys004', '类别管理', 'sys'),
('sys006', '安全设置', 'sys'),
('sys007', '权限组设置', 'sys'),
('casehelp000', '访问权限', 'cp'),
('casehelp001', '案件协助', 'cp'),
('casehelp002', '银行登帐', 'cp'),
('casehelp004', '案人数据库', 'cp'),
('law001', '访问权限', 'law'),
('law002', '我的诉讼案件', 'law'),
('law003', '部门诉讼案件', 'law'),
('law004', '收费记录', 'law'),
('vis000', '访问权限', 'vis'),
('vis001', '外访管理', 'vis'),
('m001', '我的外访', 'mob'),
('m002', '案件查询', 'mob');

-- 插入锁表数据
INSERT IGNORE INTO lock_table (table_name, table_max) VALUES 
('acc_trans', 0),
('cus_cor_cus', 0),
('sal_org', 0),
('sal_paid_past', 0),
('sal_supplier', 0),
('spo_paid_past', 0),
('wms_change', 0),
('wms_check', 0),
('wms_stro', 0),
('wms_war_in', 0),
('wms_war_out', 0),
('bank_case', 1000),
('pho_red', 5000),
('case_paid', 500);

-- 插入大量员工数据 (sal_emp表)
INSERT IGNORE INTO sal_emp (se_no, se_so_code, se_name, se_ide_code, se_pos, se_sex, se_prob, se_bir_place, se_acc_place, se_birth, se_marry, se_type, se_job_lev, se_job_cate, se_job_title, se_start_day, se_year_pay, se_cost_center, se_email, se_nation, se_poli_status, se_edu, se_tel, se_phone, se_qq, se_msn, se_rec_source, se_prov_fund, se_job_date, se_hou_reg, se_social_code, se_rap, se_address, se_remark, se_bank_name, se_bank_card, se_weal_address, se_weal_pos, se_isovertime, se_attendance, se_card_num, se_pic, se_isenabled, se_inser_date, se_code, se_log, se_alt_date, se_inser_user, se_alt_user, se_end_date, se_edc_bac, se_work_ex, se_user_code, se_per_tel, se_plan_sign_date, se_sign_date, se_credit_date, se_college, se_transfer) VALUES
(1, 'EMP001', '张三', '320101199001011234', '催收专员', '男', '江苏', '南京', '南京市玄武区', '1990-01-01', '已婚', '正式员工', 3, '催收', '高级催收专员', '2020-01-01 00:00:00', '80000', 'CC001', '<EMAIL>', '汉族', '群众', '本科', '025-********', '***********', '*********', '<EMAIL>', '招聘网站', '有', '2020-01-01 00:00:00', '南京', '320101199001011234', '无', '南京市玄武区某某街道123号', '表现优秀', '工商银行', '622202*********0123', '南京市玄武区', '催收部', '是', '正常', 'C001', NULL, '1', '2020-01-01 00:00:00', 'EMP001', NULL, '2024-06-24 00:00:00', 'admin', 'admin', NULL, '南京大学计算机专业', '2018-2020年在某金融公司工作', 'zhangsan', '***********', '2020-01-01 00:00:00', '2020-01-01 00:00:00', '2020-01-01 00:00:00', '南京大学', NULL),
(2, 'EMP002', '李四', '320101199002021234', '催收主管', '女', '江苏', '苏州', '苏州市姑苏区', '1990-02-02', '未婚', '正式员工', 4, '催收', '催收主管', '2019-06-01 00:00:00', '120000', 'CC001', '<EMAIL>', '汉族', '党员', '硕士', '0512-********', '139********', '234567890', '<EMAIL>', '内部推荐', '有', '2019-06-01 00:00:00', '苏州', '320101199002021234', '无', '苏州市姑苏区某某路456号', '管理能力强', '建设银行', '622700*********0123', '苏州市姑苏区', '催收部', '是', '正常', 'C002', NULL, '1', '2019-06-01 00:00:00', 'EMP002', NULL, '2024-06-24 00:00:00', 'admin', 'admin', NULL, '苏州大学金融专业', '2017-2019年在某银行工作', 'lisi', '139********', '2019-06-01 00:00:00', '2019-06-01 00:00:00', '2019-06-01 00:00:00', '苏州大学', NULL),
(3, 'EMP003', '王五', '320101199003031234', '催收专员', '男', '江苏', '无锡', '无锡市梁溪区', '1990-03-03', '已婚', '正式员工', 3, '催收', '催收专员', '2021-03-01 00:00:00', '70000', 'CC001', '<EMAIL>', '汉族', '团员', '本科', '0510-********', '137********', '345678901', '<EMAIL>', '校园招聘', '有', '2021-03-01 00:00:00', '无锡', '320101199003031234', '无', '无锡市梁溪区某某街789号', '工作认真', '农业银行', '622848*********0123', '无锡市梁溪区', '催收部', '是', '正常', 'C003', NULL, '1', '2021-03-01 00:00:00', 'EMP003', NULL, '2024-06-24 00:00:00', 'admin', 'admin', NULL, '江南大学经济学专业', '应届毕业生', 'wangwu', '137********', '2021-03-01 00:00:00', '2021-03-01 00:00:00', '2021-03-01 00:00:00', '江南大学', NULL),
(4, 'EMP004', '赵六', '320101199004041234', '催收专员', '女', '江苏', '常州', '常州市天宁区', '1990-04-04', '已婚', '正式员工', 3, '催收', '催收专员', '2020-08-01 00:00:00', '75000', 'CC001', '<EMAIL>', '汉族', '群众', '本科', '0519-********', '136********', '456789012', '<EMAIL>', '招聘网站', '有', '2020-08-01 00:00:00', '常州', '320101199004041234', '无', '常州市天宁区某某路101号', '沟通能力强', '交通银行', '622260*********0123', '常州市天宁区', '催收部', '是', '正常', 'C004', NULL, '1', '2020-08-01 00:00:00', 'EMP004', NULL, '2024-06-24 00:00:00', 'admin', 'admin', NULL, '常州大学法学专业', '2018-2020年在某律所工作', 'zhaoliu', '136********', '2020-08-01 00:00:00', '2020-08-01 00:00:00', '2020-08-01 00:00:00', '常州大学', NULL),
(5, 'EMP005', '钱七', '320101199005051234', '催收专员', '男', '江苏', '镇江', '镇江市京口区', '1990-05-05', '未婚', '正式员工', 3, '催收', '催收专员', '2022-01-01 00:00:00', '68000', 'CC001', '<EMAIL>', '汉族', '团员', '本科', '0511-********', '135********', '567890123', '<EMAIL>', '内部推荐', '有', '2022-01-01 00:00:00', '镇江', '320101199005051234', '无', '镇江市京口区某某街202号', '学习能力强', '招商银行', '622588*********0123', '镇江市京口区', '催收部', '是', '正常', 'C005', NULL, '1', '2022-01-01 00:00:00', 'EMP005', NULL, '2024-06-24 00:00:00', 'admin', 'admin', NULL, '江苏大学管理学专业', '应届毕业生', 'qianqi', '135********', '2022-01-01 00:00:00', '2022-01-01 00:00:00', '2022-01-01 00:00:00', '江苏大学', NULL);

-- 插入大量用户数据 (sys_user表)
INSERT IGNORE INTO sys_user (user_code, user_login_name, user_pwd, user_name, user_isenabled, user_cti_server, user_cti_phone, user_sms_max_num) VALUES
('U001', 'zhangsan', 'admin', '张三', '1', '*************', '8001', 100),
('U002', 'lisi', 'admin', '李四', '1', '*************', '8002', 200),
('U003', 'wangwu', 'admin', '王五', '1', '*************', '8003', 100),
('U004', 'zhaoliu', 'admin', '赵六', '1', '*************', '8004', 100),
('U005', 'qianqi', 'admin', '钱七', '1', '*************', '8005', 100),
('U006', 'sunba', 'admin', '孙八', '1', '*************', '8006', 100),
('U007', 'zhoujiu', 'admin', '周九', '1', '*************', '8007', 100),
('U008', 'wushi', 'admin', '吴十', '1', '*************', '8008', 100),
('U009', 'zhengyi', 'admin', '郑一', '1', '*************', '8009', 100),
('U010', 'wanger', 'admin', '王二', '1', '*************', '8010', 100);

-- 插入大量案件数据 (bank_case表) - 使用正确的字段名
INSERT IGNORE INTO bank_case (cas_id, cas_code, cas_group, cas_state, cas_name, cas_phone, cas_m, cas_paid_m, cas_se_no, cas_ins_time, cas_ins_user, cas_alt_time, cas_alt_user, cas_remark, cas_id_no, cas_address, cas_bank, cas_card_no) VALUES
-- 工商银行案件
(1, 'CASE001', 'A组', 1, '张明华', '***********', 50000.00, 5000.00, 1, '2024-01-15 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期90天，需重点关注', '320101198501011234', '江苏省南京市玄武区中山路123号', '工商银行', '622202*********0001'),
(2, 'CASE002', 'A组', 1, '李建国', '***********', 80000.00, 0.00, 2, '2024-01-10 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期120天，联系困难', '320101198502021234', '江苏省南京市鼓楼区湖南路456号', '工商银行', '622202*********0002'),
(3, 'CASE003', 'A组', 1, '王小红', '***********', 35000.00, 18000.00, 3, '2024-02-01 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期60天，有还款意愿', '320101198503031234', '江苏省南京市建邺区江东中路789号', '工商银行', '622202*********0003'),
(4, 'CASE004', 'A组', 1, '赵大伟', '***********', 120000.00, 80000.00, 4, '2023-12-20 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期150天，拒绝还款', '320101198504041234', '江苏省南京市雨花台区软件大道101号', '工商银行', '622202*********0004'),
(5, 'CASE005', 'A组', 1, '钱志强', '13800138005', 65000.00, 50000.00, 5, '2024-02-10 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期75天，部分还款', '320101198505051234', '江苏省南京市栖霞区仙林大道202号', '工商银行', '622202*********0005'),

-- 建设银行案件
(6, 'BAT002', 'CASE006', '孙美丽', '320101198506061234', '13800138006', '江苏省苏州市姑苏区观前街303号', '建设银行', '622700*********0006', 45000.00, 47250.00, 45, '2024-03-01 00:00:00', '2024-03-05 00:00:00', 'zhangsan', '催收中', '逾期45天，正在协商', '2024-03-01 00:00:00', '2024-06-24 00:00:00', 'admin'),
(7, 'BAT002', 'CASE007', '周文杰', '320101198507071234', '13800138007', '江苏省苏州市吴中区东吴南路404号', '建设银行', '622700*********0007', 90000.00, 94500.00, 100, '2024-01-25 00:00:00', '2024-01-30 00:00:00', 'lisi', '催收中', '逾期100天，失联状态', '2024-01-25 00:00:00', '2024-06-24 00:00:00', 'admin'),
(8, 'BAT002', 'CASE008', '吴晓东', '320101198508081234', '13800138008', '江苏省苏州市相城区相城大道505号', '建设银行', '622700*********0008', 75000.00, 78750.00, 80, '2024-02-20 00:00:00', '2024-02-25 00:00:00', 'wangwu', '催收中', '逾期80天，承诺还款', '2024-02-20 00:00:00', '2024-06-24 00:00:00', 'admin'),
(9, 'BAT002', 'CASE009', '郑小芳', '320101198509091234', '13800138009', '江苏省苏州市昆山市前进西路606号', '建设银行', '622700*********0009', 55000.00, 57750.00, 65, '2024-03-10 00:00:00', '2024-03-15 00:00:00', 'zhaoliu', '催收中', '逾期65天，家庭困难', '2024-03-10 00:00:00', '2024-06-24 00:00:00', 'admin'),
(10, 'BAT002', 'CASE010', '刘大明', '320101198510101234', '13800138010', '江苏省苏州市张家港市杨舍镇707号', '建设银行', '622700*********0010', 100000.00, 105000.00, 110, '2024-01-05 00:00:00', '2024-01-10 00:00:00', 'qianqi', '催收中', '逾期110天，资产查封', '2024-01-05 00:00:00', '2024-06-24 00:00:00', 'admin'),

-- 农业银行案件
(11, 'BAT003', 'CASE011', '陈志华', '320101198511111234', '13800138011', '江苏省无锡市梁溪区中山路808号', '农业银行', '622848*********0011', 60000.00, 63000.00, 55, '2024-03-20 00:00:00', '2024-03-25 00:00:00', 'zhangsan', '催收中', '逾期55天，积极配合', '2024-03-20 00:00:00', '2024-06-24 00:00:00', 'admin'),
(12, 'BAT003', 'CASE012', '林小玲', '320101198512121234', '13800138012', '江苏省无锡市滨湖区太湖大道909号', '农业银行', '622848*********0012', 85000.00, 89250.00, 95, '2024-02-05 00:00:00', '2024-02-10 00:00:00', 'lisi', '催收中', '逾期95天，经济纠纷', '2024-02-05 00:00:00', '2024-06-24 00:00:00', 'admin'),
(13, 'BAT003', 'CASE013', '黄建军', '320101198601011234', '13800138013', '江苏省无锡市新吴区长江路1010号', '农业银行', '622848*********0013', 70000.00, 73500.00, 70, '2024-03-05 00:00:00', '2024-03-15 00:00:00', 'wangwu', '催收中', '逾期70天，分期还款', '2024-03-05 00:00:00', '2024-06-24 00:00:00', 'admin'),
(14, 'BAT003', 'CASE014', '许大海', '320101198602021234', '13800138014', '江苏省无锡市惠山区惠山大道1111号', '农业银行', '622848*********0014', 95000.00, 99750.00, 85, '2024-02-28 00:00:00', '2024-03-05 00:00:00', 'zhaoliu', '催收中', '逾期85天，法律程序', '2024-02-28 00:00:00', '2024-06-24 00:00:00', 'admin'),
(15, 'BAT003', 'CASE015', '谢小燕', '320101198603031234', '13800138015', '江苏省无锡市江阴市澄江中路1212号', '农业银行', '622848*********0015', 40000.00, 42000.00, 40, '2024-04-01 00:00:00', '2024-04-05 00:00:00', 'qianqi', '催收中', '逾期40天，刚开始催收', '2024-04-01 00:00:00', '2024-06-24 00:00:00', 'admin'),

-- 交通银行案件
(16, 'BAT004', 'CASE016', '马文龙', '320101198604041234', '13800138016', '江苏省常州市天宁区延陵西路1313号', '交通银行', '622260*********0016', 110000.00, 115500.00, 130, '2023-12-15 00:00:00', '2023-12-20 00:00:00', 'zhangsan', '催收中', '逾期130天，高额欠款', '2023-12-15 00:00:00', '2024-06-24 00:00:00', 'admin'),
(17, 'BAT004', 'CASE017', '杨小梅', '320101198605051234', '13800138017', '江苏省常州市钟楼区南大街1414号', '交通银行', '622260*********0017', 65000.00, 68250.00, 75, '2024-02-18 00:00:00', '2024-02-23 00:00:00', 'lisi', '催收中', '逾期75天，还款计划', '2024-02-18 00:00:00', '2024-06-24 00:00:00', 'admin'),
(18, 'BAT004', 'CASE018', '朱志勇', '320101198606061234', '13800138018', '江苏省常州市新北区太湖东路1515号', '交通银行', '622260*********0018', 80000.00, 84000.00, 90, '2024-01-28 00:00:00', '2024-02-02 00:00:00', 'wangwu', '催收中', '逾期90天，联系中断', '2024-01-28 00:00:00', '2024-06-24 00:00:00', 'admin'),
(19, 'BAT004', 'CASE019', '何小丽', '320101198607071234', '13800138019', '江苏省常州市武进区湖塘镇1616号', '交通银行', '622260*********0019', 50000.00, 52500.00, 60, '2024-03-12 00:00:00', '2024-03-17 00:00:00', 'zhaoliu', '催收中', '逾期60天，医疗费用', '2024-03-12 00:00:00', '2024-06-24 00:00:00', 'admin'),
(20, 'BAT004', 'CASE020', '冯大强', '320101198608081234', '***********', '江苏省常州市金坛区金城镇1717号', '交通银行', '622260*********0020', 75000.00, 78750.00, 105, '2024-01-12 00:00:00', '2024-01-17 00:00:00', 'qianqi', '催收中', '逾期105天，生意失败', '2024-01-12 00:00:00', '2024-06-24 00:00:00', 'admin');

-- 继续添加更多案件数据 (招商银行、中国银行等)
INSERT IGNORE INTO bank_case (bac_id, bac_bat_code, bac_case_code, bac_cus_name, bac_cus_ide_code, bac_cus_phone, bac_cus_address, bac_bank_name, bac_card_num, bac_org_money, bac_cur_money, bac_overdue_days, bac_case_date, bac_assign_date, bac_assign_user, bac_case_status, bac_remark, bac_create_date, bac_update_date, bac_update_user) VALUES
-- 招商银行案件 (21-30)
(21, 'BAT005', 'CASE021', '蒋小华', '320101198609091234', '***********', '江苏省镇江市京口区中山东路1818号', '招商银行', '622588*********0021', 55000.00, 57750.00, 50, '2024-03-25 00:00:00', '2024-03-30 00:00:00', 'zhangsan', '催收中', '逾期50天，新案件', '2024-03-25 00:00:00', '2024-06-24 00:00:00', 'admin'),
(22, 'BAT005', 'CASE022', '韩志明', '320101198610101234', '***********', '江苏省镇江市润州区金山路1919号', '招商银行', '622588*********0022', 90000.00, 94500.00, 115, '2024-01-08 00:00:00', '2024-01-13 00:00:00', 'lisi', '催收中', '逾期115天，投资失败', '2024-01-08 00:00:00', '2024-06-24 00:00:00', 'admin'),
(23, 'BAT005', 'CASE023', '曹小红', '320101198611111234', '13800138023', '江苏省镇江市丹徒区上党镇2020号', '招商银行', '622588*********0023', 70000.00, 73500.00, 85, '2024-02-12 00:00:00', '2024-02-17 00:00:00', 'wangwu', '催收中', '逾期85天，离婚纠纷', '2024-02-12 00:00:00', '2024-06-24 00:00:00', 'admin'),
(24, 'BAT005', 'CASE024', '邓大伟', '320101198612121234', '13800138024', '江苏省镇江市丹阳市云阳镇2121号', '招商银行', '622588*********0024', 85000.00, 89250.00, 95, '2024-01-30 00:00:00', '2024-02-04 00:00:00', 'zhaoliu', '催收中', '逾期95天，房产抵押', '2024-01-30 00:00:00', '2024-06-24 00:00:00', 'admin'),
(25, 'BAT005', 'CASE025', '范小玲', '320101198701011234', '13800138025', '江苏省镇江市扬中市三茅镇2222号', '招商银行', '622588*********0025', 45000.00, 47250.00, 65, '2024-03-08 00:00:00', '2024-03-13 00:00:00', 'qianqi', '催收中', '逾期65天，教育支出', '2024-03-08 00:00:00', '2024-06-24 00:00:00', 'admin'),

-- 中国银行案件 (26-35)
(26, 'BAT006', 'CASE026', '高志强', '320101198702021234', '13800138026', '浙江省杭州市西湖区文三路2323号', '中国银行', '621786*********0026', 100000.00, 105000.00, 120, '2024-01-03 00:00:00', '2024-01-08 00:00:00', 'zhangsan', '催收中', '逾期120天，跨省案件', '2024-01-03 00:00:00', '2024-06-24 00:00:00', 'admin'),
(27, 'BAT006', 'CASE027', '胡小燕', '320101198703031234', '13800138027', '浙江省杭州市拱墅区莫干山路2424号', '中国银行', '621786*********0027', 60000.00, 63000.00, 70, '2024-02-25 00:00:00', '2024-03-02 00:00:00', 'lisi', '催收中', '逾期70天，创业失败', '2024-02-25 00:00:00', '2024-06-24 00:00:00', 'admin'),
(28, 'BAT006', 'CASE028', '姜大明', '320101198704041234', '13800138028', '浙江省杭州市江干区钱江路2525号', '中国银行', '621786*********0028', 75000.00, 78750.00, 80, '2024-02-15 00:00:00', '2024-02-20 00:00:00', 'wangwu', '催收中', '逾期80天，股票亏损', '2024-02-15 00:00:00', '2024-06-24 00:00:00', 'admin'),
(29, 'BAT006', 'CASE029', '孔小丽', '320101198705051234', '13800138029', '浙江省杭州市下城区体育场路2626号', '中国银行', '621786*********0029', 55000.00, 57750.00, 55, '2024-03-18 00:00:00', '2024-03-23 00:00:00', 'zhaoliu', '催收中', '逾期55天，医疗开支', '2024-03-18 00:00:00', '2024-06-24 00:00:00', 'admin'),
(30, 'BAT006', 'CASE030', '雷志华', '320101198706061234', '13800138030', '浙江省杭州市上城区解放路2727号', '中国银行', '621786*********0030', 80000.00, 84000.00, 90, '2024-01-25 00:00:00', '2024-01-30 00:00:00', 'qianqi', '催收中', '逾期90天，装修贷款', '2024-01-25 00:00:00', '2024-06-24 00:00:00', 'admin'),

-- 民生银行案件 (31-40)
(31, 'BAT007', 'CASE031', '李大龙', '320101198707071234', '13800138031', '上海市浦东新区陆家嘴环路2828号', '民生银行', '622622*********0031', 95000.00, 99750.00, 110, '2024-01-15 00:00:00', '2024-01-20 00:00:00', 'zhangsan', '催收中', '逾期110天，上海案件', '2024-01-15 00:00:00', '2024-06-24 00:00:00', 'admin'),
(32, 'BAT007', 'CASE032', '刘小梅', '320101198708081234', '13800138032', '上海市黄浦区南京东路2929号', '民生银行', '622622*********0032', 65000.00, 68250.00, 75, '2024-02-22 00:00:00', '2024-02-27 00:00:00', 'lisi', '催收中', '逾期75天，消费贷款', '2024-02-22 00:00:00', '2024-06-24 00:00:00', 'admin'),
(33, 'BAT007', 'CASE033', '毛志勇', '320101198709091234', '13800138033', '上海市静安区南京西路3030号', '民生银行', '622622*********0033', 70000.00, 73500.00, 65, '2024-03-02 00:00:00', '2024-03-07 00:00:00', 'wangwu', '催收中', '逾期65天，房贷逾期', '2024-03-02 00:00:00', '2024-06-24 00:00:00', 'admin'),
(34, 'BAT007', 'CASE034', '倪小芳', '320101198710101234', '13800138034', '上海市徐汇区淮海中路3131号', '民生银行', '622622*********0034', 50000.00, 52500.00, 45, '2024-04-05 00:00:00', '2024-04-10 00:00:00', 'zhaoliu', '催收中', '逾期45天，信用卡债务', '2024-04-05 00:00:00', '2024-06-24 00:00:00', 'admin'),
(35, 'BAT007', 'CASE035', '欧阳华', '320101198711111234', '13800138035', '上海市长宁区延安西路3232号', '民生银行', '622622*********0035', 85000.00, 89250.00, 100, '2024-01-18 00:00:00', '2024-01-23 00:00:00', 'qianqi', '催收中', '逾期100天，经营贷款', '2024-01-18 00:00:00', '2024-06-24 00:00:00', 'admin'),

-- 兴业银行案件 (36-45)
(36, 'BAT008', 'CASE036', '潘大伟', '320101198712121234', '13800138036', '北京市朝阳区建国门外大街3333号', '兴业银行', '622262*********0036', 120000.00, 126000.00, 140, '2023-12-10 00:00:00', '2023-12-15 00:00:00', 'zhangsan', '催收中', '逾期140天，北京案件', '2023-12-10 00:00:00', '2024-06-24 00:00:00', 'admin'),
(37, 'BAT008', 'CASE037', '秦小玲', '320101198801011234', '13800138037', '北京市海淀区中关村大街3434号', '兴业银行', '622262*********0037', 75000.00, 78750.00, 85, '2024-02-08 00:00:00', '2024-02-13 00:00:00', 'lisi', '催收中', '逾期85天，科技公司', '2024-02-08 00:00:00', '2024-06-24 00:00:00', 'admin'),
(38, 'BAT008', 'CASE038', '任志明', '320101198802021234', '13800138038', '北京市西城区金融大街3535号', '兴业银行', '622262*********0038', 90000.00, 94500.00, 95, '2024-01-22 00:00:00', '2024-01-27 00:00:00', 'wangwu', '催收中', '逾期95天，金融从业', '2024-01-22 00:00:00', '2024-06-24 00:00:00', 'admin'),
(39, 'BAT008', 'CASE039', '宋小红', '320101198803031234', '13800138039', '北京市东城区王府井大街3636号', '兴业银行', '622262*********0039', 60000.00, 63000.00, 70, '2024-02-28 00:00:00', '2024-03-05 00:00:00', 'zhaoliu', '催收中', '逾期70天，商业贷款', '2024-02-28 00:00:00', '2024-06-24 00:00:00', 'admin'),
(40, 'BAT008', 'CASE040', '唐大强', '320101198804041234', '13800138040', '北京市丰台区丽泽金融商务区3737号', '兴业银行', '622262*********0040', 105000.00, 110250.00, 125, '2024-01-02 00:00:00', '2024-01-07 00:00:00', 'qianqi', '催收中', '逾期125天，投资亏损', '2024-01-02 00:00:00', '2024-06-24 00:00:00', 'admin');

-- 插入大量催收记录数据 (pho_red表) - 使用正确的字段名
INSERT IGNORE INTO pho_red (pr_id, pr_cas_id, pr_se_no, pr_con_type, pr_time, pr_contact, pr_content, pr_name, pr_rel) VALUES
-- 案件1的催收记录
(1, 1, 1, '电话', '2024-01-21 09:15:00', '***********', '客户承认欠款，表示近期会还款，但需要时间筹集资金。态度较好，有还款意愿。', '张明华', '本人'),
(2, 1, 1, '电话', '2024-01-25 14:45:00', '***********', '客户表示资金紧张，申请延期还款。已告知逾期后果，客户承诺本月底前还款。', '张明华', '本人'),
(3, 1, 1, '电话', '2024-01-30 10:35:00', '***********', '客户未按承诺还款，表示生意出现问题，需要更多时间。已发送催收函。', '张明华', '本人'),
(4, 1, 1, '上门', '2024-02-05 16:30:00', '江苏省南京市玄武区中山路123号', '上门拜访客户，了解实际情况。客户确实遇到经营困难，但有固定资产。协商分期还款方案。', '张明华', '本人'),
(5, 1, 1, '电话', '2024-02-10 11:20:00', '***********', '跟进分期还款方案，客户同意每月还款5000元，共分10期。已签署还款协议。', '张明华', '本人'),

-- 案件2的催收记录
(6, 2, 'lisi', '电话', '2024-01-16', '09:30:00', '09:40:00', '***********', '无人接听', '多次拨打客户电话无人接听，疑似更换号码或故意回避。', '2024-01-18', '需要查找新的联系方式', '2024-01-16 09:40:00', '2024-01-16 09:40:00'),
(7, 2, 'lisi', '电话', '2024-01-18', '14:00:00', '14:10:00', '***********', '关机', '客户电话关机，通过114查询无果。尝试联系紧急联系人。', '2024-01-20', '联系紧急联系人', '2024-01-18 14:10:00', '2024-01-18 14:10:00'),
(8, 2, 'lisi', '电话', '2024-01-20', '16:20:00', '16:35:00', '13900139001', '联系到第三人', '联系到客户朋友，得知客户因生意失败外出打工，暂时无法还款。', '2024-01-25', '继续寻找客户下落', '2024-01-20 16:35:00', '2024-01-20 16:35:00'),
(9, 2, 'lisi', '上门', '2024-01-25', '10:00:00', '11:00:00', '***********', '未见到本人', '上门拜访客户住址，邻居反映客户已搬走，留下新地址信息。', '2024-01-30', '按新地址继续寻找', '2024-01-25 11:00:00', '2024-01-25 11:00:00'),
(10, 2, 'lisi', '电话', '2024-01-30', '13:45:00', '14:00:00', '***********', '联系到本人', '终于联系到客户，客户承认困难，表示正在努力筹款，请求宽限时间。', '2024-02-05', '给予最后期限', '2024-01-30 14:00:00', '2024-01-30 14:00:00'),

-- 案件3的催收记录
(11, 3, 'wangwu', '电话', '2024-02-06', '09:15:00', '09:30:00', '***********', '联系到本人', '客户态度良好，承认欠款，表示因为孩子教育费用导致资金紧张。', '2024-02-10', '客户有还款意愿，可协商', '2024-02-06 09:30:00', '2024-02-06 09:30:00'),
(12, 3, 'wangwu', '电话', '2024-02-10', '15:20:00', '15:35:00', '***********', '联系到本人', '客户提出分期还款申请，希望分6期偿还。已向上级汇报，等待审批。', '2024-02-15', '等待分期审批结果', '2024-02-10 15:35:00', '2024-02-10 15:35:00'),
(13, 3, 'wangwu', '电话', '2024-02-15', '11:10:00', '11:25:00', '***********', '联系到本人', '分期方案获得批准，客户同意每月还款6000元。已发送分期协议。', '2024-02-20', '跟进协议签署情况', '2024-02-15 11:25:00', '2024-02-15 11:25:00'),
(14, 3, 'wangwu', '电话', '2024-02-20', '14:40:00', '14:50:00', '***********', '联系到本人', '客户已签署分期协议，承诺按时还款。首期款项将于月底前支付。', '2024-02-28', '跟进首期还款情况', '2024-02-20 14:50:00', '2024-02-20 14:50:00'),
(15, 3, 'wangwu', '电话', '2024-02-28', '16:00:00', '16:10:00', '***********', '联系到本人', '客户按约定支付首期款项6000元，态度积极配合。', '2024-03-28', '跟进下期还款', '2024-02-28 16:10:00', '2024-02-28 16:10:00'),

-- 案件4的催收记录
(16, 4, 'zhaoliu', '电话', '2023-12-26', '10:00:00', '10:15:00', '***********', '联系到本人', '客户态度恶劣，拒绝承认欠款，声称银行计算错误。', '2023-12-30', '客户态度不配合，需要强化措施', '2023-12-26 10:15:00', '2023-12-26 10:15:00'),
(17, 4, 'zhaoliu', '电话', '2023-12-30', '14:30:00', '14:45:00', '***********', '联系到本人', '向客户详细说明欠款明细，客户仍然拒绝还款，威胁要投诉。', '2024-01-05', '记录客户威胁行为', '2023-12-30 14:45:00', '2023-12-30 14:45:00'),
(18, 4, 'zhaoliu', '上门', '2024-01-05', '09:30:00', '10:30:00', '***********', '见到本人', '上门催收，客户拒绝开门，通过门缝沟通。态度依然强硬。', '2024-01-10', '考虑法律途径', '2024-01-05 10:30:00', '2024-01-05 10:30:00'),
(19, 4, 'zhaoliu', '电话', '2024-01-10', '15:45:00', '16:00:00', '***********', '联系到本人', '告知客户将采取法律措施，客户态度有所软化，表示考虑还款。', '2024-01-15', '给予最后考虑时间', '2024-01-10 16:00:00', '2024-01-10 16:00:00'),
(20, 4, 'zhaoliu', '电话', '2024-01-15', '11:20:00', '11:40:00', '***********', '联系到本人', '客户同意部分还款，先支付50000元，剩余部分协商解决。', '2024-01-20', '跟进部分还款执行', '2024-01-15 11:40:00', '2024-01-15 11:40:00'),

-- 案件5的催收记录
(21, 5, 'qianqi', '电话', '2024-02-16', '09:45:00', '10:00:00', '13800138005', '联系到本人', '客户主动联系，表示已准备部分资金，希望先还一部分。', '2024-02-20', '客户主动性强，积极跟进', '2024-02-16 10:00:00', '2024-02-16 10:00:00'),
(22, 5, 'qianqi', '电话', '2024-02-20', '13:30:00', '13:45:00', '13800138005', '联系到本人', '客户支付20000元，承诺剩余款项分3期支付。态度诚恳。', '2024-02-25', '跟进分期还款计划', '2024-02-20 13:45:00', '2024-02-20 13:45:00'),
(23, 5, 'qianqi', '电话', '2024-02-25', '16:15:00', '16:25:00', '13800138005', '联系到本人', '确认分期还款计划，每月15000元，共3期。客户同意并签署协议。', '2024-03-25', '跟进第一期还款', '2024-02-25 16:25:00', '2024-02-25 16:25:00'),
(24, 5, 'qianqi', '电话', '2024-03-25', '10:30:00', '10:40:00', '13800138005', '联系到本人', '客户按时支付第一期款项15000元，表现良好。', '2024-04-25', '跟进第二期还款', '2024-03-25 10:40:00', '2024-03-25 10:40:00'),
(25, 5, 'qianqi', '电话', '2024-04-25', '14:20:00', '14:30:00', '13800138005', '联系到本人', '客户按时支付第二期款项15000元，剩余最后一期。', '2024-05-25', '跟进最后一期还款', '2024-04-25 14:30:00', '2024-04-25 14:30:00');

-- 插入大量还款记录数据 (case_paid表) - 使用正确的字段名
INSERT IGNORE INTO case_paid (pa_id, pa_cas_id, pa_paid_time, pa_paid_num, pa_writer, pa_wri_time, pa_sur_remark, pa_state) VALUES
-- 案件1的还款记录
(1, 1, '2024-02-15 10:30:00', 5000.00, 'admin', '2024-02-15 10:30:00', '分期还款第一期，客户按协议执行', 1),
(2, 1, '2024-03-15 11:20:00', 5000.00, 'admin', '2024-03-15 11:20:00', '分期还款第二期，客户按时还款', 1),
(3, 1, '2024-04-15 09:45:00', 5000.00, 'admin', '2024-04-15 09:45:00', '分期还款第三期，客户继续配合', 1),
(4, 1, '2024-05-15 14:10:00', 5000.00, 'admin', '2024-05-15 14:10:00', '分期还款第四期，还款记录良好', 1),
(5, 1, '2024-06-15 16:30:00', 5000.00, 'admin', '2024-06-15 16:30:00', '分期还款第五期，客户信用恢复', 1),

-- 案件3的还款记录
(6, 3, 'wangwu', '2024-02-28', 6000.00, '现金', '建设银行', '分期还款首期，客户现金支付', '2024-02-28 15:20:00', '2024-02-28 15:20:00', '已确认'),
(7, 3, 'wangwu', '2024-03-28', 6000.00, '银行转账', '建设银行', '分期还款第二期，转账支付', '2024-03-28 10:15:00', '2024-03-28 10:15:00', '已确认'),
(8, 3, 'wangwu', '2024-04-28', 6000.00, '银行转账', '建设银行', '分期还款第三期，按时执行', '2024-04-28 13:40:00', '2024-04-28 13:40:00', '已确认'),
(9, 3, 'wangwu', '2024-05-28', 6000.00, '银行转账', '建设银行', '分期还款第四期，客户配合度高', '2024-05-28 11:55:00', '2024-05-28 11:55:00', '已确认'),

-- 案件4的还款记录
(10, 4, 'zhaoliu', '2024-01-20', 50000.00, '银行转账', '工商银行', '部分还款，客户态度转变', '2024-01-20 09:30:00', '2024-01-20 09:30:00', '已确认'),
(11, 4, 'zhaoliu', '2024-03-20', 30000.00, '银行转账', '工商银行', '第二次部分还款，协商进展顺利', '2024-03-20 14:45:00', '2024-03-20 14:45:00', '已确认'),

-- 案件5的还款记录
(12, 5, 'qianqi', '2024-02-20', 20000.00, '现金', '农业银行', '首次部分还款，客户主动支付', '2024-02-20 16:00:00', '2024-02-20 16:00:00', '已确认'),
(13, 5, 'qianqi', '2024-03-25', 15000.00, '银行转账', '农业银行', '分期第一期，按协议执行', '2024-03-25 10:40:00', '2024-03-25 10:40:00', '已确认'),
(14, 5, 'qianqi', '2024-04-25', 15000.00, '银行转账', '农业银行', '分期第二期，客户信用良好', '2024-04-25 14:30:00', '2024-04-25 14:30:00', '已确认'),

-- 案件6的还款记录
(15, 6, 'zhangsan', '2024-04-10', 10000.00, '银行转账', '建设银行', '部分还款，客户经济好转', '2024-04-10 11:20:00', '2024-04-10 11:20:00', '已确认'),
(16, 6, 'zhangsan', '2024-05-10', 15000.00, '银行转账', '建设银行', '继续还款，剩余金额减少', '2024-05-10 15:30:00', '2024-05-10 15:30:00', '已确认'),

-- 案件7的还款记录
(17, 7, 'lisi', '2024-03-15', 20000.00, '现金', '建设银行', '找到客户后的首次还款', '2024-03-15 09:45:00', '2024-03-15 09:45:00', '已确认'),

-- 案件8的还款记录
(18, 8, 'wangwu', '2024-04-01', 25000.00, '银行转账', '建设银行', '客户履行承诺，大额还款', '2024-04-01 13:15:00', '2024-04-01 13:15:00', '已确认'),

-- 案件11的还款记录
(19, 11, 'zhangsan', '2024-05-01', 30000.00, '银行转账', '农业银行', '客户积极配合，主动还款', '2024-05-01 10:20:00', '2024-05-01 10:20:00', '已确认'),

-- 案件13的还款记录
(20, 13, 'wangwu', '2024-04-20', 12000.00, '银行转账', '农业银行', '分期还款第一期', '2024-04-20 14:50:00', '2024-04-20 14:50:00', '已确认'),
(21, 13, 'wangwu', '2024-05-20', 12000.00, '银行转账', '农业银行', '分期还款第二期', '2024-05-20 16:10:00', '2024-05-20 16:10:00', '已确认'),

-- 案件17的还款记录
(22, 17, 'lisi', '2024-05-05', 18000.00, '现金', '交通银行', '客户按还款计划执行', '2024-05-05 11:30:00', '2024-05-05 11:30:00', '已确认'),

-- 案件19的还款记录
(23, 19, 'zhaoliu', '2024-05-15', 8000.00, '银行转账', '交通银行', '医疗费用缓解后的还款', '2024-05-15 13:20:00', '2024-05-15 13:20:00', '已确认'),

-- 案件21的还款记录
(24, 21, 'zhangsan', '2024-05-25', 22000.00, '银行转账', '招商银行', '新案件快速回款', '2024-05-25 15:40:00', '2024-05-25 15:40:00', '已确认'),

-- 案件25的还款记录
(25, 25, 'qianqi', '2024-06-01', 15000.00, '现金', '招商银行', '教育支出缓解后还款', '2024-06-01 09:25:00', '2024-06-01 09:25:00', '已确认'),

-- 案件27的还款记录
(26, 27, 'lisi', '2024-06-10', 20000.00, '银行转账', '中国银行', '创业项目有起色后还款', '2024-06-10 12:15:00', '2024-06-10 12:15:00', '已确认'),

-- 案件29的还款记录
(27, 29, 'zhaoliu', '2024-06-15', 12000.00, '银行转账', '中国银行', '医疗费用问题解决后还款', '2024-06-15 14:35:00', '2024-06-15 14:35:00', '已确认'),

-- 案件32的还款记录
(28, 32, 'lisi', '2024-06-20', 25000.00, '银行转账', '民生银行', '消费贷款部分还款', '2024-06-20 10:50:00', '2024-06-20 10:50:00', '已确认'),

-- 案件34的还款记录
(29, 34, 'zhaoliu', '2024-06-22', 35000.00, '现金', '民生银行', '信用卡债务大额还款', '2024-06-22 16:20:00', '2024-06-22 16:20:00', '已确认'),

-- 案件37的还款记录
(30, 37, 'lisi', '2024-06-18', 28000.00, '银行转账', '兴业银行', '科技公司项目回款', '2024-06-18 11:45:00', '2024-06-18 11:45:00', '已确认');

-- 更新锁表的最大ID
UPDATE lock_table SET table_max = 1000 WHERE table_name = 'bank_case';
UPDATE lock_table SET table_max = 5000 WHERE table_name = 'pho_red';
UPDATE lock_table SET table_max = 500 WHERE table_name = 'case_paid';

COMMIT;
