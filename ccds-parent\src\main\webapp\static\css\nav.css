.iconGray {
	vertical-align:middle;
	/*filter:gray;*/
	FILTER: alpha(opacity=70);
	opacity:0.7;
}
.iconOver {
	vertical-align:middle;
}
/*=== 导航 ===*/
#contentbox{
	background:url(../images/nav/normal/nav_back.png) #122b52 repeat-x 0 6px;
	width:100%;
	height:85px;
	text-align:left;
}
/* logo */
#navLogo{
	/*margin-left:10px;*/
	width:650px;
	height:50px;
}

/* 导航菜单 */
#menu {
	position:absolute;
	top:55px;
	margin:0px;
	padding:0px;
	width:100%;
}
#menu table {
	width:100%;
	border:0;
	
}

#funMenus {
	background:url(../images/nav/normal/menu_r_border.gif) right no-repeat;
	padding:0 1px 0 0;
}		
.menuNormal,.menuCur,.menuOver{
	background:url(../images/nav/normal/menu.gif) left no-repeat;
	height:29px;
	width:75px;
	vertical-align:top;
	padding:9px 0 0 0;
	font-size:12px;
	text-align:center;
	cursor:pointer;
	color:#fff;
}

.menuCur{
	background:url(../images/nav/normal/menu_cur.png) no-repeat;
	padding:10px 0 0 0;
}

.menuOver{
	background:url(../images/nav/normal/menu_over.png) no-repeat;
}

/* 用户登录信息 */
#topNotice {
	margin:0px;
	padding:0px;
	display:inline;
	text-align:right;
	/*height:30px;*/
	position:absolute;
	top:20px;
	right:25px;
}
#topNews{
	height:27px; width:400px; padding:0 5px 0 0; font-size:12px;line-height:20px;
}
#topNews span {
	color:#999; font-size:12px;
}
#topNews a:link {
	color:#CCC;
}
#topNews a:visited {
	color:#999;
}
#topNews a:hover, #topNews a:active{
	color:#fff;
}
/* 用户功能菜单 */
#iconMenu {
	/*padding-right:15px;*/
}
#iconMenu span {
	height:30px;
	width:50px;
	text-align:left;
}
#iconMenu img {
	cursor:pointer;
}


/* 欢迎语 */
#welcomeInf {
	text-align:left;
	width:325px;
	padding:2px 16px 0 0;
}
#welcomeInf div {
	padding:2px 8px 2px 8px;
	font-size:12px;
	color:#FFF;
}

/* 最小化导航栏 */
#hideNav{
	position:absolute;
	top:30px;
	right:5px;
	width:50px;
	height:16px;
	padding-top:2px;
	padding-left:5px;
	background-image:url(../images/nav/normal/hideButton.gif);
	background-repeat:no-repeat;
	background-position:right;
	font-size:12px;
	cursor:pointer;
}
#restoreNav {
	margin-top:4px;
	margin-right:15px;
	padding-top:2px;
	padding-right:20px;
	width:50px;
	height:16px;
	background-image:url(../images/nav/normal/showButton.gif);
	background-repeat:no-repeat;
	background-position:right;
	font-size:12px;
	cursor:pointer;
}
.showOver {
	background-color:#fff;
	border:#333333 1px solid;
}


#showNav {
	background:url(../images/nav/normal/nav_bg.gif) repeat-x;
	border-bottom:#333333 1px solid;
	height:25px;
	width:100%;
	text-align:left;
}
