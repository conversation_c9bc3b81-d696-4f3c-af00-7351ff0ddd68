/*
	打开选择商品窗口
*/
function forwardToChoose(n,code){
	switch(n){
		//选择商品(所有商品)
		case 1:
			window.open("prodAction.do?op=searchProType&wm=wm", "选择商品",
				"height=500, width=800, top=0, left=0, toolbar=no, menubar=no, scrollbars=yes, resizable=no,location=no, status=no");
			break;
		//选择人员
		case 2:
			window.open("empAction.do?op=selEmp&ch=ch", "选择人员",
			 "height=500, width=800, top=0, left=0, toolbar=no, menubar=no, scrollbars=yes, resizable=no,location=no, status=no");
			break;
		//选择账号
		case 3:
			window.open("empAction.do?op=searchUser", "选择账号",
	 "height=500, width=800, top=0, left=0, toolbar=no, menubar=no, scrollbars=yes, resizable=no,location=no, status=no");
			break;
		//选择某个仓库的商品
		case 4:
			window.open("wwoAction.do?op=stroSearch&wmsCode="+code, "选择商品",
	 "height=500, width=800, top=0, left=0, toolbar=no, menubar=no, scrollbars=yes, resizable=no,location=no, status=no");
			break;
		//选择合同
		case 5:
			window.open("orderAction.do?op=searchOrderType", "选择合同",
	 "height=500, width=800, top=0, left=0, toolbar=no, menubar=no, scrollbars=yes, resizable=no,location=no, status=no");
			break;
	}
	
}	

/* 
	选择商品完成传值 
	cusCode:商品编号
	cusName:商品名称名称
*/
function choosePro(n,code,name){
	switch(n){
		//选择商品
		case 1:
			try{
				window.opener.document.getElementById("wprId").value=code;
				window.opener.document.getElementById("wprName").value=name;
			}catch(error){}
			window.close();
			break;
		//选择人员	
		case 2:
			try{
			 	window.opener.document.getElementById("seNo").value=code;
   			 	window.opener.document.getElementById("seName").value=name;
			}catch(error){}
    		window.close();
			break;
		//选择账号
		case 3:
			try{
			 	window.opener.document.getElementById("uc").value=code;
   			 	window.opener.document.getElementById("ul").value=name;
			}catch(error){}
			window.close();
			break;
		//选择合同
		case 4:
			try{
			 	window.opener.document.getElementById("sodCode").value=code;
    			window.opener.document.getElementById("sodTil").value=name;
			}catch(error){}
    		window.close();
			break;
	}
}

/* 
	选择完成传值 
	wprCode:商品编号
	wprName:商品名称名称
	rspProNum:库存数量
*/
function chooseStroPro(wprCode,wprName,wprModel,rspProNum){
	if(wprModel!=''){
		wprName += '/' + wprModel;
	}
	try{
		window.opener.document.getElementById("wprCode").value=wprCode;
		window.opener.document.getElementById("wprName").value=wprName;
		window.opener.document.getElementById("rspProNum").innerText=rspProNum;
		if(window.opener.document.getElementById("num")!=null){
			window.opener.document.getElementById("num").value=rspProNum.replace(/,/g,"");
		}
	}catch(error){}
    window.close();
}
/*
	增减表格行
	n:类型（1：入库，2：合同,3：采购单）
	tabId:表格Id
	wprId:商品Id
	wprName:商品名和商品编号数组
	可选参数:wprModel,typName,price - 型号，单位，价格
*/
function tbladdrow(n,tabId,wprId,wprName,wprModel,typName,price) { 
	var tableEl=parent.document.getElementById(tabId);
	var row =tableEl.insertRow(tableEl.rows.length); 
	var i = tableEl.rows.length - 2; 
	var proCode=parent.document.getElementsByName("wprId");
	for(var i=0; i<proCode.length;i++){
		if(proCode[i].value==wprId){
			alert("您已经添加过 "+wprName[0]+" 这个商品！")
			return ;
		}
	}
  	switch(n){
		//入库明细
		case 1:
			var wprNameStr = wprName[0];
			if(wprModel!=null&&wprModel!=""){
				wprNameStr+="/"+wprModel;
			}
			
			var col = row.insertCell(0);
			col.innerHTML = wprNameStr;
			col = row.insertCell(1);
			col.innerHTML = wprName[1]+"&nbsp;";
			col = row.insertCell(2); 
			col.innerHTML = "<input class='inputSize2' style='width:95%' type='text'  name='"+wprId+"num' onKeyUp='checkIsNum(this)' onblur='checkIsNum(this)'/>&nbsp;"; 
			col = row.insertCell(3); 
			col.innerHTML = typName+"&nbsp;";
			col = row.insertCell(4);
			col.innerHTML = "&nbsp;<textarea rows='1' name='"+wprId+"remark' style='width:95%' onblur='autoShort(this,500)'></textarea>&nbsp;"; 
			col = row.insertCell(5); 
			col.innerHTML = "&nbsp;<img src='images/content/del.gif' onClick='delTable(this,\""+tabId+"\")' alt='删除' style='cursor:pointer'/>&nbsp;<div style='display:none'><INPUT type='checkbox' name='wprId' checked='checked' value="+wprId+"></div>"; 
			col.style.borderRight="0px";
			break;
		
		//合同明细
		case 2:
			var num=1;
			var zk=100;
			var allPrice=(price*num*zk)/100;
			var wprNameStr = wprName[0];
			if(wprModel!=null&&wprModel!=""){
				wprNameStr+="/"+wprModel;
			}
			
			var col = row.insertCell(0);
			col.innerHTML = "<INPUT type='hidden' id='pro"+wprId+"' value="+wprId+" >"+wprNameStr; 
			col = row.insertCell(1); 
			col.innerHTML =wprName[1]+"&nbsp;";;
			col = row.insertCell(2); 
			col.innerHTML = "<INPUT type='text' class='inputSize2 ' style='width:95%;' name='price"+wprId+"' value="+price+" onKeyUp='changePrice("+wprId+",this,1)' onblur='changePrice("+wprId+",this,1)'>"; 
			col = row.insertCell(3); 
			col.innerHTML = "<INPUT type='text' class='inputSize2 ' style='width:95%;' name='num"+wprId+"' value="+num+" onKeyUp='changePrice("+wprId+",this,0)'>"; 
			col = row.insertCell(4);
			col.innerHTML = typName+"&nbsp;";
			//col = row.insertCell(5); 
			//col.innerHTML = "<INPUT type='text' class='inputSize2 inputBoxAlign' style='width:50px' name='zk"+wprId+"' value="+zk+" onKeyUp='changePrice("+wprId+",this,0)' >%";
			col = row.insertCell(5); 
			col.innerHTML = "<INPUT type='text' class='inputSize2' style='width:95%' name='allPrice"+wprId+"' value="+allPrice+" onKeyUp='changeTotal("+wprId+",this,1)' >";  
			col = row.insertCell(6); 
			col.innerHTML = "<textarea rows='1' name='remark"+wprId+"' style='width:95%;' onblur='autoShort(this,500)'></textarea>"; 
			col = row.insertCell(7); 
			col.innerHTML = "&nbsp;&nbsp;<img src='images/content/del.gif' onClick='delTable(this,\""+tabId+"\")' alt='删除' style='cursor:pointer'/>"+
							"<div style='display:none'><INPUT type='checkbox' name='wprId' checked='checked' value="+wprId+"></div>&nbsp;"; 
			col.style.borderRight="0px";
			break;
			//采购明细
		case 3:
			var num=1;
			var allPrice=price*num;
			var col = row.insertCell(0);
			
			if(wprModel!=null&&wprModel!=""){
				wprName+="/"+wprModel;
			}
			col.innerHTML = "<INPUT type='hidden' id='pro"+wprId+"' value="+wprId+" >"+wprName; 
			//赋值
			col = row.insertCell(1); 
			col.innerHTML = "<INPUT type='text' class='inputSize2' style='width:95%;' name='price"+wprId+"' value="+price+" onKeyUp='changePrice("+wprId+",this,1)' onblur='changePrice("+wprId+",this,1)'>"; 
			col = row.insertCell(2); 
			col.innerHTML = "<INPUT type='text' class='inputSize2 inputBoxAlign' style='width:60px' name='num"+wprId+"' value="+num+" onKeyUp='changePrice("+wprId+",this,0)'>"; 
			col = row.insertCell(3);
			col.innerHTML = typName+"&nbsp;";
			col = row.insertCell(4); 
			col.innerHTML = "<INPUT type='text' class='inputSize2' style='width:95%' name='allPrice"+wprId+"' value="+allPrice+" onKeyUp='changeTotal("+wprId+",this,1)' >";  
			col = row.insertCell(5); 
			col.innerHTML = "<textarea rows='1' name='remark"+wprId+"' style='width:95%;' onblur='autoShort(this,500)'></textarea>"; 
			col = row.insertCell(6); 
			col.innerHTML = "<img src='images/content/del.gif' onClick='delTable(this,\""+tabId+"\")' alt='删除' style='cursor:pointer'/>"; 
			col = row.insertCell(7); 
			col.innerHTML = "<div style='display:none'><INPUT type='checkbox' name='wprId' checked='checked' value="+wprId+"></div>"; 
			col.style.borderRight="0px";
			break;
	}
} 
function delTable(r,idName){
	var tableEl=parent.document.getElementById(idName);
	var i=r.parentNode.parentNode.rowIndex;
	tableEl.deleteRow(i);
	/*if(confirm("确定删除这条数据吗?")){
			
	}*/
}
