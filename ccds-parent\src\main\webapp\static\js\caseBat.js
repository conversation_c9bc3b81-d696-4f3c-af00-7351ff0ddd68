/* 
	新建、编辑弹出层
*/
function batPopDiv(n,arg){
	var width;
	var height;
	var idPre="";
	var titleTxt="";
	var tips="（带<span class='red'>*</span>为必填字段）";
	var url;			
	switch(n){
		case 1:
			height=310;
			titleTxt="添加案件批次"+tips;
			url="caseBatAction.do?op=toNewCaseBat";
			break;
		case 11:
			height = 350;
			titleTxt="编辑案件批次"+tips;
			url="caseBatAction.do?op=toUpdBat&cbatId="+arg;
			break;
		case 12:
			height = 200;
			titleTxt="更新分配提示"+tips;
			url="caseBatAction.do?op=toUpdBatTips&batId="+arg;
			break;
		case 2:
			height=390;
			width=360;
			titleTxt="导入案件";
			url="caseAction.do?op=toImportCases&batId="+arg;
			break;
		case 3:
			height=150;
			titleTxt = "导出批次催收记录";
			//url="caseAction.do?op=toBatOut&cbatId="+arg;
			url="caseAction.do?op=toExpCollRec&cbatId="+arg+"&expType=caseBat";
			break;
		case 4:
			height=270;
			titleTxt="选择导出字段";
			url="caseBatAction.do?op=toOutBat&type="+arg;	
			break;
		
		case 52:
			height=210;
			width=360;
			titleTxt="导入案件档案号";
			url="caseAction.do?op=toImportFileNO";
			break;
		case 53:
			height=210;
			width=360;
			titleTxt="导入案件利息";
			url="caseAction.do?op=toImportCaseInt";
			break;
		case 54:
			height=210;
			width=360;
			titleTxt="更新委案金额";
			url="caseAction.do?op=toImportCaseM";
			break;
		case 57:
			height=210;
			width=360;
			titleTxt="导入案件评语";
			url="caseAction.do?op=toImportCaseComment";
			break;
		case 58:
			height=210;
			width=360;
			titleTxt="导入案件电话";
			url="caseAction.do?op=toImportCasePhone";
			break;
		case 59:
			height=210;
			width=360;
			titleTxt="导入催收记录";
			url="caseAction.do?op=toImportPhoRed";
			break;
		case 510:
			height=210;
			width=360;
			titleTxt="导入更新案件";
			url="caseAction.do?op=toImportUpdCase";
			break;
		case 511:
			height=210;
			width=360;
			titleTxt="导入催收建议";
			url="caseAction.do?op=toImportCaseAdv";
			break;
		case 512:
			height=210;
			width=360;
			titleTxt="导入案件地址";
			url="caseAction.do?op=toImportCaseAddress";
			break;
		/*case 512:
			height=210;
			width=360;
			titleTxt="导入委款金额";
			url="caseAction.do?op=toImportCaseDebt";
			break;*/
	}
	createPopWindow(idPre,titleTxt,url,width,height);		
}
/* 
	删除弹出确认层
	n:删除类型，在delConfirm方法中的对应序号
*/
function batDelDiv(type,id){
	var titleTxt="";
	switch(type){
		case 1:
			titleTxt="删除案件批次";
			break;
		case 2:
			titleTxt="归并案件";
			break;
	}
	createCommonConfirmWindow(titleTxt,id,type);
}
//返回实体名称，url数组
function getConObj(type,id){
	var confirmObj = new Array();
	switch(parseInt(type)){
		case 1:
			confirmObj[0]="确认删除该案件批次？";
			confirmObj[1]="caseBatAction.do?op=delBat&cbatId="+id;
			break;
		case 2:
			confirmObj[0]="确认归并该批次案件？";
			confirmObj[1]="caseAction.do?op=setCaseCollection&cbatId="+id;
			break;
	}
	return confirmObj;
}

/*
	批量退案弹出层
*/
function batBackConf(n,batId,batCode){
    var width=230;
	var height=120;
	var idPre="";
	var titleTxt="";
	var url;
	switch(n){
		case 1:
			titleTxt="确定退案";
			url="caseBatAction.do?op=toBackCase&batId="+batId+"&batCode="+batCode+"&isBack=1";
			break;
		case 2:
			titleTxt="确定恢复案件";
			url="caseBatAction.do?op=toBackCase&batId="+batId+"&batCode="+batCode+"&isBack=0";
			break;
	}
	createPopWindow(idPre,titleTxt,url,width,height);
}

//生成案件导入列表中xls
function createXlsToDL(xlsPath,fileName){
	var fileHTML = "<a href='downloadAction.do?op=getStreamInfo&fileName="+fileName+"&path="+encodeURIComponent(xlsPath)+"'>"+xlsPath+"</a>&nbsp;";
	return fileHTML;
}