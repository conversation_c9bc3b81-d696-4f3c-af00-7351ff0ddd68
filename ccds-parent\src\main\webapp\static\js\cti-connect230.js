function connectCTI(phone){
	createProgressBar();
	var url = "connectCTI.do?op=connectToJust";
	var pars = [];
	pars.phone = phone;
	pars.serverIP = "***************:6083";
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var rs = response.responseText;
			if(rs.indexOf("|")!=-1){
				var rsArray = rs.split("|");
				if(rsArray[0]=="0"){
					alert("拨号成功！");
					if($("callId")!=null){
						$("callId").value=rsArray[1];
					}
					else if(parent.document.getElementById("callId")!=null){
						parent.document.getElementById("callId").value=rsArray[1];
					}
				}
			}
			else{
				alert("拨号失败！"+rs);
			}
			closeProgressBar();
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}

function getRec(callId){
	var url = "connectCTI.do?op=getRecordOfJust";
	var pars = [];
	pars.callId = callId;
	pars.serverIP = "***************:6083";
	pars.recServerIP = "************";
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var rs = response.responseText;
			if(rs.indexOf("|")!=-1){
				var rsArray = rs.split("|");
				if(rsArray[0]=="0"){
					self.location.href=rsArray[1];
				}
			}
			else{
				$("errMsgLayer").innerHTML=rs;
				$("errMsgLayer").show();
			}
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}