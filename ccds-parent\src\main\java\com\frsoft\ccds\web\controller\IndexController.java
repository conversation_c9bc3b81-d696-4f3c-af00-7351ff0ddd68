package com.frsoft.ccds.web.controller;

import com.frsoft.ccds.common.core.controller.BaseController;
import com.frsoft.ccds.common.core.domain.AjaxResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 首页控制器
 */
@RestController
public class IndexController extends BaseController {

    /**
     * 首页
     */
    @GetMapping("/api")
    public AjaxResult index() {
        Map<String, Object> data = new HashMap<>();
        data.put("title", "CCDS催收系统");
        data.put("version", "2.0.0");
        data.put("description", "基于Spring Boot重构的催收管理系统");
        return success(data);
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public AjaxResult health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", System.currentTimeMillis());
        return success(data);
    }
}
