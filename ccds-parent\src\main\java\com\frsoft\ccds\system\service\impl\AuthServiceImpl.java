package com.frsoft.ccds.system.service.impl;

import com.frsoft.ccds.common.core.domain.AjaxResult;
import com.frsoft.ccds.common.core.domain.model.LoginUser;
import com.frsoft.ccds.common.utils.StringUtils;
import com.frsoft.ccds.system.domain.SysUser;
import com.frsoft.ccds.system.service.IAuthService;
import com.frsoft.ccds.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 认证服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class AuthServiceImpl implements IAuthService {
    
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    // 简单的内存token存储，生产环境建议使用Redis
    private final Map<String, LoginUser> tokenStore = new ConcurrentHashMap<>();
    
    // 当前登录用户的ThreadLocal
    private final ThreadLocal<LoginUser> currentUserHolder = new ThreadLocal<>();
    
    @Override
    public AjaxResult login(String username, String password) {
        try {
            // 参数验证
            if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
                return AjaxResult.error("用户名或密码不能为空");
            }
            
            // 查询用户
            SysUser user = userService.selectUserByLoginName(username);
            if (user == null) {
                return AjaxResult.error("用户不存在");
            }
            
            // 检查用户状态
            if (!"1".equals(user.getEnabled())) {
                return AjaxResult.error("用户已被禁用");
            }

            // 验证密码
            if (!verifyPassword(password, user.getPassword())) {
                return AjaxResult.error("密码错误");
            }

            // 生成token
            String token = generateToken();

            // 创建登录用户信息
            LoginUser loginUser = new LoginUser();
            loginUser.setToken(token);
            loginUser.setUserCode(user.getUserCode());
            loginUser.setUsername(user.getLoginName());
            loginUser.setPassword(user.getPassword());
            loginUser.setStatus("0"); // 正常状态
            loginUser.setLoginTime(System.currentTimeMillis());
            loginUser.setExpireTime(System.currentTimeMillis() + 24 * 60 * 60 * 1000); // 24小时过期
            
            // 存储token
            tokenStore.put(token, loginUser);
            
            // 返回登录信息
            Map<String, Object> data = new HashMap<>();
            data.put("token", token);
            data.put("user", user);
            
            return AjaxResult.success("登录成功", data);
            
        } catch (Exception e) {
            return AjaxResult.error("登录失败：" + e.getMessage());
        }
    }
    
    @Override
    public AjaxResult logout(String token) {
        if (StringUtils.isNotEmpty(token)) {
            tokenStore.remove(token);
            currentUserHolder.remove();
        }
        return AjaxResult.success("登出成功");
    }
    
    @Override
    public LoginUser getCurrentUser() {
        return currentUserHolder.get();
    }
    
    @Override
    public AjaxResult refreshToken(String token) {
        LoginUser loginUser = tokenStore.get(token);
        if (loginUser == null) {
            return AjaxResult.error("无效的令牌");
        }
        
        // 生成新token
        String newToken = generateToken();
        loginUser.setToken(newToken);
        loginUser.setExpireTime(System.currentTimeMillis() + 24 * 60 * 60 * 1000);
        
        // 移除旧token，存储新token
        tokenStore.remove(token);
        tokenStore.put(newToken, loginUser);
        
        Map<String, Object> data = new HashMap<>();
        data.put("token", newToken);
        
        return AjaxResult.success("令牌刷新成功", data);
    }
    
    @Override
    public boolean validateToken(String token) {
        if (StringUtils.isEmpty(token)) {
            return false;
        }
        
        LoginUser loginUser = tokenStore.get(token);
        if (loginUser == null) {
            return false;
        }
        
        // 检查是否过期
        if (System.currentTimeMillis() > loginUser.getExpireTime()) {
            tokenStore.remove(token);
            return false;
        }
        
        // 设置当前用户
        currentUserHolder.set(loginUser);
        return true;
    }
    
    @Override
    public AjaxResult changePassword(String oldPassword, String newPassword) {
        LoginUser currentUser = getCurrentUser();
        if (currentUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        // 获取当前用户信息
        SysUser user = userService.selectUserByUserCode(currentUser.getUserCode());
        if (user == null) {
            return AjaxResult.error("用户不存在");
        }

        // 验证旧密码
        if (!verifyPassword(oldPassword, user.getPassword())) {
            return AjaxResult.error("原密码错误");
        }

        // 更新密码
        String encodedPassword = encodePassword(newPassword);
        int result = userService.resetUserPassword(user.getUserCode(), encodedPassword);
        
        if (result > 0) {
            return AjaxResult.success("密码修改成功");
        } else {
            return AjaxResult.error("密码修改失败");
        }
    }
    
    /**
     * 生成token
     */
    private String generateToken() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 验证密码
     */
    private boolean verifyPassword(String rawPassword, String encodedPassword) {
        // 这里使用MD5验证，实际项目中建议使用BCrypt
        return md5(rawPassword).equals(encodedPassword);
    }
    
    /**
     * 加密密码
     */
    private String encodePassword(String rawPassword) {
        return md5(rawPassword);
    }
    
    /**
     * MD5加密
     */
    private String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5加密失败", e);
        }
    }
}
