function execSend(){
	if($("msgContent").value!=""){
		waitSubmit("save");
		waitSubmit("doCancel");
		var url="connectSMS.do";
		$("op").value="connectToCR";
		var args= $("sendForm").serialize(true);
		args.user="18060528130";
		args.pwd="C402F3207C6B1FD1EEE6E0308A3F";
		args.sign="法务部";
		new Ajax.Request(url,{
			method		:	'post',
			parameters	:	args,
			onSuccess	:	function(response){
				var rs = response.responseText;
				var rsMsg = getErrMsg(rs);
				$("rsLayer").innerHTML = rsMsg+"<div class='deepBlue'>[<a href='javascript:void(0)' onclick='cancel()'>请点此关闭</a>]</div>";
				$("rsLayer").show();
				//setTimeout("cancel()",3000);
			},
			onfailure 	: 	function(response){
				if (transport.status == 404)
					alert("您访问的url地址不存在！");
				else
					alert("Error: status code is " + transport.status);
			}
		});
	}
	else{
		alert("未填写短信内容！");
	}
}


function getErrMsg(msg){
	var rsArr = msg.split(',');
	var rsCode = rsArr[0];
	var rtMsg = rsArr[1];
	switch(rsCode){
	case '0': rtMsg = "发送成功"; break;
	case '1': rtMsg = "含有敏感词汇"; break;
	case '2': rtMsg = "余额不足"; break;
	case '3': rtMsg = "没有号码"; break;
	case '4': rtMsg = "包含sql语句"; break;
	case '10': rtMsg = "账号不存在"; break;
	case '11': rtMsg = "账号注销"; break;
	case '12': rtMsg = "账号停用"; break;
	case '13': rtMsg = "IP鉴权失败"; break;
	case '14': rtMsg = "格式错误"; break;
	case '-1': rtMsg = "系统异常"; break;
	}
	return rtMsg;
}