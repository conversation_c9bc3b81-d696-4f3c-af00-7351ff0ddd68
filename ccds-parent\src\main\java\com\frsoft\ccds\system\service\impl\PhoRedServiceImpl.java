package com.frsoft.ccds.system.service.impl;

import com.frsoft.ccds.system.domain.PhoRed;
import com.frsoft.ccds.system.mapper.PhoRedMapper;
import com.frsoft.ccds.system.service.IPhoRedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 催收记录Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PhoRedServiceImpl implements IPhoRedService {
    
    @Autowired
    private PhoRedMapper phoRedMapper;
    
    /**
     * 查询催收记录
     * 
     * @param prId 催收记录主键
     * @return 催收记录
     */
    @Override
    public PhoRed selectPhoRedByPrId(Long prId) {
        return phoRedMapper.selectPhoRedByPrId(prId);
    }
    
    /**
     * 查询催收记录列表
     * 
     * @param phoRed 催收记录
     * @return 催收记录
     */
    @Override
    public List<PhoRed> selectPhoRedList(PhoRed phoRed) {
        return phoRedMapper.selectPhoRedList(phoRed);
    }
    
    /**
     * 新增催收记录
     * 
     * @param phoRed 催收记录
     * @return 结果
     */
    @Override
    public int insertPhoRed(PhoRed phoRed) {
        return phoRedMapper.insertPhoRed(phoRed);
    }
    
    /**
     * 修改催收记录
     * 
     * @param phoRed 催收记录
     * @return 结果
     */
    @Override
    public int updatePhoRed(PhoRed phoRed) {
        return phoRedMapper.updatePhoRed(phoRed);
    }
    
    /**
     * 批量删除催收记录
     * 
     * @param prIds 需要删除的催收记录主键
     * @return 结果
     */
    @Override
    public int deletePhoRedByPrIds(Long[] prIds) {
        return phoRedMapper.deletePhoRedByPrIds(prIds);
    }
    
    /**
     * 删除催收记录信息
     * 
     * @param prId 催收记录主键
     * @return 结果
     */
    @Override
    public int deletePhoRedByPrId(Long prId) {
        return phoRedMapper.deletePhoRedByPrId(prId);
    }
    
    /**
     * 根据案件ID查询催收记录列表
     * 
     * @param casId 案件ID
     * @return 催收记录列表
     */
    @Override
    public List<PhoRed> selectPhoRedListByCasId(Long casId) {
        return phoRedMapper.selectPhoRedListByCasId(casId);
    }
    
    /**
     * 根据员工编号查询催收记录列表
     * 
     * @param seNo 员工编号
     * @return 催收记录列表
     */
    @Override
    public List<PhoRed> selectPhoRedListBySeNo(Long seNo) {
        return phoRedMapper.selectPhoRedListBySeNo(seNo);
    }
    
    /**
     * 统计催收记录总数
     * 
     * @param phoRed 查询条件
     * @return 催收记录总数
     */
    @Override
    public int countPhoRed(PhoRed phoRed) {
        return phoRedMapper.countPhoRed(phoRed);
    }
}
