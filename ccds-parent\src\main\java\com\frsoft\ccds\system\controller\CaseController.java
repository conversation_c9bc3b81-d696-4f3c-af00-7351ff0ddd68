package com.frsoft.ccds.system.controller;

import com.frsoft.ccds.common.core.controller.BaseController;
import com.frsoft.ccds.common.core.domain.AjaxResult;
import com.frsoft.ccds.system.domain.BankCase;
import com.frsoft.ccds.system.service.IBankCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpSession;
import java.util.List;

/**
 * 案件管理控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/case")
public class CaseController extends BaseController {
    
    @Autowired
    private IBankCaseService bankCaseService;
    
    /**
     * 案件列表页面
     */
    @GetMapping("/list")
    public String list(Model model, HttpSession session) {
        // 检查用户是否已登录（暂时跳过，因为Security已禁用）
        // Object user = session.getAttribute("user");
        // if (user == null) {
        //     return "redirect:/login";
        // }
        
        return "case/listRecord";
    }
    
    /**
     * 简单案件列表页面
     */
    @GetMapping("/simple-list")
    public String simpleList(Model model, HttpSession session) {
        return "case/simple-listRecord";
    }
    
    /**
     * 获取案件列表数据
     */
    @GetMapping("/data")
    @ResponseBody
    public AjaxResult getCaseData() {
        try {
            List<BankCase> caseList = bankCaseService.selectBankCaseList(new BankCase());
            return AjaxResult.success("查询成功", caseList);
        } catch (Exception e) {
            logger.error("查询案件列表异常", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取案件统计信息
     */
    @GetMapping("/stats")
    @ResponseBody
    public AjaxResult getCaseStats() {
        try {
            List<BankCase> allCases = bankCaseService.selectBankCaseList(new BankCase());
            
            int totalCases = allCases.size();
            double totalAmount = allCases.stream()
                .mapToDouble(c -> c.getCasM() != null ? c.getCasM().doubleValue() : 0.0)
                .sum();
            double totalPaid = allCases.stream()
                .mapToDouble(c -> c.getCasPaidM() != null ? c.getCasPaidM().doubleValue() : 0.0)
                .sum();
            
            AjaxResult result = AjaxResult.success("统计成功");
            result.put("totalCases", totalCases);
            result.put("totalAmount", totalAmount);
            result.put("totalPaid", totalPaid);
            result.put("remainingAmount", totalAmount - totalPaid);
            result.put("recoveryRate", totalAmount > 0 ? (totalPaid / totalAmount) * 100 : 0);
            
            return result;
        } catch (Exception e) {
            logger.error("查询案件统计异常", e);
            return AjaxResult.error("统计失败：" + e.getMessage());
        }
    }
}
