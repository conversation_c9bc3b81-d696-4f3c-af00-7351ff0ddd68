-- 简单的测试数据导入脚本
USE ccds;

-- 插入测试案件数据
INSERT IGNORE INTO bank_case (
    cas_code, cas_name, cas_ca_cd, cas_m, cas_state,
    cas_ins_time, cas_card_bank, cas_hom_pho, cas_mob_pho
) VALUES
('BC001', '<PERSON>', '320101199001011234', 50000.00, 1, NOW(), 'Bank of China', '025-********', '***********'),
('BC002', '<PERSON> Si', '320101199002021234', 80000.00, 1, NOW(), 'ICBC', '025-********', '***********'),
('BC003', '<PERSON>', '320101199003031234', 120000.00, 1, NOW(), 'CCB', '025-********', '***********'),
('BC004', '<PERSON>', '320101199004041234', 95000.00, 1, NOW(), 'ABC', '025-********', '***********'),
('BC005', '<PERSON>', '320101199005051234', 75000.00, 1, NOW(), 'BOCOM', '025-********', '***********');

-- 插入测试催收记录数据
INSERT IGNORE INTO pho_red (
    pr_cas_id, pr_content, pr_time, pr_contact, pr_con_type
) VALUES
(1, 'Contact debtor about payment plan - Promise to pay by month end', NOW(), '***********', 'Phone Call'),
(2, 'Multiple calls no answer - Unable to contact', NOW(), '***********', 'Phone Call'),
(3, 'Visit debtor residence - Not home, neighbor received notice', NOW(), '***********', 'Home Visit'),
(4, 'Discussed payment options - Agreed to partial payment', NOW(), '***********', 'Phone Call'),
(5, 'Follow up on previous promise - Requested extension', NOW(), '***********', 'Phone Call');

-- 插入测试还款记录
INSERT IGNORE INTO case_paid (
    pa_cas_id, pa_paid_num, pa_paid_time, pa_writer, pa_sur_remark
) VALUES
(1, 10000.00, NOW(), 'admin', 'Partial payment received'),
(4, 25000.00, NOW(), 'admin', 'Cash payment at office');

-- 更新案件状态
UPDATE bank_case SET cas_state = 2 WHERE cas_id IN (1, 4);

COMMIT;
