var MBGPS_DF_POINT_LNG = 116.331398;//默认地图中心点精度
var MBGPS_DF_POINT_LAT = 39.897445;//默认地图中心点维度
var MBGPS_DF_ZOOM = 15;//默认放大级别

var GPS_POINTS = [];
var GPS_POINTS_INDEX = 0;

function getGpsLine(){
	if($("macAddrStr").value == "") {
		alert("未选择外访设备");
		return false;
	}
	createProgressBar();
	MAP_G.clearOverlays();//清空覆盖物
	var url = "mobGpsAction.do";
	var para = $("queryForm").serialize(true);
	para.op = "listMobGps";
	new Ajax.Request(url,{
		method : 'post',
		parameters : para,
		onSuccess : function(response){
			var jsonData = response.responseText;
			if(jsonData.indexOf("error") == -1){
				if(jsonData!=""){
					jsonData = response.responseText.evalJSON();
					var listObj = jsonData.list;
					if(listObj!=undefined && listObj!=""){
						var lastPoint = null;
						var gpsPoints = new Array(); 
						GPS_POINTS = [];//初始化
						GPS_POINTS_INDEX = 0;
						for(var i=0; i<listObj.length; i++){
							var obj = listObj[i];
							var point = new BMap.Point(obj.mgpsLongitude, obj.mgpsLatitude);
							GPS_POINTS.push(point);
//							gpsPoints.push(point);
							/*if(i == listObj.length-1) {
								setTimeout(function(){
									convertor.translate(gpsPoints, 1, 5, drawGpsLine);
								   }, 500);
							    var lastPointArr = [];
							    lastPointArr.push(point);
							    setTimeout(function(){
							    convertor.translate(lastPointArr, 1, 5, drawLastPoint);
							    }, 500);
							}
							else {
								if(gpsPoints.length == 5) {
									setTimeout(function(){
										 convertor.translate(gpsPoints, 1, 5, drawGpsLine);//5个点输出线条
									    }, 500);
									gpsPoints.length = 0;//清空数组
									gpsPoints.push(point);//加入起始点连接上一个折线
								}
							}*/
						}
						translateGpsPoints();
					}
					else {
						alert("此日期无轨迹记录");
					}
				}
			}
			else {
				errInfoCallBack(jsonData);
			}
			closeProgressBar();
		},
		onfailure : function(response){
			closeProgressBar();
			if (transport.status == 404) { alert("您访问的url地址不存在！"); }
			else { alert("Error: status code is " + transport.status); }
		}
	});
}

function translateGpsPoints() {
	if(GPS_POINTS_INDEX > -1) {
		var convertor = new BMap.Convertor();
		var startIndex = GPS_POINTS_INDEX;
		var endIndex = GPS_POINTS_INDEX + 10;
		if(endIndex > GPS_POINTS.length) {//数组遍历结束
			endIndex = GPS_POINTS.length;
			GPS_POINTS_INDEX = -1;
			var lastPointArr = [];
			lastPointArr.push(GPS_POINTS[GPS_POINTS.length-1]);
			setTimeout(function(){
			    convertor.translate(lastPointArr, 1, 5, drawLastPoint);
			}, 500);
		}
		else {
			GPS_POINTS_INDEX = endIndex - 1;//更新数组下标
		}
		convertor.translate(GPS_POINTS.slice(startIndex, endIndex), 1, 5, drawGpsLine);
	}
}
/** 加载外访设备 **/
function cleanMacAddrInput(){
	$('macAddrStr').value='';
	$('mdvId').value = '';
	restoreFromLock('macAddrStr');
}
function setMacAddrInput(mdvId, macAddr){
	$('mdvId').value = mdvId;
	$('macAddrStr').value=macAddr;
	setLockInput('macAddrStr');
}
function setMobDeviceListItem(jsonData){
	var listHTML = [];
	if(jsonData!=""&&jsonData.list&&jsonData.list.length>0){
		var listObj = jsonData.list;
		var pageObj = jsonData.page;
		listHTML.push("<ul>");
		for(var i=0; i<listObj.length; i++){
			var obj = listObj[i];
			listHTML.push("<li><a href=\"javascript:void(0)\" onclick=\"setMacAddrInput('"+obj.mdvId+"','"+encodeString(obj.mdvMacAddr)+"');return false;\">"
					+ (obj.mdvLimUser ? encodeString(obj.mdvLimUser.userSeName) + "&nbsp;" : "") +encodeString(obj.mdvMacAddr)+"</a></li>");
		}
		listHTML.push("</ul>");
	}
	return listHTML.join('');
}
function showMobDeviceList(){
	loadMobDeviceListList();
	if($('mobDeviceListLayer')!=null){
		floatTipsLayer('mobDeviceListLayer',-80,10);
	}
}
function loadMobDeviceListList(pageNo){
	showAjaxList('选择外访设备','mobDeviceListLayer','150px','mobDeviceAction.do','op=listAllMobDevice',setMobDeviceListItem,'',-80,10,'loadMobDeviceListList');
}

/**
 * 初始化地图页面（使用浏览器定位）
 */
function initMap() {
	var geolocation = new BMap.Geolocation();
	geolocation.getCurrentPosition(function(r){
		if(this.getStatus() == BMAP_STATUS_SUCCESS){
			MAP_G.centerAndZoom(r.point, MBGPS_DF_ZOOM);
		}
		else {
			MAP_G.centerAndZoom(new BMap.Point(MBGPS_DF_POINT_LNG, MBGPS_DF_POINT_LAT), MBGPS_DF_ZOOM);
		}
	},{enableHighAccuracy: true})
	MAP_G.enableScrollWheelZoom(true);
	if($("macAddrStr").value != "") {//设备列表查询轨迹，默认加载
		getGpsLine();
	}
}

/**
 * 最近一次轨迹点
 * @param lastPoint 当前(最近一次上传)轨迹点
 */
function drawLastPoint(lastPoint) {
	if(lastPoint.status === 0) {
//		for (var i = 0; i < lastPoint.points.length; i++) {
			//当前(最近一次上传)位置
		    var lastMarker = new BMap.Marker(lastPoint.points[0]);
		    lastMarker.setAnimation(BMAP_ANIMATION_BOUNCE); 
		    MAP_G.addOverlay(lastMarker);            //增加点
			MAP_G.centerAndZoom(lastPoint.points[0], MBGPS_DF_ZOOM);
//	    }
		
	}
    
}
/**
 * 绘制轨迹
 * @param allPoints	所有轨迹点
 */
function drawGpsLine(allPoints){
	if(allPoints.status === 0) {
		var polyline = new BMap.Polyline(allPoints.points, {strokeColor:"red", strokeWeight:4, strokeOpacity:0.7});   //创建折线
	    MAP_G.addOverlay(polyline);          //增加折线
    }
	translateGpsPoints();
}

/*
function drawCheckinPoint() {
	 var mouseoverTxt = "<br/>" + parseInt(Math.random() * 1000,10) + "套" ;
    
    var myCompOverlay = new ComplexCustomOverlay(new BMap.Point(116.405, 39.920), "银湖海岸城",mouseoverTxt);
    MAP_G.addOverlay(myCompOverlay);
}*/


/** 复杂的自定义覆盖物 **/
function ComplexCustomOverlay(point, text, mouseoverText){
  this._point = point;
  this._text = text;
  this._overText = mouseoverText;
}
ComplexCustomOverlay.prototype = new BMap.Overlay();
ComplexCustomOverlay.prototype.initialize = function(MAP_G){
  this._map = MAP_G;
  var div = this._div = document.createElement("div");
  div.style.position = "absolute";
  div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
  div.style.backgroundColor = "#EE5D5B";
  div.style.border = "1px solid #BC3B3A";
  div.style.color = "white";
  div.style.height = "auto";
  div.style.padding = "2px";
  div.style.lineHeight = "18px";
  div.style.whiteSpace = "nowrap";
  div.style.MozUserSelect = "none";
  div.style.fontSize = "12px"
  var span = this._span = document.createElement("span");
  div.appendChild(span);
  span.appendChild(document.createTextNode(this._text));      
  var that = this;

  var arrow = this._arrow = document.createElement("div");
  arrow.style.background = "url(http://map.baidu.com/fwmap/upload/r/map/fwmap/static/house/images/label.png) no-repeat";
  arrow.style.position = "absolute";
  arrow.style.width = "11px";
  arrow.style.height = "10px";
  arrow.style.top = "22px";
  arrow.style.left = "10px";
  arrow.style.overflow = "hidden";
  div.appendChild(arrow);
 
  div.onmouseover = function(){
    this.getElementsByTagName("span")[0].innerHTML = that._text + that._overText;
    arrow.style.display = "none";
  }

  div.onmouseout = function(){
    this.getElementsByTagName("span")[0].innerHTML = that._text;
     arrow.style.display = "";
  }
  this._map.getPanes().labelPane.appendChild(div);
  
  return div;
}
ComplexCustomOverlay.prototype.draw = function(){
  var pixel = this._map.pointToOverlayPixel(this._point);
  this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
  this._div.style.top  = pixel.y - 30 + "px";
}