/*
	自动截断字符串
	maxLength：字数
*/
function autoShort(obj,maxLength){
	if(obj.value.length>maxLength){
		alert("输入文本过长,此次输入将被截断至"+maxLength+"个字符...");
		obj.value=obj.value.substring(0,maxLength);
	}
}
/* 
	弹出层录入,编辑
*/
function hurPopDiv(n,id,arg){
	var width;
	var height;
	var idPre="";
	var titleTxt="";
	var tips="（带<span class='red'>*</span>为必填字段）";
	var url;
	
	switch(n){
	    case 1:
	        height =260;
			titleTxt="评语"+tips;
			url="caseAction.do?op=toRemarkPSea&caseId="+id;
			break;
		case 2:
	        height =260;
			titleTxt="完成协催"+tips;
			url="assitanceAction.do?op=toAssRes&assId="+id+"&assType=10";
			break;
    }
	createPopWindow(idPre,titleTxt,url,width,height);
}

