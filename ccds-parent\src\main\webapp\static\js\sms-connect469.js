function execSend(){
	if($("msgContent").value!=""){
		waitSubmit("save");
		waitSubmit("doCancel");
		var url="connectSMS.do";
		$("op").value="connectToCL253";
		var args= $("sendForm").serialize(true);
		args.serverIP="https://sms.253.com/msg/send";
		new Ajax.Request(url,{
			method		:	'post',
			parameters	:	args,
			onSuccess	:	function(response){
				var rs = response.responseText;
				$("rsLayer").innerHTML = rs+"<div class='deepBlue'>[<a href='javascript:void(0)' onclick='cancel()'>请点此关闭</a>]</div>";
				$("rsLayer").show();
				//setTimeout("cancel()",3000);
			},
			onfailure 	: 	function(response){
				if (transport.status == 404)
					alert("您访问的url地址不存在！");
				else
					alert("Error: status code is " + transport.status);
			}
		});
	}
	else{
		alert("未填写短信内容！");
	}
}