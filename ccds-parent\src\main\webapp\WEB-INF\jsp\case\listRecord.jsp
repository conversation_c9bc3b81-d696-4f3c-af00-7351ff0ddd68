<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
	<head>
		<base href="<%=basePath%>"></base>
		<title>催收记录</title>
		<link rel="shortcut icon" href="favicon.ico" />
		<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
        <meta http-equiv="pragma" content="no-cache">
        <meta http-equiv="cache-control" content="no-cache">
        <meta http-equiv="expires" content="0">
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<script type="text/javascript" src="js/prototype.js"></script>
		<script type="text/javascript" src="js/common.js"></script>
		<script type="text/javascript" src="js/case.js"></script>
		<script type="text/javascript" src="js/formCheck.js"></script>
		<script type="text/javascript" src="js/config.js"></script>
		<script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
		<script language="javaScript" type="text/javascript">
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			var prContent = obj.prContent;
			dataId=obj.prId;
			var dblFunc = obj.bankCase?"descPop('caseAction.do?op=caseDesc&caseId="+obj.bankCase.casId+"&view=case')":"";
			var caseCode = obj.bankCase?"<a href=\"javascript:void(0)\" onclick=\""+dblFunc+";return false;\">"+obj.bankCase.casCode+"&nbsp;</a>":"";
			var funcCol = "<img onClick=\"casePopDiv(38,'"+obj.prId+"')\" class=\"hand\" src=\"images/content/edit.gif\" alt=\"编辑\"/>&nbsp;&nbsp;<img onClick=\"caseDelDiv(4,'"+obj.prId+"','1')\" class='hand' src='images/content/del.gif' alt='删除'/>";
			datas = [obj.prId,caseCode,obj.bankCase?obj.bankCase.casName:"",convPrCat(obj.prCat),obj.prTime, obj.prName, obj.prRel, obj.prContact, obj.prConType, prContent, obj.typeList?obj.typeList.typName:"", obj.prNegotiation, obj.prPtpDate, obj.prPtpNum, obj.prReduceM, obj.prReduceState, obj.salEmp?obj.salEmp.seName:"", obj.prStateType?obj.prStateType.typName:"", funcCol];
			if("${CUS_VER_ID}"=="403"){
				datas = [obj.prId,caseCode,obj.bankCase?obj.bankCase.casName:"",convPrCat(obj.prCat), obj.prClType, obj.prTime, obj.prName, obj.prRel, 
						obj.prContact, obj.prConType, prContent, obj.typeList?obj.typeList.typName:"", obj.prNegotiation, obj.prPtpDate, obj.prPtpNum, 
						obj.prReduceM, obj.prReduceState, obj.prIsAnswered=='1'?'是':(obj.prIsAnswered=='0'?'否':''), obj.prIsSelfAnswered=='1'?'是':(obj.prIsSelfAnswered=='0'?'否':''), 
						obj.prCaseConState, obj.salEmp?obj.salEmp.seName:"", obj.prStateType?obj.prStateType.typName:"", obj.prNxtClDate, obj.prNxtClType, funcCol];
			}
			return [datas,className,dblFunc,dataId];
		}
		
		function getFormArgs(){
			var pars;
			pars = $("searchForm").serialize(true);
			pars.op = "listRecord"; 
			return pars;
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "caseAction.do";
			var pars = getFormArgs();
			var loadFunc = "loadList";
			var cols = [
				{name:"ID"},
				{name:"个案序列号",align:"left"},
				{name:getCasNameTxt("${CUS_VER_ID}")},
				{name:"催收措施"},
				{name:"时间",renderer:'time'},
				{name:"对象姓名"},
				{name:"关系"},
				{name:"电话/地址",align:"left"},
				{name:"类型"},
				{name:"催收记录",align:'left',isSort:false},
				{name:"催收结果"},
				{name:"谈判方式"},
				{name:"承诺日期",renderer:'date'},
				{name:"承诺金额",renderer:'moneyOrEmt',align:'right'},	
				{name:"减免金额",renderer:'moneyOrEmt',align:'right'},
				{name:"减免状态"},
				{name:"催收员"},
				{name:"催收状态"},
				{name:"操作"}
			];
			if("${CUS_VER_ID}"=="403"){
				cols = [
					{name:"ID"},
					{name:"个案序列号",align:"left"},
					{name:getCasNameTxt("${CUS_VER_ID}")},
					{name:"催收措施"},
					{name:"催收类型"},
					{name:"时间",renderer:'time'},
					{name:"对象姓名"},
					{name:"关系"},
					{name:"电话/地址",align:"left"},
					{name:"类型"},
					{name:"催收记录",align:'left',isSort:false},
					{name:"催收结果"},
					{name:"谈判方式"},
					{name:"承诺日期",renderer:'date'},
					{name:"承诺金额",renderer:'moneyOrEmt',align:'right'},	
					{name:"减免金额",renderer:'moneyOrEmt',align:'right'},
					{name:"减免状态"},
					{name:"是否接听"},
					{name:"是否本人接听"},
					{name:"案件状态"},
					{name:"催收员"},
					{name:"催收状态"},
					{name:"下次跟进日期",renderer:'date'},
					{name:"下次催收类型"},
					{name:"操作"}
				];
			}
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
		}
    	var gridEl = new MGrid("listPhoRecordTab","dataList");
    	gridEl.config.hasCheckBox = true;
	    createProgressBar();
     	window.onload=function(){
			//loadList();
			createCancelButton(loadList,'searchForm',-50,5,'searButton','after');
			$("casNameTxt").innerHTML=getCasNameTxt("${CUS_VER_ID}");
			$("casCaCdTxt").innerHTML=getCasCaCdTxt("${CUS_VER_ID}");
			$("bankTxt").innerHTML=getBankTxt("${CUS_VER_ID}");
			loadCaseState('caseState');
			loadRedBlueState('redBlueSt');
			$("caseState").value="u";//默认未退案
			loadPrCat("prCat");
			closeProgressBar();
		}
	/*function searchCode(){
		 var someValue= window.showModalDialog("caseAction.do?op=findBank","","dialogWidth=600px;dialogHeight=500px;status=no;help=no;scrollbars=no");
		 if(someValue)
         document.getElementById("batCode").value=someValue;
	}*/
  </script>
</head>


  <body>
 <div id="mainbox">
    	<div id="contentbox">
        	<div id="title">
            	<table>
                	<tr>
                    	<th>数据管理 > 催记管理 <span id="changeFuncBt" onMouseOver="popFuncMenu(['case',3],true)">切换</span><div id="funcMenu" onMouseOver="popFuncMenu(['case',3],true)" onMouseOut="popFuncMenu(['case',3],false)" style="display:none"><div id="showFuncBt">切换</div></div></th>
                        <td><a class="refreshButton" href="javascript:void(0)" onClick="self.history.go(0)" title="刷新">&nbsp;</a></td>
                    </tr>
                </table>
 			</div>
            <table class="mainTab" cellpadding="0" cellspacing="0">
                <tr>
                    <th>
                        <div id="tabType">
                            <div id="tabType1" class="tabTypeWhite" onClick="self.location.href='caseAction.do?op=toListRecord'">催记管理</div>
                        </div>
                    </th>
                </tr>
            </table>
			<script type="text/javascript">loadTabTypeWidth();</script>
            <div id="listContent">  
                <div class="listSearch">
                    <form class="listSearchForm" id="searchForm" onSubmit="loadList();return false;">
                    <table cellpadding="0" cellspacing="0">
                        <tr>      
                        	<th>催收区域：</th>
                            <td>
                               <c:if test="${!empty userAreaList}">
                                <select name='clArea' class="inputSize2">
                                    <option value="<c:forEach items="${userAreaList}" var="uArea">${uArea.uarArea.typId},</c:forEach>">全部</option>
                                    <c:forEach items="${userAreaList}" var="userArea">
                                    <option value="${userArea.uarArea.typId}">${userArea.uarArea.typName}</option>
                                    </c:forEach>
                                </select>
                                </c:if>
                                <c:if test="${empty userAreaList}">
                                    <c:if test="${!empty areaList}">
                                    <select name='clArea' class="inputSize2">
                                        <option value="">全部</option>
                                        <c:forEach items="${areaList}" var="area">
                                        <option value="${area.typId}">${area.typName}</option>
                                        </c:forEach>
                                    </select>
                                    </c:if>
                                    <c:if test="${empty areaList}">
                                    <select name='clArea' class="inputSize2">
                                        <option value="">未添加</option>
                                    </select>
                                    </c:if>
                                </c:if></td>             
                        	<th>部门：</th>
                            <td><select id="org" name="org" class="inputSize2 inputBoxAlign" onChange="loadOrgEmp(this)"><option value="">全部</option><c:if test="${!empty orgList}"><c:forEach items="${orgList}" var="oList"><option value="${oList.soCode}">${oList.soName}</option></c:forEach></c:if></select></td>
                            <th>催收员：</th>
                            <td colspan="2"><input style="width:60px;" class="inputSize2 inputBoxAlign" type="text" id="empName" name="empName" onBlur="autoShort(this,100)"/>&nbsp;<c:if test="${!empty empList}"><select id="empSel" class="inputSize2 inputBoxAlign" style="width:70px" onChange="setEmpNameFromSel(this,'empName')"><option value="">请选择</option><c:forEach items="${empList}" var="eList"><option value="${eList.seName}">${eList.seName}</option></c:forEach></select></c:if><c:if test="${empty empList}"><select class="inputSize2 inputBoxAlign" style="width:70px" disabled="disabled"><option>未添加</option></select></c:if></td>
                            <th>个案序列号：</th>
                            <td><input class="inputSize2 inputBoxAlign" type="text" name="caseCode" onBlur="autoShort(this,100)"/></td>
                            <th><span id="casNameTxt"></span>：</th>
                            <td><input class="inputSize2 inputBoxAlign" style="width:60px" type="text" name="caseName" onBlur="autoShort(this,50)"/></td>
                        </tr>
                        <tr>
                        	<th>催收措施：</th>
                            <td><select id="prCat" name="prCat" class="inputSize2"><option value=""></option></select></td>
                            <th><span id="bankTxt"></span>：</th>
                            <td colspan="2"><input style="width:110px;" class="inputSize2 inputBoxAlign" type="text" id="bankName" name="bankName" onBlur="autoShort(this,100)"/>&nbsp;<c:if test="${!empty bankList}"><select class="inputSize2 inputBoxAlign" style="width:100px"onChange="setValueFromSel(this,'bankName')"><option value="">请选择</option><c:forEach items="${bankList}" var="bList"><option value="${bList.typName}">${bList.typName}</option></c:forEach></select></c:if><c:if test="${empty bankList}"><select class="inputSize2 inputBoxAlign" disabled="disabled" style="width:100px"><option>未添加</option></select></c:if></td>
                        	<th>案件状态：</th>
                            <td><select  id="caseState" name="casState" class="inputSize2" style="width:60px"><option value="u">未退案</option></select></td>
                        	<th>委案日期：</th>
                            <td colspan="3"><input name="caseDateStart" id="cdStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('cdEnd');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'cdEnd\')}'})"/>&nbsp;到&nbsp;<input name="caseDateEnd" id="cdEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'cdStart\')}'})"/></td>
                        </tr>
                        <tr>
                        	<th>催收结果：</th>
                            <td>
                            	<select name="contactCat" class="inputSize2">
                            		<option value=""></option>
                                     <c:if test="${!empty cpType}">
                                     <c:forEach var="cpType" items="${cpType}">
                                     <option value="${cpType.typId}">${cpType.typName}</option>
                                     </c:forEach>
                                     </c:if>
                            	</select>
                           	</td>
                        	<th>批次号：</th>
                            <td colspan="4"><input class="inputSize2 inputBoxAlign" style="width:300px" type="text" id="batCodeStr" name="batCode" onDblClick="cleanBatCodeInput()"/><input type="text" style="display:none" id="batIds" name="batIds"/>&nbsp;<button class="butSize2 inputBoxAlign" onClick="showBatList()" style="width:40px">选择</button><!--<input class="inputSize2 inputBoxAlign" type="text" id="batCode" name="batCode" onBlur="autoShort(this,100)"/>&nbsp;<button class="butSize2 inputBoxAlign" onClick="addDivBrow(22)" style="width:40px">选择</button>--><!--<span onClick="searchCode();" class="cbatbtn"></span>&nbsp;&nbsp;--></td>
                            <th>预计退案日：</th>
                            <td colspan="3"><input name="planDateStart" id="pdStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('pdEnd');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'pdEnd\')}'})"/>&nbsp;到&nbsp;<input name="planDateEnd" id="pdEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; " readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'pdStart\')}'})"/></td>
                       </tr>
                       <tr>
                          	<th>证件号：</th>
                            <td><input class="inputSize2 inputBoxAlign" type="text" id="caseNum" name="caseNum" value="${caseNum}" onBlur="autoShort(this,25)"/></td>
                         	<th><span id="casCaCdTxt"></span>：</th>
                        	<td><input class="inputSize2 inputBoxAlign" type="text" id="casCaCd" name="casCaCd" value="${casCaCd}" onBlur="autoShort(this,50)"/></td>
                            <th>催记内容：</th>
                            <td colspan="2"><input class="inputSize2 inputBoxAlign" style="width:130px" type="text" name="prContent" onBlur="autoShort(this,500)"/></td>
                            <th>催收时间：</th>
                            <td colspan="3"><input name="startTime" id="staTim" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('endTim');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'endTim\')}'})"/>&nbsp;到&nbsp;<input name="endTime" id="endTim" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'staTim\')}'})"/></td>
                       </tr>
                       <tr>
                       		<th>标色状态：</th>
                        	<td><select id="redBlueSt" name="redBlueSt" class="inputSize2"></select></td>
                        	<td colspan="2">&nbsp;&nbsp;&nbsp;<input id="searButton" type="submit" class="butSize3 inputBoxAlign" value="查询"/></td>
                       </tr>
                   </table>
                    </form>
                </div>
                <div id="toolsBarTop" class="bottomBar">
                    <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(19)">删除催记</span>
                    <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(16,true)">导出所选催记</span>
                    <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="casePopDiv(62)">导出查询结果</span>
                </div>
                <div id="dataList" class="dataList"></div>
            </div>
			</div>
		</div>
	</body>
</html>
