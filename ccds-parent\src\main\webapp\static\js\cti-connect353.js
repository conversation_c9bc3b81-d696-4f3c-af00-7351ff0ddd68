function connectCTI(phone){
	var url = "connectCTI.do?op=connectToMW";
	var pars = [];
	pars.phone = phone;
	//pars.localNo = localNo;
	pars.serverIP = "*************";
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var rs = response.responseText;
			if(rs=="1"){
				alert("拨号成功！");
			}
			else{
				alert("拨号失败！");
			}
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}