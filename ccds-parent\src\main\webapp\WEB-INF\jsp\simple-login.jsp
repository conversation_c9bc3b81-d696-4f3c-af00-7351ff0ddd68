<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>催收管理系统 - 登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .login-container {
            background-color: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            width: 400px;
        }
        .login-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group input:focus {
            border-color: #4CAF50;
            outline: none;
        }
        .login-btn {
            width: 100%;
            padding: 12px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }
        .login-btn:hover {
            background-color: #45a049;
        }
        .error-msg {
            color: #d32f2f;
            text-align: center;
            margin-top: 10px;
            display: none;
        }
        .success-msg {
            color: #4CAF50;
            text-align: center;
            margin-top: 10px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2 class="login-title">催收管理系统</h2>
        <form id="loginForm" action="/doLogin" method="post">
            <div class="form-group">
                <label for="loginName">用户名:</label>
                <input type="text" id="loginName" name="loginName" required>
            </div>
            <div class="form-group">
                <label for="pwd">密码:</label>
                <input type="password" id="pwd" name="pwd" required>
            </div>
            <button type="submit" class="login-btn">登录</button>
        </form>
        <div id="errorMsg" class="error-msg">
            <c:if test="${not empty error}">
                ${error}
            </c:if>
        </div>
        <div id="successMsg" class="success-msg">
            <c:if test="${not empty success}">
                ${success}
            </c:if>
        </div>
    </div>

    <script>
        // 显示错误或成功消息
        window.onload = function() {
            var errorMsg = document.getElementById('errorMsg');
            var successMsg = document.getElementById('successMsg');
            
            if (errorMsg.innerHTML.trim() !== '') {
                errorMsg.style.display = 'block';
            }
            if (successMsg.innerHTML.trim() !== '') {
                successMsg.style.display = 'block';
            }
        };

        // 表单提交处理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            var loginName = document.getElementById('loginName').value;
            var pwd = document.getElementById('pwd').value;
            
            if (!loginName || !pwd) {
                document.getElementById('errorMsg').innerHTML = '请输入用户名和密码';
                document.getElementById('errorMsg').style.display = 'block';
                return;
            }
            
            // 使用AJAX提交登录请求
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/doLogin', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        var response = JSON.parse(xhr.responseText);
                        if (response.code === 200) {
                            document.getElementById('successMsg').innerHTML = '登录成功，正在跳转...';
                            document.getElementById('successMsg').style.display = 'block';
                            document.getElementById('errorMsg').style.display = 'none';
                            // 跳转到主页面
                            setTimeout(function() {
                                window.location.href = '/main';
                            }, 1000);
                        } else {
                            document.getElementById('errorMsg').innerHTML = response.msg || '登录失败';
                            document.getElementById('errorMsg').style.display = 'block';
                            document.getElementById('successMsg').style.display = 'none';
                        }
                    } else {
                        document.getElementById('errorMsg').innerHTML = '网络错误，请重试';
                        document.getElementById('errorMsg').style.display = 'block';
                    }
                }
            };
            
            var formData = 'loginName=' + encodeURIComponent(loginName) + '&pwd=' + encodeURIComponent(pwd);
            xhr.send(formData);
        });
    </script>
</body>
</html>
