-- 催收系统数据库安装脚本
-- 适用于MySQL 5.7
-- 执行方式：mysql -u root -p < install.sql

-- 设置字符集
SET NAMES utf8;
SET FOREIGN_KEY_CHECKS = 0;

-- 删除数据库（如果存在）
DROP DATABASE IF EXISTS `ccds`;

-- 创建数据库
CREATE DATABASE `ccds` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;

-- 使用数据库
USE `ccds`;

-- 显示开始信息
SELECT '开始创建催收系统数据库...' AS '状态';

-- 执行完整脚本
SOURCE mysql57-complete.sql;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示完成信息
SELECT '数据库安装完成！' AS '状态';
SELECT '请使用以下信息登录系统：' AS '提示';
SELECT 'URL: http://localhost:8080' AS '访问地址';
SELECT '用户名: admin' AS '登录账号';
SELECT '密码: 123456' AS '登录密码';
