function connectCTI(phone){
	var serverIP = "http://cc.huayucc.com/setevent/makeCallCJI";
	var targettype = 'exter';
	var agentgroupid = '163';
	var usertype = 'agent';
    var orgidentity = 'kxjr';
    var pwdtype = 'md5';
    var modeltype = 'Campaign';
    var model_id = '278';
    var user = $("ctiUser")!=null?$("ctiUser").value:parent.document.getElementById("ctiUser").value;
    var password = hex_md5($("ctiPwd")!=null?$("ctiPwd").value:parent.document.getElementById("ctiPwd").value);
    alert("targetdn="+encodeURIComponent(phone)+"&targettype="+targettype+"&agentgroupid="+agentgroupid+"&usertype="+usertype+"&user="+user+"&orgidentity="+orgidentity+"&pwdtype="+pwdtype+"&password="+password+"&modeltype="+modeltype+"&model_id="+model_id+"&callback=");
	var ajaxParas = ({
        url: serverIP,              //请求地址
        type: "POST",                       //请求方式
        data: "targetdn="+encodeURIComponent(phone)+"&targettype="+targettype+"&agentgroupid="+agentgroupid+"&usertype="+usertype+"&user="+user+"&orgidentity="+orgidentity+"&pwdtype="+pwdtype+"&password="+password+"&modeltype="+modeltype+"&model_id="+model_id+"&callback=",        //请求参数
        dataType: "json",
        callback: "callback",
        success: function (jsonData) {
        	var json = jsonData;
        	 if(json.code==1){
                 alert('呼叫成功！');

             }else if(json.code==2){
                 var msg = '呼叫失败！失败原因：'+json.message;
                 alert(msg);
             }
             if(typeof(json.param) != 'undefined'){
                     alert('param: '+json.param);
             }
             if(typeof(json.status) != 'undefined'){
                 alert('status: '+json.status);
             }
        },
        fail: function (jsonData) {
        	var rs = jsonData;
            alert(rs.message);
        }
    });
	
	jsonp(ajaxParas);
}

function jsonp(options) {
    options = options || {};
    if (!options.url || !options.callback) {
        throw new Error("参数不合法");
    }

    //创建 script 标签并加入到页面中
    var callbackName = ('jsonp_' + Math.random()).replace(".", "");
    var oHead = document.getElementsByTagName('head')[0];
    options.data+= callbackName;
    var params = options.data;
    var oS = document.createElement('script');
    oHead.appendChild(oS);

    //创建jsonp回调函数
    window[callbackName] = function (json) {
        oHead.removeChild(oS);
        //clearTimeout(oS.timer);
        window[callbackName] = null;
        options.success && options.success(json);
    };
    //发送请求
    oS.src = options.url + '?' + params;

    //超时处理
   /* if (options.time) {
        oS.timer = setTimeout(function () {
            window[callbackName] = null;
            oHead.removeChild(oS);
            options.fail && options.fail({ message: "超时" });
        }, time);
    }*/
};
//格式化参数 TODO
/*function formatParams(data) {
    var arr = [];
    for (var name in data) {
        arr.push(encodeURIComponent(name) + '=' + encodeURIComponent(data[i]));
    }
    return arr.join('&');
}*/