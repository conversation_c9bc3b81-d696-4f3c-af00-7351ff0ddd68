-- 批量生成测试数据的脚本
-- 可以根据需要调整数量和内容
USE ccds;

-- 生成更多案件数据 (从ID 11开始，添加30个案件)
INSERT IGNORE INTO bank_case (cas_id, cas_code, cas_group, cas_state, cas_name, cas_mob_pho, cas_m, cas_paid_m, cas_se_no, cas_ins_time, cas_ins_user, cas_alt_time, cas_alt_user, cas_remark, cas_ca_cd, cas_hom_add) VALUES
-- 建设银行案件 (11-20)
(11, 'CASE011', 'B组', 1, '陈志华', '***********', 60000.00, 30000.00, 1, '2024-03-20 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期55天，积极配合', '320101198511111234', '江苏省无锡市梁溪区中山路808号'),
(12, 'CASE012', 'B组', 1, '林小玲', '***********', 85000.00, 0.00, 2, '2024-02-05 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期95天，经济纠纷', '320101198512121234', '江苏省无锡市滨湖区太湖大道909号'),
(13, 'CASE013', 'B组', 1, '黄建军', '***********', 70000.00, 24000.00, 3, '2024-03-05 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期70天，分期还款', '320101198601011234', '江苏省无锡市新吴区长江路1010号'),
(14, 'CASE014', 'B组', 1, '许大海', '13800138014', 95000.00, 0.00, 4, '2024-02-28 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期85天，法律程序', '320101198602021234', '江苏省无锡市惠山区惠山大道1111号'),
(15, 'CASE015', 'B组', 1, '谢小燕', '13800138015', 40000.00, 0.00, 5, '2024-04-01 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期40天，刚开始催收', '320101198603031234', '江苏省无锡市江阴市澄江中路1212号'),

-- 农业银行案件 (16-25)
(16, 'CASE016', 'C组', 1, '马文龙', '13800138016', 110000.00, 0.00, 1, '2023-12-15 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期130天，高额欠款', '320101198604041234', '江苏省常州市天宁区延陵西路1313号'),
(17, 'CASE017', 'C组', 1, '杨小梅', '13800138017', 65000.00, 18000.00, 2, '2024-02-18 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期75天，还款计划', '320101198605051234', '江苏省常州市钟楼区南大街1414号'),
(18, 'CASE018', 'C组', 1, '朱志勇', '13800138018', 80000.00, 0.00, 3, '2024-01-28 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期90天，联系中断', '320101198606061234', '江苏省常州市新北区太湖东路1515号'),
(19, 'CASE019', 'C组', 1, '何小丽', '13800138019', 50000.00, 8000.00, 4, '2024-03-12 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期60天，医疗费用', '320101198607071234', '江苏省常州市武进区湖塘镇1616号'),
(20, 'CASE020', 'C组', 1, '冯大强', '13800138020', 75000.00, 0.00, 5, '2024-01-12 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期105天，生意失败', '320101198608081234', '江苏省常州市金坛区金城镇1717号'),

-- 交通银行案件 (21-30)
(21, 'CASE021', 'D组', 1, '蒋小华', '13800138021', 55000.00, 22000.00, 1, '2024-03-25 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期50天，新案件', '320101198609091234', '江苏省镇江市京口区中山东路1818号'),
(22, 'CASE022', 'D组', 1, '韩志明', '13800138022', 90000.00, 0.00, 2, '2024-01-08 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期115天，投资失败', '320101198610101234', '江苏省镇江市润州区金山路1919号'),
(23, 'CASE023', 'D组', 1, '曹小红', '13800138023', 70000.00, 0.00, 3, '2024-02-12 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期85天，离婚纠纷', '320101198611111234', '江苏省镇江市丹徒区上党镇2020号'),
(24, 'CASE024', 'D组', 1, '邓大伟', '13800138024', 85000.00, 0.00, 4, '2024-01-30 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期95天，房产抵押', '320101198612121234', '江苏省镇江市丹阳市云阳镇2121号'),
(25, 'CASE025', 'D组', 1, '范小玲', '13800138025', 45000.00, 15000.00, 5, '2024-03-08 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期65天，教育支出', '320101198701011234', '江苏省镇江市扬中市三茅镇2222号'),

-- 招商银行案件 (26-35)
(26, 'CASE026', 'E组', 1, '高志强', '13800138026', 100000.00, 0.00, 1, '2024-01-03 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期120天，跨省案件', '320101198702021234', '浙江省杭州市西湖区文三路2323号'),
(27, 'CASE027', 'E组', 1, '胡小燕', '13800138027', 60000.00, 20000.00, 2, '2024-02-25 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期70天，创业失败', '320101198703031234', '浙江省杭州市拱墅区莫干山路2424号'),
(28, 'CASE028', 'E组', 1, '姜大明', '13800138028', 75000.00, 0.00, 3, '2024-02-15 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期80天，股票亏损', '320101198704041234', '浙江省杭州市江干区钱江路2525号'),
(29, 'CASE029', 'E组', 1, '孔小丽', '13800138029', 55000.00, 12000.00, 4, '2024-03-18 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期55天，医疗开支', '320101198705051234', '浙江省杭州市下城区体育场路2626号'),
(30, 'CASE030', 'E组', 1, '雷志华', '13800138030', 80000.00, 0.00, 5, '2024-01-25 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期90天，装修贷款', '320101198706061234', '浙江省杭州市上城区解放路2727号'),

-- 中国银行案件 (31-40)
(31, 'CASE031', 'F组', 1, '李大龙', '13800138031', 95000.00, 0.00, 1, '2024-01-15 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期110天，上海案件', '320101198707071234', '上海市浦东新区陆家嘴环路2828号'),
(32, 'CASE032', 'F组', 1, '刘小梅', '13800138032', 65000.00, 25000.00, 2, '2024-02-22 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期75天，消费贷款', '320101198708081234', '上海市黄浦区南京东路2929号'),
(33, 'CASE033', 'F组', 1, '毛志勇', '13800138033', 70000.00, 0.00, 3, '2024-03-02 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期65天，房贷逾期', '320101198709091234', '上海市静安区南京西路3030号'),
(34, 'CASE034', 'F组', 1, '倪小芳', '13800138034', 50000.00, 35000.00, 4, '2024-04-05 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期45天，信用卡债务', '320101198710101234', '上海市徐汇区淮海中路3131号'),
(35, 'CASE035', 'F组', 1, '欧阳华', '13800138035', 85000.00, 0.00, 5, '2024-01-18 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期100天，经营贷款', '320101198711111234', '上海市长宁区延安西路3232号'),

-- 民生银行案件 (36-40)
(36, 'CASE036', 'G组', 1, '潘大伟', '13800138036', 120000.00, 0.00, 1, '2023-12-10 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期140天，北京案件', '320101198712121234', '北京市朝阳区建国门外大街3333号'),
(37, 'CASE037', 'G组', 1, '秦小玲', '13800138037', 75000.00, 28000.00, 2, '2024-02-08 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期85天，科技公司', '320101198801011234', '北京市海淀区中关村大街3434号'),
(38, 'CASE038', 'G组', 1, '任志明', '13800138038', 90000.00, 0.00, 3, '2024-01-22 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期95天，金融从业', '320101198802021234', '北京市西城区金融大街3535号'),
(39, 'CASE039', 'G组', 1, '宋小红', '13800138039', 60000.00, 0.00, 4, '2024-02-28 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期70天，商业贷款', '320101198803031234', '北京市东城区王府井大街3636号'),
(40, 'CASE040', 'G组', 1, '唐大强', '13800138040', 105000.00, 0.00, 5, '2024-01-02 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期125天，投资亏损', '320101198804041234', '北京市丰台区丽泽金融商务区3737号');

-- 生成对应的催收记录 (从ID 51开始)
INSERT IGNORE INTO pho_red (pr_id, pr_cas_id, pr_se_no, pr_con_type, pr_time, pr_contact, pr_content, pr_name, pr_rel) VALUES
-- 案件11-15的催收记录
(51, 11, 1, '电话', '2024-03-21 10:30:00', '***********', '客户主动联系，表示已准备部分资金，希望先还一部分。', '陈志华', '本人'),
(52, 11, 1, '电话', '2024-04-01 13:30:00', '***********', '客户支付30000元，承诺剩余款项分2期支付。态度诚恳。', '陈志华', '本人'),
(53, 12, 2, '电话', '2024-02-06 09:30:00', '***********', '客户因经济纠纷导致资金困难，正在通过法律途径解决。', '林小玲', '本人'),
(54, 13, 3, '电话', '2024-03-06 09:45:00', '***********', '客户态度良好，承认欠款，表示因为房屋装修导致资金紧张。', '黄建军', '本人'),
(55, 13, 3, '电话', '2024-04-01 11:10:00', '***********', '分期方案获得批准，客户同意每月还款12000元。已发送分期协议。', '黄建军', '本人'),
(56, 14, 4, '电话', '2024-03-01 14:20:00', '13800138014', '客户拒绝还款，声称要通过法律程序解决，态度强硬。', '许大海', '本人'),
(57, 15, 5, '电话', '2024-04-02 10:15:00', '13800138015', '新案件首次联系，客户态度配合，承认欠款。', '谢小燕', '本人'),

-- 案件16-20的催收记录
(58, 16, 1, '电话', '2023-12-16 09:00:00', '13800138016', '高额欠款案件，客户失联，多次拨打无人接听。', '马文龙', '本人'),
(59, 17, 2, '电话', '2024-02-19 14:20:00', '13800138017', '客户态度配合，表示愿意按计划还款，已制定详细还款计划。', '杨小梅', '本人'),
(60, 17, 2, '电话', '2024-03-15 10:30:00', '13800138017', '跟进还款计划执行情况，客户按时支付第一期款项。', '杨小梅', '本人'),
(61, 18, 3, '电话', '2024-01-29 11:45:00', '13800138018', '客户电话中断联系，疑似更换号码。', '朱志勇', '本人'),
(62, 19, 4, '电话', '2024-03-13 16:00:00', '13800138019', '客户说明因医疗费用导致资金困难，提供了医院证明。', '何小丽', '本人'),
(63, 19, 4, '电话', '2024-04-10 11:20:00', '13800138019', '客户医疗费用问题缓解，主动联系还款8000元。', '何小丽', '本人'),
(64, 20, 5, '电话', '2024-01-13 15:30:00', '13800138020', '客户生意失败，表示暂时无法还款，请求宽限时间。', '冯大强', '本人'),

-- 案件21-25的催收记录
(65, 21, 1, '电话', '2024-03-26 09:30:00', '13800138021', '新案件首次联系，客户态度良好，承认欠款，表示会尽快处理。', '蒋小华', '本人'),
(66, 21, 1, '电话', '2024-04-15 14:45:00', '13800138021', '客户主动联系，表示已筹集到部分资金，希望先还一部分。', '蒋小华', '本人'),
(67, 22, 2, '电话', '2024-01-09 10:20:00', '13800138022', '客户投资失败，资金链断裂，暂时无法还款。', '韩志明', '本人'),
(68, 23, 3, '电话', '2024-02-13 13:15:00', '13800138023', '客户因离婚纠纷导致财产分割，影响还款能力。', '曹小红', '本人'),
(69, 24, 4, '电话', '2024-01-31 16:40:00', '13800138024', '客户房产抵押手续复杂，需要时间处理。', '邓大伟', '本人'),
(70, 25, 5, '电话', '2024-03-09 10:15:00', '13800138025', '客户说明因子女教育费用导致资金紧张，态度诚恳。', '范小玲', '本人'),
(71, 25, 5, '电话', '2024-04-20 15:30:00', '13800138025', '客户教育费用压力缓解，主动还款15000元。', '范小玲', '本人');

-- 生成对应的还款记录 (从ID 46开始)
INSERT IGNORE INTO case_paid (pa_id, pa_cas_id, pa_paid_time, pa_paid_num, pa_writer, pa_wri_time, pa_sur_remark, pa_state) VALUES
-- 案件11的还款记录
(46, 11, '2024-04-01 10:20:00', 30000.00, 'admin', '2024-04-01 10:20:00', '客户积极配合，主动还款', 1),

-- 案件13的还款记录
(47, 13, '2024-04-20 14:50:00', 12000.00, 'admin', '2024-04-20 14:50:00', '分期还款第一期', 1),
(48, 13, '2024-05-20 16:10:00', 12000.00, 'admin', '2024-05-20 16:10:00', '分期还款第二期', 1),

-- 案件17的还款记录
(49, 17, '2024-03-15 11:30:00', 18000.00, 'admin', '2024-03-15 11:30:00', '客户按还款计划执行', 1),

-- 案件19的还款记录
(50, 19, '2024-04-10 13:20:00', 8000.00, 'admin', '2024-04-10 13:20:00', '医疗费用缓解后的还款', 1),

-- 案件21的还款记录
(51, 21, '2024-04-15 15:40:00', 22000.00, 'admin', '2024-04-15 15:40:00', '新案件快速回款', 1),

-- 案件25的还款记录
(52, 25, '2024-04-20 09:25:00', 15000.00, 'admin', '2024-04-20 09:25:00', '教育支出缓解后还款', 1),

-- 案件27的还款记录
(53, 27, '2024-05-10 12:15:00', 20000.00, 'admin', '2024-05-10 12:15:00', '创业项目有起色后还款', 1),

-- 案件29的还款记录
(54, 29, '2024-04-15 14:35:00', 12000.00, 'admin', '2024-04-15 14:35:00', '医疗费用问题解决后还款', 1),

-- 案件32的还款记录
(55, 32, '2024-04-20 10:50:00', 25000.00, 'admin', '2024-04-20 10:50:00', '消费贷款部分还款', 1),

-- 案件34的还款记录
(56, 34, '2024-05-22 16:20:00', 35000.00, 'admin', '2024-05-22 16:20:00', '信用卡债务大额还款', 1),

-- 案件37的还款记录
(57, 37, '2024-04-18 11:45:00', 28000.00, 'admin', '2024-04-18 11:45:00', '科技公司项目回款', 1);

COMMIT;

-- 显示导入结果
SELECT '=== 批量测试数据导入完成 ===' AS message;
SELECT '案件总数量' AS type, COUNT(*) AS count FROM bank_case;
SELECT '催收记录总数量' AS type, COUNT(*) AS count FROM pho_red;
SELECT '还款记录总数量' AS type, COUNT(*) AS count FROM case_paid;
SELECT '总还款金额' AS type, SUM(pa_paid_num) AS total_amount FROM case_paid;
SELECT '平均案件金额' AS type, AVG(cas_m) AS avg_amount FROM bank_case;
SELECT '总欠款金额' AS type, SUM(cas_m) AS total_debt FROM bank_case;
SELECT '已还款比例' AS type, CONCAT(ROUND((SUM(pa_paid_num) / (SELECT SUM(cas_m) FROM bank_case)) * 100, 2), '%') AS percentage FROM case_paid;
