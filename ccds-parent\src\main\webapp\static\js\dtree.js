/*--------------------------------------------------|
| dTree 2.05 | www.destroydrop.com/javascript/tree/ |
|---------------------------------------------------|
| Copyright (c) 2002-2003 G<PERSON><PERSON>?               |
|                                                   |
| This script can be used freely as long as all     |
| copyright messages are intact.                    |
|                                                   |
| Updated: 17.04.2003                               |
|--------------------------------------------------*/

// Node object
function Node(id, pid, name, url, title, target, icon, iconOpen, open, value) {
	this.id = id;
	this.pid = pid;//parent Id
	this.name = name;
	this.value = value;//value of checkbox
	this.url = url;
	this.title = title;
	this.target = target;
	this.icon = icon;
	this.iconOpen = iconOpen;
	this._io = open || false;//is open
	this._is = false;//is selected
	this._ls = false;//is last node
	this._hc = false;//has children
	this._ai = 0;//index in tree
	this._p;//parent node
};


/* 
	Tree object 
	参数objName为new dTree时使用的对象名称
*/
function dTree(objName) {
	this.config = {
		hasCheckBox				: false, //是否使用checkbox
		hasCallBack				: false, //在勾选完成后是否使用自定义回调方法覆盖默认回调方法（勾选checkbox完成时的回调方法）
		showRootIcon			: true,  //是否显示root图标
		isReturnName			: false, //是否返回树节点内文字

		target					: null,  //打开链接方式
		folderLinks				: true,  //是否在非叶子节点上加链接
		useSelection			: true,  //是否高亮显示选中节点
		useCookies				: true,  //是否保存树显示状态
		useLines				: true,  //是否显示虚线
		useIcons				: true,  //是否显示图标
		useStatusText			: false, //是否在状态栏显示节点信息
		closeSameLevel			: false, //是否在打开子节点时关闭同级别的其他树
		inOrder					: false  //是否根据页面调用add()方法顺序(即aNodes数组中的顺序)生成树
	}
	this.icon = {
		root				: 'images/dtree/base.gif',
		folder				: 'images/dtree/folder.gif',
		folderOpen			: 'images/dtree/folderopen.gif',
		node				: 'images/dtree/page.gif',
		empty				: 'images/dtree/empty.gif',
		line				: 'images/dtree/line.gif',
		join				: 'images/dtree/join.gif',
		joinBottom			: 'images/dtree/joinbottom.gif',
		plus				: 'images/dtree/plus.gif',
		plusBottom			: 'images/dtree/plusbottom.gif',
		minus				: 'images/dtree/minus.gif',
		minusBottom			: 'images/dtree/minusbottom.gif',
		nlPlus				: 'images/dtree/nolines_plus.gif',
		nlMinus				: 'images/dtree/nolines_minus.gif'

	};
	this.obj = objName;
	this.aNodes = [];
	this.aIndent = [];
	this.root = new Node(-1);//根节点
	this.selectedNode = null;
	this.selectedFound = false;
	this.completed = false;
};

// Adds a new node to the node array
dTree.prototype.add = function(id, pid, name, url, title, target, icon, iconOpen, open, value) {
	this.aNodes[this.aNodes.length] = new Node(id, pid, name, url, title, target, icon, iconOpen, open, value);//初始化aNodes;
};


// Open/close all nodes
dTree.prototype.openAll = function() {
	this.oAll(true);
};

dTree.prototype.closeAll = function() {
	this.oAll(false);
};


// Outputs the tree to the page
dTree.prototype.toString = function() {
	var str = '<div class="dtree">\n';
	if (document.getElementById) {
		if (this.config.useCookies) this.selectedNode = this.getSelected();//载入选中项
		str += this.addNode(this.root);//生成树
	} else str += 'Browser not supported.';
	str += '</div>';
	
	if(this.config.isReturnName){
		str += "<div class='treeButton'><button onclick=\"chooseManyNodes("+ this.obj +","+ this.config.hasCallBack +")\">完成</button></div>";
	}
	
	if (!this.selectedFound) this.selectedNode = null;
	this.completed = true;
	return str;
};

// 得到所有选中的checkbox中的value和节点文本内容，返回数组第一个元素为多选框中的value（以,分隔），第二个元素为文本（以;&nbsp;分隔）
dTree.prototype.getSelectedNodes = function (){
	var treeNodes = document.getElementsByName('nodes'+this.obj);
	var rs = ['',''];//初始化数组
	for(var i = 0; i<treeNodes.length; i++){
		if(treeNodes[i].checked==true){
			var nodeValue = treeNodes[i].value;
			var ind = nodeValue.indexOf('|');
			if(nodeValue!=''){
				if(ind!=-1){
					rs[0] += nodeValue.substring(0,ind) + ',';
					rs[1] += nodeValue.substring(ind+1) + '; ';
				}
				else{
					rs[0] += nodeValue + ',';
				}
			}
		}
	}
	return rs;
}

// Creates the tree structure
dTree.prototype.addNode = function(pNode) {
	var str = '';
	var n=0;
	if (this.config.inOrder) n = pNode._ai;//接上一个索引继续循环

	for (n; n<this.aNodes.length; n++) {
		if (this.aNodes[n].pid == pNode.id) {//搜索pNode的下级子节点
			var cn = this.aNodes[n];
			cn._p = pNode;
			cn._ai = n;//设置节点索引

			this.setCS(cn);//设置是否有子节点和是否最后一个元素
			if (!cn.target && this.config.target) cn.target = this.config.target;
			if (cn._hc && !cn._io && this.config.useCookies) cn._io = this.isOpen(cn.id);//设置是否打开
			if (!this.config.folderLinks && cn._hc) cn.url = null;//清除文件夹（有子节点的节点）的链接
			if (this.config.useSelection && cn.id == this.selectedNode && !this.selectedFound) {
					cn._is = true;
					this.selectedNode = n;//?
					this.selectedFound = true;//标志选中状态
			}
			str += this.node(cn, n);
			if (cn._ls) break;//如果到到达树底层节点跳回上层父节点（前序遍历），避免无意义的循环
		}
	}
	return str;
};


// Creates the node icon, url and text
dTree.prototype.node = function(node, nodeId) {
	var str = '<div class="dTreeNode">' + this.indent(node, nodeId);

	//显示多选框
	if(this.config.hasCheckBox) {
		var cbValue;
		if(this.config.isReturnName&& node.value!=undefined && node.value!=''){
			cbValue = node.value + "|" + node.name;
		}
		else{
			cbValue = node.value;
		}
		str += '<input type="checkbox" style="vertical-align:middle;margin:0;padding:0;" ' + ((node.value == undefined||node.value=='') ? '' : ('name="nodes' + this.obj + '" value="' + cbValue + '"')) + 'id="cb' + this.obj + nodeId + '" onclick="javascript: ' + this.obj + '.checkChild(\'' + nodeId + '\')" />';
	}
	
	if (this.config.useIcons && !(this.root.id == node.pid && this.config.showRootIcon == false)) {
		if (!node.icon&&node.icon!="") node.icon = (this.root.id == node.pid) ? this.icon.root : ((node._hc) ? this.icon.folder : this.icon.node);
		if (!node.iconOpen&&node.iconOpen!="") node.iconOpen = (this.root.id == node.pid) ? this.icon.root : (node._hc) ? this.icon.folderOpen : this.icon.node;
		/* 放开注释将不可自定义root图标
		if (this.root.id == node.pid) {
			node.icon = this.icon.root;
			node.iconOpen = this.icon.root;
		}
		*/
		str += '<img id="i' + this.obj + nodeId + '" src="' + ((node._io) ? node.iconOpen : node.icon) + '" alt="" />';
		
	}
	if (node.url) {
		str += '<a id="s' + this.obj + nodeId + '" class="' + ((this.config.useSelection) ? ((node._is ? 'nodeSel' : 'node')) : 'node') + '" href="' + node.url + '"';
		if (node.target) str += ' target="' + node.target + '"';
		if (this.config.useStatusText) str += ' onmouseover="window.status=\'' + node.name + '\';return true;" onmouseout="window.status=\'\';return true;" ';
		if (this.config.useSelection && ((node._hc && this.config.folderLinks) || !node._hc))
			str += ' onclick="javascript: ' + this.obj + '.s(' + nodeId + ');"';
		str += '>';
	}
	else if ((!this.config.folderLinks || !node.url) && node._hc && node.pid != this.root.id){
		str += '<a href="javascript: ' + this.obj + '.o(' + nodeId + ');" class="node">';
	}
	str += '<span ';
	if (node.title) str += ' title="' + node.title + '"';
	str += '>' + node.name + '</span>';
	if (node.url || ((!this.config.folderLinks || !node.url) && node._hc)) {
		str += '</a>';
	}
	str += '</div>';
	if (node._hc) {
		str += '<div id="d' + this.obj + nodeId + '" class="clip" style="display:' + ((this.root.id == node.pid || node._io) ? 'block' : 'none') + ';">';
		str += this.addNode(node);//加载子节点
		str += '</div>';
	}
	this.aIndent.pop();
	return str;
};


// Adds the empty and line icons
dTree.prototype.indent = function(node, nodeId) {
	var str = '';
	if (this.root.id != node.pid) {
		for (var n=0; n<this.aIndent.length; n++)
			str += '<img src="' + ( (this.aIndent[n] == 1 && this.config.useLines) ? this.icon.line : this.icon.empty ) + '" alt="" />';
		(node._ls) ? this.aIndent.push(0) : this.aIndent.push(1);
		if (node._hc) {
			str += '<a href="javascript: ' + this.obj + '.o(' + nodeId + ');"><img id="j' + this.obj + nodeId + '" src="';
			if (!this.config.useLines) str += (node._io) ? this.icon.nlMinus : this.icon.nlPlus;
			else str += ( (node._io) ? ((node._ls && this.config.useLines) ? this.icon.minusBottom : this.icon.minus) : ((node._ls && this.config.useLines) ? this.icon.plusBottom : this.icon.plus ) );
			str += '" alt="" /></a>';
		} else str += '<img src="' + ( (this.config.useLines) ? ((node._ls) ? this.icon.joinBottom : this.icon.join ) : this.icon.empty) + '" alt="" />';
	}
	
	return str;

};


// Checks if a node has any children and if it is the last sibling
dTree.prototype.setCS = function(node) {
	var lastId;
	for (var n=0; n<this.aNodes.length; n++) {
		if (this.aNodes[n].pid == node.id) node._hc = true;
		if (this.aNodes[n].pid == node.pid) lastId = this.aNodes[n].id;
	}
	if (lastId==node.id) node._ls = true;
};



// Returns the selected node
dTree.prototype.getSelected = function() {
	var sn = this.getCookie('cs' + this.obj);
	return (sn) ? sn : null;
};



// Highlights the selected node
dTree.prototype.s = function(id) {
	if (!this.config.useSelection) return;
	var cn = this.aNodes[id];
	if (cn._hc && !this.config.folderLinks) return;
	if (this.selectedNode != id) {
		if (this.selectedNode || this.selectedNode==0) {
			eOld = document.getElementById("s" + this.obj + this.selectedNode);
			if(eOld!=null&&eOld!="")
			eOld.className = "node";
		}
		eNew = document.getElementById("s" + this.obj + id);
		eNew.className = "nodeSel";
		this.selectedNode = id;
		if (this.config.useCookies) this.setCookie('cs' + this.obj, cn.id);
	}
};


// Toggle Open or close
dTree.prototype.o = function(id) {
	var cn = this.aNodes[id];
	this.nodeStatus(!cn._io, id, cn._ls);
	cn._io = !cn._io;
	if (this.config.closeSameLevel) this.closeLevel(cn);
	if (this.config.useCookies) this.updateCookie();
};


// Open or close all nodes
dTree.prototype.oAll = function(status) {
	for (var n=0; n<this.aNodes.length; n++) {
		if (this.aNodes[n]._hc && this.aNodes[n].pid != this.root.id) {
			this.nodeStatus(status, n, this.aNodes[n]._ls)
			this.aNodes[n]._io = status;
		}
	}
	if (this.config.useCookies) this.updateCookie();
};


// Opens the tree to a specific node
dTree.prototype.openTo = function(nId, bSelect, bFirst) {
	if (!bFirst) {
		for (var n=0; n<this.aNodes.length; n++) {
			if (this.aNodes[n].id == nId) {
				nId=n;
				break;
			}
		}
	}
	var cn=this.aNodes[nId];
	if (cn.pid==this.root.id || !cn._p) return;
	cn._io = true;
	cn._is = bSelect;
	if (this.completed && cn._hc) this.nodeStatus(true, cn._ai, cn._ls);
	if (this.completed && bSelect) this.s(cn._ai);
	else if (bSelect) this._sn=cn._ai;
	this.openTo(cn._p._ai, false, true);
};


// Closes all nodes on the same level as certain node
dTree.prototype.closeLevel = function(node) {
	for (var n=0; n<this.aNodes.length; n++) {
		if (this.aNodes[n].pid == node.pid && this.aNodes[n].id != node.id && this.aNodes[n]._hc) {
			this.nodeStatus(false, n, this.aNodes[n]._ls);
			this.aNodes[n]._io = false;
			this.closeAllChildren(this.aNodes[n]);
		}
	}
}


// Closes all children of a node
dTree.prototype.closeAllChildren = function(node) {
	for (var n=0; n<this.aNodes.length; n++) {
		if (this.aNodes[n].pid == node.id && this.aNodes[n]._hc) {
			if (this.aNodes[n]._io) this.nodeStatus(false, n, this.aNodes[n]._ls);
			this.aNodes[n]._io = false;
			this.closeAllChildren(this.aNodes[n]);		
		}
	}
}



// Change the status of a node(open or closed)
dTree.prototype.nodeStatus = function(status, id, bottom) {
	eDiv	= document.getElementById('d' + this.obj + id);
	eJoin	= document.getElementById('j' + this.obj + id);
	if (this.config.useIcons) {
		eIcon	= document.getElementById('i' + this.obj + id);
		eIcon.src = (status) ? this.aNodes[id].iconOpen : this.aNodes[id].icon;
	}
	eJoin.src = (this.config.useLines)?
	((status)?((bottom)?this.icon.minusBottom:this.icon.minus):((bottom)?this.icon.plusBottom:this.icon.plus)):
	((status)?this.icon.nlMinus:this.icon.nlPlus);
	eDiv.style.display = (status) ? 'block': 'none';
};


//改变子节点check状态
dTree.prototype.checkChild = function(nodeId) {
	var curNode = this.aNodes[nodeId];
	var isChecked = document.getElementById('cb' + this.obj + nodeId).checked;
	var n = 0;
	for (n; n<this.aNodes.length; n++) {
		var cn = this.aNodes[n];
		if(cn.pid == curNode.id){
			document.getElementById('cb' + this.obj + cn._ai).checked = isChecked;
			if(cn._hc){ this.checkChild(cn._ai);}
			if(cn._ls){ break; }
		}
	}
}


// [Cookie] Clears a cookie
dTree.prototype.clearCookie = function() {
	var now = new Date();
	var yesterday = new Date(now.getTime() - 1000 * 60 * 60 * 24);
	this.setCookie('co'+this.obj, 'cookieValue', yesterday);
	this.setCookie('cs'+this.obj, 'cookieValue', yesterday);
};


// [Cookie] Sets value in a cookie
dTree.prototype.setCookie = function(cookieName, cookieValue, expires, path, domain, secure) {
	document.cookie =
		escape(cookieName) + '=' + escape(cookieValue)
		+ (expires ? '; expires=' + expires.toGMTString() : '')
		+ (path ? '; path=' + path : '')
		+ (domain ? '; domain=' + domain : '')
		+ (secure ? '; secure' : '');
};


// [Cookie] Gets a value from a cookie
dTree.prototype.getCookie = function(cookieName) {
	var cookieValue = '';
	var posName = document.cookie.indexOf(escape(cookieName) + '=');
	if (posName != -1) {
		var posValue = posName + (escape(cookieName) + '=').length;
		var endPos = document.cookie.indexOf(';', posValue);
		if (endPos != -1) cookieValue = unescape(document.cookie.substring(posValue, endPos));
		else cookieValue = unescape(document.cookie.substring(posValue));
	}
	return (cookieValue);
};


// [Cookie] Returns ids of open nodes as a string
dTree.prototype.updateCookie = function() {
	var str = '';
	for (var n=0; n<this.aNodes.length; n++) {
		if (this.aNodes[n]._io && this.aNodes[n].pid != this.root.id) {
			if (str) str += '.';
			str += this.aNodes[n].id;
		}
	}
	this.setCookie('co' + this.obj, str);
};


// [Cookie] Checks if a node id is in a cookie
dTree.prototype.isOpen = function(id) {
	var aOpen = this.getCookie('co' + this.obj).split('.');
	for (var n=0; n<aOpen.length; n++)
		if (aOpen[n] == id) return true;
	return false;
};

// 初始化树中的checkbox
dTree.prototype.setChecked = function(values) {
	var checkboxs = document.getElementsByName("nodes"+this.obj);
	var selectedValues = values.split(",");
	for(var i = 0; i < checkboxs.length; i++){
		var checkValue = checkboxs[i].value;
		if(checkValue.indexOf("|")!=-1){
			checkValue = checkValue.substring(0,checkValue.indexOf("|"));
		}
		for(var j = 0; j < selectedValues.length; j++){
			if(checkValue==selectedValues[j]){
				checkboxs[i].checked=true;
				break;
			}
		}
	}
};


// If Push and pop is not implemented by the browser
if (!Array.prototype.push) {
	Array.prototype.push = function array_push() {
		for(var i=0;i<arguments.length;i++)
			this[this.length]=arguments[i];
		return this.length;
	}
};
if (!Array.prototype.pop) {
	Array.prototype.pop = function array_pop() {
		lastElement = this[this.length-1];
		this.length = Math.max(this.length-1,0);
		return lastElement;
	}
};