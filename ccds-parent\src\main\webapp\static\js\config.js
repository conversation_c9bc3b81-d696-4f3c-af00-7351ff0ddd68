var VER_ID=0;
var SYS_MOD = null;//[0]模块ID,[1]模块名称
var SYS_TYPE_ENTERPRISE = "E",SYS_TYPE_GROUP ="E1",SYS_TYPE_PROJ ="E2",SYS_TYPE_LOAN ="E3";
var F_PR_REPORT = Math.pow(2, 0), F_EXT_INF = Math.pow(2, 1), F_LAW = Math.pow(2, 2), F_APP = Math.pow(2, 3);//F_ITL = Math.pow(2, 4);

/*
	初始化版本号
*/
function initVer(verId){
	VER_ID = verId;
}
/*
	初始化系统功能模块
*/
function initSysMod(modIds,modNames,verId,regType,hasBFMod,regFunctions){
	if(SYS_MOD==null&&modIds!=undefined&&modIds!=""){
		SYS_MOD = [];
		SYS_MOD[0] = modIds.split(",");
		SYS_MOD[1] = modNames.split(",");
		VER_ID = verId;
		if(!canShowLAWMod(regType,regFunctions)){
			for(var i=0; i<SYS_MOD[0].length; i++){
				if(SYS_MOD[0][i]=='law'){
					SYS_MOD[0].splice(i,1);
					SYS_MOD[1].splice(i,1);
					break;
				}
			}
		}
		if(hasBFMod != "TRUE" && hasBFMod != "true"){
			for(var i=0; i<SYS_MOD[0].length; i++){
				if(SYS_MOD[0][i]=='rec'){
					SYS_MOD[0].splice(i,1);
					SYS_MOD[1].splice(i,1);
					break;
				}
			}
		}
	}
}

function canShowEXIMod(regType,regFunctions){
	var canShowEXI = false;
	if(regFunctions != ''){
		if( (parseInt(regFunctions)&F_EXT_INF) != 0){
			canShowEXI = true;	
		}
	}
	else if(regType!=''&&regType.substring(0,1)==SYS_TYPE_ENTERPRISE){
		canShowEXI = true;
	}
	return canShowEXI;
}
function canShowEXPMod(regType,regFunctions){
	var canShowEXP = false;
	if(regFunctions != ''){
		if( (parseInt(regFunctions)&F_PR_REPORT) != 0){
			canShowEXP = true;	
		}
	}
	else if(regType!=''&&regType.substring(0,1)==SYS_TYPE_ENTERPRISE){
		canShowEXP = true;
	}
	return canShowEXP;
}
function canShowLAWMod(regType,regFunctions){
	var canShowLAW = false;
	if(regFunctions != ''){
		if( (parseInt(regFunctions)&F_LAW) != 0){
			canShowLAW = true;	
		}
	}
	else if(regType!=''&&regType.substring(0,1)==SYS_TYPE_ENTERPRISE){
		canShowLAW = true;
	}
	return canShowLAW;
}
function canShowAPPMod(regType,regFunctions){
	var canShowAPP = false;
	if(regFunctions != ''){
		if( (parseInt(regFunctions)&F_APP) != 0){
			canShowAPP = true;	
		}
	}
	else if(regType!=''&&(regType==SYS_TYPE_GROUP||regType==SYS_TYPE_PROJ||regType==SYS_TYPE_LOAN)){
		canShowAPP = true;
	}
	return canShowAPP;
}
function canShowRecycleMod(regType,regFunctions){
	var canShowRecycle = false;
	if(regFunctions != ''){
		if( (parseInt(regFunctions)&F_PR_REPORT) != 0){
			canShowRecycle = true;	
		}
	}
	else if(regType!=''&&(regType==SYS_TYPE_GROUP||regType==SYS_TYPE_PROJ||regType==SYS_TYPE_LOAN)){
		canShowRecycle = true;
	}
	return canShowRecycle;
}
/*
function canShowAdvStatMod(regType){
	if(regType!=''&&(regType==SYS_TYPE_GROUP||regType==SYS_TYPE_PROJ||regType==SYS_TYPE_LOAN)){
		return true;
	}
	else{
		return false;
	}
}
*/
/************ updatePwd.jsp ********************/
function showLocalPhoLayer(hasCTI){
	if(hasCTI=="1"){
		$("setLocalPhoLayer").show();
		//loadLocalPhoAndIP();
	}
}

/************ limRight ********************/
var RIG_MOD_MAPPING = [["bankCase","case"],["phoCl","hurry"],["ass","cp"],["vis","vis"],["stat","sta"],["oa","oa"],["hr","hr"],["loan","loan"],["cus","cus"],["sal","sal"],["law","law"],["sys","sys"],["rec","rec"]];//模块对应权限类别(模块ID+权限类别)
function loadRightMenu(regType,regFunctions){
	var rightMenu = [];
	if(SYS_MOD!=null){
		for(var i=0;i<SYS_MOD[0].length;i++){
			for(var j=0;j<RIG_MOD_MAPPING.length;j++){
				if(RIG_MOD_MAPPING[j][0]==SYS_MOD[0][i]){
					rightMenu.push([SYS_MOD[1][i],RIG_MOD_MAPPING[j][1]]);
					break;
				}
			}
		}
	}
	if(canShowAPPMod(regType,regFunctions)){
		rightMenu.push(["移动端","mob"]);
	}
	return rightMenu;
}

/************ export ****************/
function getOutDataInitCol(type,verId){
	var initCol = [];
	switch(type){
		case "PR":
			if(verId=="8"){
				initCol[0] = ["工商银行","中国银行","建设银行"];
				initCol[1] = [
					[1,11,12,13],
					[11,14,2,12,0,9,10,15,16,13],
					[1,2,4,5,7,8,11,12,13,14]
				];
			}
			break;
	}
	return initCol;
}

/*function expCRecOfCase(verId,casId){
	if(verId=="1"){
		casePopDiv(51,casId);
	}
	else{
    	self.location.href="caseAction.do?op=outRedList&casId="+casId;
	}	
}*/

function getCasNameTxt(verId){
	return "姓名";
}
function getBankTxt(verId){
		return "委托方";
}
function getRem8Txt(verId){
		return "商户";
}
function getCasCaCdTxt(verId){
	return "卡号";
}
/*function getManagerAssTxt(verId){
	if(verId=="95"){
		return "暂住证查询";
	}
	else{
		return "主管协催";
	}
}*/
function getTipsDateTxt(verId){
	if(verId=="18"){
		return "行动日期";
	}
	else{
		return "下次跟进日期";
	}
}

/**
 * 是否有案件录音查询
 * @param verId
 * @returns {Boolean} 默认false 
 */
function hasCaseRecordBtn(verId){
	if(verId=='477'){
		return true;
	}
	else{
		return false;
	}	
}

/**
 * 是否加载同批次共债催记
 * @param verId
 * @returns {Boolean} 默认true
 */
function isShowOthPrInDesc(verId){
	if(verId=='492'){
		return false;
	}
	else{
		return true;
	}	
}

/**
 * 是否加载天气模块
 * @param verId
 * @returns {Boolean} 默认true
 */
function isShowWeather(verId){
	var isShow = true;
	switch(verId){
		case "10":
		case "36":
		case "151":
		case "177":
		case "233":
			isShow = false;
			break;
	}
	return isShow;
}

/**
 * 是否有证件号认证模块(ZNHY)
 * @param verId
 * @returns {Boolean} 默认false
 */
function hasIDCheckService(verId){
	if(verId=='260'){
		return true;
	}
	else{
		return false;
	}	
}

/**
 * 是否连接FTP服务器(SHBST)
 * @param verId
 * @returns {Boolean} 默认false
 */
function hasFTPServer(verId){
	if(verId=='477'){
		return true;
	}
	else{
		return false;
	}	
}
/**
 * 生成ftp链接(SHBST)
 */
function linkFTPServer(bankName,batCode,casCode){
	if(bankName!=''&&batCode!=''&casCode!=''){
		window.open('******************************************/'+encodeURIComponent(bankName)+"/"+encodeURIComponent(batCode)+"/"+encodeURIComponent(casCode));
	}
	else{
		alert("操作失败，案件委托方/批次号/个案序列号中有空值");
	}
}

/**
 * 隐藏证件号配置启用时是否隐藏证件号中四位(默认隐藏后四位)
 * @param verId
 * @returns {Boolean} 默认false
 */
function isHideMidOfIDNumber(verId){
	if(verId=='95'){
		return true;
	}
	else if(verId=='325'){
		return true;
	}
	else{
		return false;
	}	
}

/**
 * 拨号按钮旁是否显示挂断按钮
 * @param verId
 * @returns {Boolean} 默认false
 */
function isShowHungUp(verId){
	if(verId == '160'){
		return true;
	}
	else{
		return false;
	}
}

/**
 * 催收记录姓名是否必填(默认不必填)
 * @param verId
 * @returns {Boolean} 默认false
 */
function isPrNameRequired(verId){
	var isReuqired;
	switch(verId){
		case "36":
		case "74":
		case "82":
			isRequired = true;
			break;
		default:
			isRequired = false;
	}
	return isRequired;
}
/**
 * 催收记录关系是否必填(默认不必填)
 * @param verId
 * @returns {Boolean} 默认false
 */
function isPrRelRequired(verId){
	var isReuqired;
	switch(verId){
		case "35":
		case "36":
		case "74":
		case "82":
		case "95":
			isRequired = true;
			break;
		default:
			isRequired = false;
	}
	return isRequired;
}
/**
 * 催收记录谈判方式是否必填(默认不必填)
 * @param verId
 * @returns {Boolean} 默认false
 */
function isPrNegTypeRequired(verId){
	var isReuqired;
	switch(verId){
		case "74":
		case "403":
			isRequired = true;
			break;
		default:
			isRequired = false;
	}
	return isRequired;
}
/**
 * 催收结果是否必填(默认必填)
 * @param verId
 * @returns {Boolean} 默认true
 */
function isContactCatRequired(verId){
	var isReuqired;
	switch(verId){
		case "134":
		case "182":
		case "194":
		case "349":
		case "483":
			isRequired = false;
			break;
		default:
			isRequired = true;
	}
	return isRequired;
}
/**
 * 下次跟进日是否必填(默认不必填)
 * @param verId
 * @returns {Boolean} 默认false
 */
function isNxtTipsDateRequired(verId){
	var isReuqired;
	switch(verId){
		case "2":
		case "25":
		case "35":
		case "53":
		case "62":
		case "67":
		case "80":
		case "82":
		case "233":
		case "333":
		case "505":
			isRequired = true;
			break;
		default:
			isRequired = false;
	}
	return isRequired;
}
/**
 * 下次跟进日是否限制在7天内
 * @param verId
 * @returns {Boolean} 默认false
 */
function isNxtDateNotOverWeek(verId){
	var rs;
	switch(verId){
		case "2":
		case "35":
		case "53":
		case "67":
		case "80":
		case "256":
		case "270":
		case "333":
		case "485":
		case "496":
		case "541":
			rs = true;
			break;
		default:
			rs = false;
	}
	return rs;
}

/**
 * 设置下次跟进日是否需要必填及选择范围
 */
function setNxtDateStyle(obj,verId){
	if(isNxtTipsDateRequired(verId)&&$("nxtDateRed")!=null){
		if($("csTypeDesc"+obj.value)==null||($("csTypeDesc"+obj.value).value!="lock_stop_cl"&&$("csTypeDesc"+obj.value).value!="lock_back_case")){
			$("nxtDateRed").show();
			$("nxtTipsDateTh").className = "required";
		}
		else{
			$("nxtDateRed").hide();
			$("nxtTipsDateTh").className = "";
			$("nxtTipsDate").value="";
		}
	}
	if(isNxtDateNotOverWeek(verId)){
		$("nxtTipsDate").onclick=function(){ WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-{%d}',maxDate:'%y-%M-{%d+7}'}); }
	}
	else{
		$("nxtTipsDate").onclick=function(){ WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-{%d}',maxDate:'%y-%M-{%d+30}'});}
	}
}
/**
 * 下次跟进日初始值是否为当天日期
 * 默认false
 */
function isNxtTipsDateDfToday(verId){
	if(verId=="2"){
		return true;
	}
	else{
		return false;
	}	
}
/**
 * 加载BJBR字段 
 * @param verId
 * @returns {Boolean} 默认false
 */
function isHasExtraPrField(verId){
	if(verId=="403"){
		return true;
	}
	else{
		return false;
	}
}
function resetCpTimeFormat(verId, cpTimeId){
	if(verId=="403"){
		$(cpTimeId).onclick=function(){ WdatePicker({dateFmt:'yyyy-MM-dd H:m:s'}); };
	}
}
function getCpTime(verId, cpTime){
	if(verId=="403"){
		return formatTime(cpTime);
	}
	else{
		return formatDate(cpTime);
	}
}


function loadImpCaseType(verId){
	if(verId=="477"){
		$("fileType").add(new Option("车险案件模板","carClaim"));
		$("fileType").add(new Option("阳光保险案件模板","sins"));
	}
	else{
		$("fileType").add(new Option("车贷案件模板","car"));
		$("fileType").add(new Option("CGI案件模板","PA"));
		if(verId == "313" || verId == "53" ){
			$("fileType").add(new Option("招商SHEET1","cmbs1"));
			$("fileType").add(new Option("招商SHEET2","cmbs2"));
		}
	}
}

/**
 * 我的案件 当日电催跟进量，是否增加短信量统计
 * @param verId
 * @returns {Boolean} 默认false
 */
function isHasSmsPrStat(verId){
	if(verId=="477"){
		return true;
	}
	else{
		return false;
	}
}

/**
 * 录催记是否默认更新同批次共债案件催收状态
 * 默认true
 */
function isUpdStateOfOth(verId){
	if(verId=="2" || verId=="63"){
		return false;
	}
	else{
		return true;
	}	
}
