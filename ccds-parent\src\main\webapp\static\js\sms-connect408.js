function execSend(){
	if($("msgContent").value!=""){
		waitSubmit("save");
		waitSubmit("doCancel");
		var url="connectSMS.do";
		$("op").value="connectToZGDX";
		var args= $("sendForm").serialize(true);
		args.zh="";
		args.mm="";
		args.serverIP="www.810086.com.cn/jk.aspx";
		args.smstype="";
		new Ajax.Request(url,{
			method		:	'post',
			parameters	:	args,
			onSuccess	:	function(response){
				var rs = response.responseText;
				var rsMsg = "";
				if(rs.indexOf(":")!=-1){
					var rsArr = rs.split(':');
					switch(rsArr[0]){
					case '0': rsMsg = "发送成功"; break;
					case '-1':
					case '-2':
					case '-8':
						 rsMsg ="发送失败:"+rsArr[1];
						 break;
					}
				}
				else{
					switch(rs){
					case '-2':  rsMsg ="发送失败:余额不足"; break;
					case '-3':  rsMsg ="发送失败:账户对应的通道余额不足"; break;
					case '1': rsMsg ="发送失败:服务器异常"; break;
					default: rsMsg = rs;
					}
				}
				$("rsLayer").innerHTML = rsMsg+"<div class='deepBlue'>[<a href='javascript:void(0)' onclick='cancel()'>请点此关闭</a>]</div>";
				$("rsLayer").show();
				//setTimeout("cancel()",3000);
			},
			onfailure 	: 	function(response){
				if (transport.status == 404)
					alert("您访问的url地址不存在！");
				else
					alert("Error: status code is " + transport.status);
			}
		});
	}
	else{
		alert("未填写短信内容！");
	}
}