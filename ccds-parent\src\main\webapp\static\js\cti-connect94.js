function connectCTI(phone){
	var url = "connectCTI.do?op=connectToLbox";
	var pars = [];
	pars.vd = "callauto";
	pars.phone = phone;
	pars.ch = "-1";
	//pars.oper = localNo;
	pars.reqType = "0";
	//pars.serverIP = serverIP;
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var xmlData = response.responseXML;
			var rsEl = xmlData.getElementsByTagName("result")[0];
			var sucRs = rsEl.getAttribute("msg");
			if(sucRs==null){
				alert(rsEl.getAttribute("error"));
			}
			else{
				alert(sucRs);
			}
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}
/*
function connectCTI(phone,localNo,serverIP){
	var url = "http://"+serverIP+"/v/vack";
	var pars = [];
	pars.vd = "callauto";
	pars.phone = phone;
	pars.ch = "-1";
	pars.oper = localNo;
	pars.reqType = "0";
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var xmlData = response.responseXML;
			alert(xmlData);
			var rsEl = xmlData.getElementsByTagName("result")[0];
			var sucRs = rsEl.getAttribute("msg");
			if(sucRs==null){
				alert(rsEl.getAttribute("error"));
			}
			else{
				alert(sucRs);
			}
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}
*/