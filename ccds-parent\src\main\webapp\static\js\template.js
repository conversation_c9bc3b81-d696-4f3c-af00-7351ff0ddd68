/* 
	弹出层
*/
function tmpPopDiv(n,arg){
	var width;
	var height;
	var idPre="";
	var titleTxt="";
	var tips="（带<span class='red'>*</span>为必填字段）";
	switch(n){	
		case 1:
			titleTxt="添加模板";
			url="templateAction.do?op=toSaveTemplate&tmpType="+arg;
			width=790;
			height=780;
			break;
		case 11:
			titleTxt="修改模板";
			url="templateAction.do?op=toUpdTemplate&tmpId="+arg;
			width=790;
			height=780;
			break;
	}
	createPopWindow(idPre,titleTxt,url,width,height);
}

function tmpDelDiv(n,id,isIfrm){
	var width;
	var height;
	var idPre="";
	var titleTxt="";
	var url;
	
	switch(n){
		case 1:
			titleTxt="删除模板";
			break;
	}
	createConfirmWindow(idPre,titleTxt,url,width,height,id,n,isIfrm);
}
//返回删除实体名称，url数组
function getDelObj(delType,id){
	var delObj = new Array();
	switch(parseInt(delType)){
		case 1:
			delObj[0]="该模板";
			delObj[1]="templateAction.do?op=delTemplate&tmpId="+id;
			break;
	}
	return delObj;
}