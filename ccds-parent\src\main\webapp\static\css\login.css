body{
	background:url(../images/login/normal/loginbg.gif) 0 40px repeat-x;
}

a:link,a:visited {
	text-decoration:none;
	color:#999;
}
a:hover {
	text-decoration:underline;
	color:#666;
}
a:active {
	text-decoration:none;
	color:#666;
}

#contentbox{
	margin-left: auto;
   	margin-right: auto;
	width:800px;
	height:580px;
	/*border:#666 2px solid;*/
}

#logo {
	/*font-size:16px;
	padding:100px 0 0 350px;*/
	height:190px;
	background:url(../images/login/normal/login_logo.png) 0 67px no-repeat;
}
#logo a{
	font-size:14px;
}
#loginLayer {
	margin-left: auto;
   	margin-right: auto;
	text-align:center;
	height:300px;
}

#leftLayer {
	float:left;
	width:400px;
	height:260px;
	padding:8px 0 0 0;
	color:#333;
	text-align:left;
}

#leftInf{
	padding:0;
	margin:0;
	list-style:none;
}
#leftInf li {
	padding-top:10px;
	padding-bottom:10px;
	height:60px;
	color:#151b58;
	font-size:18px; 
}

#loginForm,#regForm{
	float:left;
	/*height:280px;
	width:310px;
	padding:45px 10px 0 70px;*/
	height:300px;
	width:350px;
	padding:35px 10px 0 50px;
	text-align:left;
	border-left:#CCC 1px solid;
}
.loginInpName {
	font-size:16px;
	color:#151b58;
	width:80px;
	padding: 0 10px 0 0;
}
.loginInpRow {
	padding:0 0 25px 0;
	text-align:right;
	width:290px;
}

#txt_regName , #txt_regCode{
	/*font-weight:bold;*/
	font-size:18px;
	color:#151b58;
}
/*
#txt_loginName, #txt_paswd ,#txt_regName , #txt_regCode{
	height:20px;
}
#txt_loginName {
	background:url(../images/login/normal/login_name.gif) no-repeat;
}
#txt_paswd {
	background:url(../images/login/normal/login_pwd.gif) no-repeat;
}
#txt_regName {
	background:url(../images/login/normal/reg_name.gif) no-repeat;
}
#txt_regCode {
	background:url(../images/login/normal/reg_code.gif) no-repeat;
}*/
#loginName,#pwd {
	/*margin:0 0 0 5px;
	padding:5px;*/
	width:200px;
}
#captchaLayer {
	width:84px;
}
#loginName input,#pwd input, #captchaLayer input {
	background:none;
	border:none;
	font-size:16px;
	width:100%;
	/*height:20px;*/
}
#regName,#regcode {
	margin:5px 0px 18px 0px;
	/*padding:5px;*/
	width:220px;
}
#regName input,#regcode input {
	background:none;
	width:100%;
	border:none;
	font-size:16px;
	/*height:20px;*/
}

#loginBtnLayer {
	padding:20px 0 0 90px;
}

#loginBut, #regBut{
	margin:0 0 5px 0;
	width:100px;
	height:30px;
	font-size:18px;
	font-weight:bold;
	text-align:center;
}

.redInput {
	border:#ff0000 1px solid;
	background-color:#fde9e4;
}
.orgInput {
	border:#fa9512 1px solid;
	background-color:#fdf8df;
}
.grayInput {
	border:#354c9a 1px solid;
	background-color:#fff;
}
#loginTips,#regTips,#submitTips1,#submitTips2 {
	margin-top:4px;
	padding:4px;
	height:auto;
	width:100%;
	font-size:12px;
	background-color:#ff4800;
	color:#fff;
}
#submitTips1,#submitTips2 {
	background-color:#215aca;
	color:#fff;
}

#botTips {
	color:#bebebe;
	text-align:right;
	padding:10px 0 10px 0;
}
#botInf {
	padding:14px 115px 10px 0;
	color:#999999;
	font-size:12px;
	text-align:right; 
	/*line-height:18px;*/
	background:url(../images/login/frsoftlogo.png) right 15px no-repeat;
}

#captchaImgLayer {
	padding:8px 0 0 0;
}
#captchaImg{
	width:80px; 
	height:22px;
}