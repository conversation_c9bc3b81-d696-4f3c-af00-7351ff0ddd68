package com.frsoft.ccds.system.service;

import com.frsoft.ccds.system.domain.PhoRed;

import java.util.List;

/**
 * 催收记录Service接口
 * 
 * <AUTHOR>
 */
public interface IPhoRedService {
    
    /**
     * 查询催收记录
     * 
     * @param prId 催收记录主键
     * @return 催收记录
     */
    PhoRed selectPhoRedByPrId(Long prId);
    
    /**
     * 查询催收记录列表
     * 
     * @param phoRed 催收记录
     * @return 催收记录集合
     */
    List<PhoRed> selectPhoRedList(PhoRed phoRed);
    
    /**
     * 新增催收记录
     * 
     * @param phoRed 催收记录
     * @return 结果
     */
    int insertPhoRed(PhoRed phoRed);
    
    /**
     * 修改催收记录
     * 
     * @param phoRed 催收记录
     * @return 结果
     */
    int updatePhoRed(PhoRed phoRed);
    
    /**
     * 批量删除催收记录
     * 
     * @param prIds 需要删除的催收记录主键集合
     * @return 结果
     */
    int deletePhoRedByPrIds(Long[] prIds);
    
    /**
     * 删除催收记录信息
     * 
     * @param prId 催收记录主键
     * @return 结果
     */
    int deletePhoRedByPrId(Long prId);
    
    /**
     * 根据案件ID查询催收记录列表
     * 
     * @param casId 案件ID
     * @return 催收记录列表
     */
    List<PhoRed> selectPhoRedListByCasId(Long casId);
    
    /**
     * 根据员工编号查询催收记录列表
     * 
     * @param seNo 员工编号
     * @return 催收记录列表
     */
    List<PhoRed> selectPhoRedListBySeNo(Long seNo);
    
    /**
     * 统计催收记录总数
     * 
     * @param phoRed 查询条件
     * @return 催收记录总数
     */
    int countPhoRed(PhoRed phoRed);
}
