function connectCTI(phone){
	var url = "connectCTI.do?op=connectToUncall";
	var pars = [];
	pars.phone = phone;
	//pars.localNo = localNo;
	pars.serverIP = "*************";
	pars.actionID = "1";
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var xmlData = response.responseXML;
			var rs = xmlData.getElementsByTagName("result")[0].childNodes[0].nodeValue;
			if(rs==1){
				var resp = xmlData.getElementsByTagName("Response")[0].childNodes[0].nodeValue;
				switch(resp){
				case 'Success': 
				case 'success':
					alert("拨号成功！"); break;
				case 'Ringing': alert("拨号失败！振铃中"); break;
				case 'Busy': alert("拨号失败！不可用"); break;
				case 'Connection Failed': alert("拨号失败！连接PBX 失败"); break;
				default:	alert("拨号失败！"+resp);
				}
			}
			else{
				alert("调用失败！"+rs);
			}
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}