function connectCTI(phone){
	var url = "connectCTI.do?op=connectToRMLXServie";
	var pars = [];
	pars.server = "210.83.80.188:8085";
	pars.context = "from-internal-6";
	pars.phone = phone;
	
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var rs = response.responseText;
			if(rs != "") {
				var rsJSON = rs.evalJSON();
				var response = rsJSON.root.row[0].Response;
				if(response == "Success"){
					alert("拨号成功！");
				}
				else{
					alert("拨号失败！[错误码:"+ rsJSON.root.row[0].errcode + "," + rsJSON.root.row[0].errmsg + "]");
				}
			}
			else {
				alert("拨号失败！");
			}
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}