var CK_NAME = "localPhoNum";

function initOCXMod(sysType){
	if(getLocalPhoNum()!=""&&sysType=="CL"){
		parent.indexFrmSet.rows="84,*,50";
		parent.ocxFrame.location="userAction.do?op=toOCXFrame&orgCodes=|O20101220-1&servIps=************|************";
		
	}
}
function initOCXBtn(btnId){
	if(getLocalPhoNum()!=""){
		document.getElementById(btnId).style.display="";
	}
}

function connectOCXPho(phoNum){
	if(phoNum!=undefined&&phoNum!=""){
		var iframeObj = parent.opener.parent.document.getElementById("ocxFrame").contentWindow;
		iframeObj.toDial(phoNum);
	}
	else{
		if(document.getElementById("phnTxt").style.display!="none"){
			phoNum = document.getElementById("phnTxt").innerText;
		}
		else{
			phoNum = document.getElementById("phoneNum").value;
		}
		if(phoNum!=""){
			var iframeObj = opener.parent.document.getElementById("ocxFrame").contentWindow;
			iframeObj.toDial(phoNum);
		}
	}
}

function saveLocalPhoNum(obj){
	if(document.getElementById("localPhoNum")!==null){
		waitSubmit(obj);
		var localPhoNum = document.getElementById("localPhoNum").value;
		if(localPhoNum!==""){
			setLocalPhoNum(localPhoNum);
		}
		else{
			setLocalPhoNum("");
		}
		alert("保存成功");
		restoreSubmit(obj);
	}
}
function loadLocalPhoNum(){
	if(document.getElementById("localPhoNum")!==null){
		document.getElementById("localPhoNum").value = getLocalPhoNum();
	}
}

function setLocalPhoNum(localPhoNum){
	saveCk(CK_NAME, localPhoNum, 24*30);
}
function getLocalPhoNum(){
	var lpn = readCk(CK_NAME);
	if(lpn==undefined||lpn==null){
		lpn="";
	}
	return lpn;
}