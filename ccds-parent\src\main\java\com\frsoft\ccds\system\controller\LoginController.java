package com.frsoft.ccds.system.controller;

import com.frsoft.ccds.common.core.controller.BaseController;
import com.frsoft.ccds.common.core.domain.AjaxResult;
import com.frsoft.ccds.system.service.IAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

/**
 * 登录控制器
 * 
 * <AUTHOR>
 */
@Controller
public class LoginController extends BaseController {
    
    @Autowired
    private IAuthService authService;
    
    /**
     * 显示登录页面
     */
    @GetMapping({"/", "/login"})
    public String login() {
        return "login";
    }
    
    /**
     * 处理登录请求
     */
    @PostMapping("/doLogin")
    @ResponseBody
    public AjaxResult doLogin(@RequestParam String loginName,
                             @RequestParam String pwd,
                             HttpServletRequest request,
                             HttpServletResponse response) {
        try {
            // 验证用户登录
            AjaxResult loginResult = authService.login(loginName, pwd);

            if (loginResult.isSuccess()) {
                // 将用户信息存储到session
                request.getSession().setAttribute("user", loginResult.get("user"));
                return AjaxResult.success("登录成功");
            } else {
                return loginResult;
            }
        } catch (Exception e) {
            logger.error("登录异常", e);
            return AjaxResult.error("登录失败：" + e.getMessage());
        }
    }
    
    /**
     * 用户登出
     */
    @GetMapping("/logout")
    public String logout(HttpSession session) {
        session.invalidate();
        return "redirect:/login";
    }
    
    /**
     * 主页面
     */
    @GetMapping("/main")
    public String main(Model model, HttpSession session) {
        // 检查用户是否已登录
        Object user = session.getAttribute("user");
        if (user == null) {
            return "redirect:/login";
        }
        
        model.addAttribute("user", user);
        return "main";
    }
    
    /**
     * 桌面页面
     */
    @GetMapping("/desktop")
    public String desktop(Model model, HttpSession session) {
        // 检查用户是否已登录
        Object user = session.getAttribute("user");
        if (user == null) {
            return "redirect:/login";
        }
        
        return "desktop";
    }
}
