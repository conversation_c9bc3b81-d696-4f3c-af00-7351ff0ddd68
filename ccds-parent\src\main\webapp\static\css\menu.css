/*=== 左侧功能菜单 ===*/
#menu {
	margin:0px;
	margin-left:8px;
	padding:0px;
	width:100px;
	height:auto;
	
}
#menuTop {
	margin:0px;
	padding:0px;
	margin-left:0px;
	background:url(../images/menu/menuTop.png) no-repeat;
	height:28px;
	width:100px;
	padding-top:6px;
	padding-left:18px;
	text-align:left;
}
#closeButton {
	position:absolute;
	top:4px;
	left:95px;
	cursor:pointer;
}

#menuList {
	margin:0px;
	margin-left:0px;
	width:100px;
	border:#314563 1px solid;
	border-top:0px;
	border-bottom:0px;
	background-color:#FFFFFF;
}
#menuList ul {
	margin:0px;
	padding:px;
	height:auto;
	list-style:none;
}
#menuList li {
	margin-top:3px;
	padding-top:3px;
	width:85px;
	cursor:pointer;
}
#menuList li img {
	margin-bottom:5px;
}

#menuBottom {
	margin-left:0px;
	background:url(../images/menu/menuBottom.png) no-repeat;
	height:6px;
	width:100px;
}
.divFocus {
	border:#FCB03F 2px solid;
}
.divBlur {
	border:#FFF 2px solid;
}

/* 最小化功能菜单 */
#showMenu {
	display:none;
	margin-top:0px;
	margin-left:7px;
	padding-top:10px;
	text-align:center;
	height:80px;
	width:30px;
	cursor:pointer;
}
.whiteMenu {
	background:#436fc3 url(../images/menu/showMenu2.png) no-repeat;
}
.grayMenu {
	background:#5882d1 url(../images/menu/showMenu.png) no-repeat;
}

