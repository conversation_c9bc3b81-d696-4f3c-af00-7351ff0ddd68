# 催收系统数据库安装说明

## 📋 文件说明

- `mysql57-complete.sql` - 完整的MySQL 5.7兼容数据库脚本（包含表结构、基础数据和测试数据）
- `install.sql` - 简化的安装脚本
- `README.md` - 本说明文件

## 🚀 快速安装

### 方法1：使用完整脚本（推荐）

```bash
# 登录MySQL
mysql -u root -p

# 执行完整脚本
source /path/to/mysql57-complete.sql
```

### 方法2：使用安装脚本

```bash
# 直接执行安装脚本
mysql -u root -p < install.sql
```

### 方法3：命令行一键安装

```bash
# 一键安装（需要输入MySQL密码）
mysql -u root -p -e "source mysql57-complete.sql"
```

## 📊 数据库结构

### 核心表结构

| 表名 | 说明 | 记录数 |
|------|------|--------|
| `sys_user` | 系统用户表 | 9条（1个管理员+8个测试用户） |
| `sal_emp` | 员工表 | 9条 |
| `sal_org` | 组织表 | 6条 |
| `bank_case` | 银行案件表 | 15条测试案件 |
| `pho_red` | 催收记录表 | 15条催收记录 |
| `case_paid` | 还款记录表 | 17条还款记录 |
| `lim_operate` | 操作权限表 | 47条权限 |
| `lim_function` | 功能模块表 | 17个功能模块 |
| `type_list` | 类型字典表 | 30条字典数据 |
| `cus_province` | 省份表 | 32个省份 |
| `cus_city` | 城市表 | 30个城市 |

### 测试数据特点

1. **用户账号**：
   - 管理员：`admin` / `123456`
   - 测试用户：`zhangsan` / `123456`（张三）等8个用户

2. **案件数据**：
   - 15个测试案件，涵盖不同状态
   - 金额范围：5万-12万
   - 不同银行：工行、建行、农行等15家银行
   - 不同逾期天数：23-44天

3. **催收记录**：
   - 每个案件都有对应的催收记录
   - 包含电话、短信等不同联系方式
   - 记录了不同的催收结果

4. **还款记录**：
   - 包含承诺还款和实际还款记录
   - 部分案件已全额还清
   - 部分案件有分期还款计划

## 🔧 兼容性说明

### MySQL 5.7 特性

- 使用 `bigint(20)` 而不是 `bigint`
- 使用 `int(11)` 而不是 `int`
- 使用 `utf8` 字符集而不是 `utf8mb4`
- 兼容旧版本的日期时间函数

### 字段类型说明

- **主键**：`bigint(20) AUTO_INCREMENT`
- **字符串**：`varchar(n)` 根据实际需要设置长度
- **金额**：`decimal(15,2)` 精确到分
- **日期**：`date` 或 `datetime`
- **状态**：`char(1)` 或 `int(11)`

## 📈 数据统计

安装完成后会自动显示以下统计信息：

- 表结构统计
- 基础数据统计
- 业务数据统计
- 案件状态分布
- 员工案件分配情况
- 银行分布统计
- 逾期天数分布

## ⚠️ 注意事项

1. **备份数据**：安装前请备份现有数据库
2. **权限要求**：确保MySQL用户有创建数据库的权限
3. **字符编码**：确保客户端使用UTF-8编码
4. **版本兼容**：专为MySQL 5.7设计，其他版本可能需要调整

## 🔍 验证安装

安装完成后，可以执行以下查询验证：

```sql
-- 检查表数量
SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'ccds';

-- 检查用户数据
SELECT user_login_name, user_name FROM sys_user;

-- 检查案件数据
SELECT cas_code, cas_name, cas_state FROM bank_case LIMIT 5;

-- 检查催收记录
SELECT pr_contact, pr_result FROM pho_red LIMIT 5;
```

## 🎯 下一步

1. 启动Spring Boot应用
2. 访问 `http://localhost:8080`
3. 使用 `admin` / `123456` 登录
4. 开始使用催收系统

## 📞 技术支持

如果遇到问题，请检查：

1. MySQL版本是否为5.7
2. 字符编码是否正确
3. 用户权限是否足够
4. 脚本路径是否正确

---

**祝您使用愉快！** 🎉
