# 催收系统数据库安装说明

## 📋 文件说明

- `mysql57-complete.sql` - **完整的MySQL 5.7兼容数据库脚本（包含所有106张表）**
- `install.sql` - 简化的安装脚本
- `README.md` - 本说明文件

## ✅ 数据库完整性

**重要更新**: 新项目现在包含完整的106张表，与旧项目完全一致！

- **表数量**: 106张表（100%完整）
- **数据兼容**: 从MSSQL完整转换到MySQL 5.7
- **功能保障**: 所有原有功能都能正常运行

## 🚀 快速安装

### 方法1：使用完整脚本（推荐）

```bash
# 登录MySQL
mysql -u root -p

# 执行完整脚本
source /path/to/mysql57-complete.sql
```

### 方法2：使用安装脚本

```bash
# 直接执行安装脚本
mysql -u root -p < install.sql
```

### 方法3：命令行一键安装

```bash
# 一键安装（需要输入MySQL密码）
mysql -u root -p -e "source mysql57-complete.sql"
```

## 📊 数据库结构

### 🎯 完整的106张表结构

**核心业务表 (1-20)**
- `acc_line` - 账户流水表
- `acc_lock` - 账户锁定表
- `acc_trans` - 账户交易表
- `account` - 账户管理表
- `address` - 地址管理表
- `attachment` - 附件管理表
- `bank_case` - 银行案件表(核心)
- `bank_customer` - 银行客户表
- `case_bat` - 案件批处理表
- `case_batch` - 案件批次表
- `case_collection` - 案件收集表
- `case_grp` - 案件组表
- `case_hp` - 案件核查表
- `case_int` - 案件利息表
- `case_paid` - 还款记录表
- `comment` - 评论管理表
- `cus_area` - 地区信息表
- `cus_city` - 城市信息表
- `cus_contact` - 客户联系人表
- `cus_cor_cus` - 客户信息表

**权限管理系统 (21-40)**
- `lim_function` - 功能管理表
- `lim_group` - 组管理表
- `lim_operate` - 操作管理表
- `lim_right` - 权限管理表
- `lim_role` - 角色管理表
- `lim_user` - 用户管理表
- `sal_emp` - 员工管理表
- `sal_org` - 销售组织表
- `type_list` - 系统类型配置表
- 以及其他87张业务表...

### 🔧 系统功能模块

✅ **用户管理系统** - 完整的用户、角色、权限体系
✅ **案件管理系统** - 银行案件的完整生命周期管理
✅ **催收管理系统** - 催收记录、还款记录等核心功能
✅ **客户管理系统** - 客户信息、联系方式、服务管理
✅ **项目管理系统** - 项目创建、跟踪、任务、阶段管理
✅ **销售管理系统** - 销售订单、发票、任务、机会管理
✅ **采购管理系统** - 采购订单、付款计划、发货管理
✅ **仓库管理系统** - 产品、库存、流水、盘点管理
✅ **财务管理系统** - 账户、交易、流水管理
✅ **消息通知系统** - 消息发送、权限控制
✅ **报告系统** - 报告生成、审批流程
✅ **附件管理系统** - 文件上传、存储管理
✅ **日志审计系统** - 用户操作日志记录
✅ **基础数据管理** - 省市区、类型配置等
✅ **贷款管理系统** - 贷款客户、公司、个人管理
✅ **访问记录系统** - 访问记录、分配管理
✅ **供应商管理系统** - 供应商信息、联系人管理

### 测试数据特点

1. **用户账号**：
   - 管理员：`admin` / `123456`
   - 测试用户：`zhangsan` / `123456`（张三）等8个用户

2. **案件数据**：
   - 15个测试案件，涵盖不同状态
   - 金额范围：5万-12万
   - 不同银行：工行、建行、农行等15家银行
   - 不同逾期天数：23-44天

3. **催收记录**：
   - 每个案件都有对应的催收记录
   - 包含电话、短信等不同联系方式
   - 记录了不同的催收结果

4. **还款记录**：
   - 包含承诺还款和实际还款记录
   - 部分案件已全额还清
   - 部分案件有分期还款计划

## 🔧 兼容性说明

### MySQL 5.7 特性

- 使用 `bigint(20)` 而不是 `bigint`
- 使用 `int(11)` 而不是 `int`
- 使用 `utf8` 字符集而不是 `utf8mb4`
- 兼容旧版本的日期时间函数

### 字段类型说明

- **主键**：`bigint(20) AUTO_INCREMENT`
- **字符串**：`varchar(n)` 根据实际需要设置长度
- **金额**：`decimal(15,2)` 精确到分
- **日期**：`date` 或 `datetime`
- **状态**：`char(1)` 或 `int(11)`

## 📈 数据统计

安装完成后会自动显示以下统计信息：

- 表结构统计
- 基础数据统计
- 业务数据统计
- 案件状态分布
- 员工案件分配情况
- 银行分布统计
- 逾期天数分布

## ⚠️ 注意事项

1. **备份数据**：安装前请备份现有数据库
2. **权限要求**：确保MySQL用户有创建数据库的权限
3. **字符编码**：确保客户端使用UTF-8编码
4. **版本兼容**：专为MySQL 5.7设计，其他版本可能需要调整

## 🔍 验证安装

安装完成后，可以执行以下查询验证：

```sql
-- 检查表数量
SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'ccds';

-- 检查用户数据
SELECT user_login_name, user_name FROM sys_user;

-- 检查案件数据
SELECT cas_code, cas_name, cas_state FROM bank_case LIMIT 5;

-- 检查催收记录
SELECT pr_contact, pr_result FROM pho_red LIMIT 5;
```

## 🎯 下一步

1. 启动Spring Boot应用
2. 访问 `http://localhost:8080`
3. 使用 `admin` / `123456` 登录
4. 开始使用催收系统

## 📞 技术支持

如果遇到问题，请检查：

1. MySQL版本是否为5.7
2. 字符编码是否正确
3. 用户权限是否足够
4. 脚本路径是否正确

---

**祝您使用愉快！** 🎉
