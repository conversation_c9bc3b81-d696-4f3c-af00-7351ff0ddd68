<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>催收记录 - 催收管理系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .nav-links {
            display: flex;
            gap: 15px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            background-color: #34495e;
        }
        .nav-links a:hover {
            background-color: #4a6741;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .search-panel {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        .form-group {
            display: flex;
            flex-direction: column;
        }
        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .form-group input, .form-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2980b9;
        }
        .btn-success {
            background-color: #27ae60;
            color: white;
        }
        .btn-success:hover {
            background-color: #229954;
        }
        .table-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .table-header {
            background-color: #34495e;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .table-header h3 {
            margin: 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #555;
        }
        tr:hover {
            background-color: #f8f9fa;
        }
        .record-content {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>催收记录</h1>
        <div class="nav-links">
            <a href="/main">返回主页</a>
            <a href="/case/listCase">案件管理</a>
            <a href="/logout">退出登录</a>
        </div>
    </div>

    <div class="container">
        <!-- 搜索面板 -->
        <div class="search-panel">
            <form class="search-form" id="searchForm">
                <div class="form-group">
                    <label for="casCode">案件编码:</label>
                    <input type="text" id="casCode" name="casCode" placeholder="请输入案件编码">
                </div>
                <div class="form-group">
                    <label for="casName">客户姓名:</label>
                    <input type="text" id="casName" name="casName" placeholder="请输入客户姓名">
                </div>
                <div class="form-group">
                    <label for="prCat">催收措施:</label>
                    <select id="prCat" name="prCat">
                        <option value="">全部措施</option>
                        <option value="1">电话催收</option>
                        <option value="2">短信催收</option>
                        <option value="3">上门催收</option>
                        <option value="4">法律催收</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="empName">催收员:</label>
                    <input type="text" id="empName" name="empName" placeholder="请输入催收员姓名">
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">搜索</button>
                </div>
                <div class="form-group">
                    <button type="button" class="btn btn-success" onclick="resetForm()">重置</button>
                </div>
            </form>
        </div>

        <!-- 催收记录列表 -->
        <div class="table-container">
            <div class="table-header">
                <h3>催收记录列表</h3>
                <div>
                    <span id="totalCount">总计: 0 条</span>
                </div>
            </div>
            
            <div id="loadingDiv" class="loading">
                正在加载数据...
            </div>
            
            <table id="recordTable" style="display: none;">
                <thead>
                    <tr>
                        <th>记录ID</th>
                        <th>案件编码</th>
                        <th>客户姓名</th>
                        <th>催收措施</th>
                        <th>催收时间</th>
                        <th>联系对象</th>
                        <th>联系方式</th>
                        <th>催收内容</th>
                        <th>催收结果</th>
                        <th>催收员</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="recordTableBody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 页面加载时获取催收记录列表
        window.onload = function() {
            loadRecordList();
        };

        // 搜索表单提交
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            loadRecordList();
        });

        // 加载催收记录列表
        function loadRecordList() {
            showLoading();
            
            // 显示模拟数据用于测试
            setTimeout(function() {
                displayMockData();
            }, 500);
        }

        // 显示模拟数据（用于测试）
        function displayMockData() {
            const mockRecords = [
                {
                    prId: 1,
                    casCode: 'CASE001',
                    casName: '张三',
                    prCat: 1,
                    prTime: '2024-06-24 10:30:00',
                    prName: '张三',
                    prContact: '13800138001',
                    prContent: '电话联系客户，客户表示近期会还款',
                    prResult: '承诺还款',
                    empName: '李催收'
                },
                {
                    prId: 2,
                    casCode: 'CASE002',
                    casName: '李四',
                    prCat: 2,
                    prTime: '2024-06-24 14:15:00',
                    prName: '李四',
                    prContact: '13800138002',
                    prContent: '发送短信提醒还款',
                    prResult: '已发送',
                    empName: '王催收'
                },
                {
                    prId: 3,
                    casCode: 'CASE001',
                    casName: '张三',
                    prCat: 3,
                    prTime: '2024-06-23 16:45:00',
                    prName: '张三配偶',
                    prContact: '上门拜访',
                    prContent: '上门拜访，客户不在家，与其配偶沟通',
                    prResult: '联系到家属',
                    empName: '李催收'
                }
            ];
            hideLoading();
            displayRecordList(mockRecords);
            document.getElementById('totalCount').textContent = `总计: ${mockRecords.length} 条`;
        }

        // 显示催收记录列表
        function displayRecordList(records) {
            const tbody = document.getElementById('recordTableBody');
            tbody.innerHTML = '';
            
            if (records && records.length > 0) {
                records.forEach(record => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${record.prId}</td>
                        <td>${record.casCode || ''}</td>
                        <td>${record.casName || ''}</td>
                        <td>${getCatText(record.prCat)}</td>
                        <td>${record.prTime || ''}</td>
                        <td>${record.prName || ''}</td>
                        <td>${record.prContact || ''}</td>
                        <td><div class="record-content" title="${record.prContent || ''}">${record.prContent || ''}</div></td>
                        <td>${record.prResult || ''}</td>
                        <td>${record.empName || ''}</td>
                        <td>
                            <button class="btn btn-primary" onclick="viewRecord(${record.prId})">查看</button>
                            <button class="btn btn-success" onclick="editRecord(${record.prId})">编辑</button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
                document.getElementById('recordTable').style.display = 'table';
            } else {
                tbody.innerHTML = '<tr><td colspan="11" style="text-align: center; padding: 40px; color: #666;">暂无数据</td></tr>';
                document.getElementById('recordTable').style.display = 'table';
            }
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loadingDiv').style.display = 'block';
            document.getElementById('recordTable').style.display = 'none';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loadingDiv').style.display = 'none';
        }

        // 重置表单
        function resetForm() {
            document.getElementById('searchForm').reset();
            loadRecordList();
        }

        // 查看记录详情
        function viewRecord(prId) {
            alert('查看催收记录详情: ' + prId);
        }

        // 编辑记录
        function editRecord(prId) {
            alert('编辑催收记录: ' + prId);
        }

        // 获取催收措施文本
        function getCatText(cat) {
            const catMap = {
                1: '电话催收',
                2: '短信催收',
                3: '上门催收',
                4: '法律催收'
            };
            return catMap[cat] || '其他';
        }
    </script>
</body>
</html>
