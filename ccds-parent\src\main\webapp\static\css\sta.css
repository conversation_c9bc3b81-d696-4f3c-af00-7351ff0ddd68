/*=== 统计 ===*/
#statContainer {
	width:100%;
	height:240px;
}
/* 左侧菜单 */
#leftTd {
	width:140px;
	vertical-align:top;
}
#leftTd iframe{
	width:140px;
}
#rightTd {
	padding:5px;
	vertical-align:top;
}

/* 菜单项样式 */
.statMenuCur, .statMenuOver, .statMenu {
	padding:5px;
	margin:10px 0 0 0; 
	cursor:pointer;
	font-family:"宋体";
	font-size:12px;
}
.statMenuCur {
	background-color:#e9eff6;
	border:#3366cc 1px solid;
	color:#234995;
}
.statMenuOver {
	background-color:#e9eff6;
	border:#a4b0c1 1px solid;
	color:#234995;
}
.statMenu {
	background-color:#f8f8f8;
	border:#cfd2e2 1px solid;
	color:#999;
}
	
/* 图表 */
.rightTitle {
	font-weight:600;
	font-size:14px;
	color:#999;
	padding-top:10px;
	padding-bottom:5px;
}
/* 显示类型 */
.disType {
	text-align:left; 
	padding:5px; 
	padding-top:10px;
}
/* 显示类型 */
.staFormTop {
	background:#2D5188;
	color:#fff;
	text-align:left;
	padding:5px;
	padding-left:10px;
	font-size:14px;
}
.chartDiv {
	text-align:left;
	width:100%;
	height:500px;
}

/*.innerTabHead {
	width:100%;
}
.innerTabHead td{
	width:50%;
	text-align:center !important; 
}*/
