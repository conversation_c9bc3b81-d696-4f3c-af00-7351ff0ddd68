-- 催收系统完整数据库脚本 - MySQL 5.7兼容版本
-- 包含表结构、基础数据和测试数据

-- 删除数据库（如果存在）
DROP DATABASE IF EXISTS `ccds`;

-- 创建数据库
CREATE DATABASE `ccds` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;

USE `ccds`;

-- 系统偏好设置表
CREATE TABLE `sys_pref` (
  `syp_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '偏好ID',
  `syp_name` varchar(100) NOT NULL COMMENT '偏好名称',
  `syp_is_def` tinyint(1) DEFAULT '0' COMMENT '是否默认',
  `syp_is_app` tinyint(1) DEFAULT '0' COMMENT '是否应用',
  `syp_pwd_len` int(11) DEFAULT '6' COMMENT '密码长度',
  `syp_pwd_rule` varchar(10) DEFAULT '0' COMMENT '密码规则',
  `syp_pwd_upd_days` int(11) DEFAULT '0' COMMENT '密码更新天数',
  `syp_login_fail` int(11) DEFAULT '5' COMMENT '登录失败次数',
  `syp_offline_days` int(11) DEFAULT '0' COMMENT '离线天数',
  `syp_has_captcha` tinyint(1) DEFAULT '1' COMMENT '是否有验证码',
  PRIMARY KEY (`syp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统偏好设置表';

-- 系统用户表
CREATE TABLE `sys_user` (
  `user_code` varchar(50) NOT NULL COMMENT '用户编码',
  `user_login_name` varchar(50) NOT NULL COMMENT '登录名',
  `user_pwd` varchar(100) NOT NULL COMMENT '密码',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户姓名',
  `user_isenabled` char(1) DEFAULT '1' COMMENT '是否启用(0否 1是)',
  `user_cti_server` varchar(100) DEFAULT NULL COMMENT 'CTI服务器',
  `user_cti_phone` varchar(20) DEFAULT NULL COMMENT 'CTI电话',
  `user_sms_max_num` int(11) DEFAULT NULL COMMENT '短信最大发送数',
  PRIMARY KEY (`user_code`),
  UNIQUE KEY `uk_user_login_name` (`user_login_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统用户表';

-- 组织表
CREATE TABLE `sal_org` (
  `so_code` varchar(50) NOT NULL COMMENT '组织编码',
  `so_name` varchar(100) NOT NULL COMMENT '组织名称',
  `so_con_area` varchar(100) DEFAULT NULL COMMENT '管辖区域',
  `so_loc` varchar(100) DEFAULT NULL COMMENT '位置',
  `so_user_code` varchar(50) DEFAULT NULL COMMENT '用户编码',
  `so_emp_num` varchar(20) DEFAULT NULL COMMENT '员工数量',
  `so_resp` varchar(200) DEFAULT NULL COMMENT '职责',
  `so_org_code` varchar(50) DEFAULT NULL COMMENT '组织编码',
  `so_remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `so_isenabled` char(1) DEFAULT '1' COMMENT '是否启用(0否 1是)',
  `so_up_code` varchar(50) DEFAULT NULL COMMENT '上级编码',
  `so_cost_center` varchar(50) DEFAULT NULL COMMENT '成本中心',
  `so_org_nature` varchar(50) DEFAULT NULL COMMENT '组织性质',
  PRIMARY KEY (`so_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='组织表';

-- 员工表
CREATE TABLE `sal_emp` (
  `se_no` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '员工编号',
  `se_name` varchar(50) NOT NULL COMMENT '员工姓名',
  `se_user_code` varchar(50) DEFAULT NULL COMMENT '用户编码',
  `se_so_code` varchar(50) DEFAULT NULL COMMENT '组织编码',
  `se_isenabled` char(1) DEFAULT '1' COMMENT '是否启用(0否 1是)',
  `se_phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `se_email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `se_position` varchar(50) DEFAULT NULL COMMENT '职位',
  `se_entry_date` date DEFAULT NULL COMMENT '入职日期',
  PRIMARY KEY (`se_no`),
  KEY `idx_se_user_code` (`se_user_code`),
  KEY `idx_se_so_code` (`se_so_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='员工表';

-- 角色表
CREATE TABLE `lim_role` (
  `rol_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `rol_name` varchar(50) NOT NULL COMMENT '角色名称',
  `rol_lev` int(11) DEFAULT NULL COMMENT '角色级别',
  `rol_desc` varchar(200) DEFAULT NULL COMMENT '角色描述',
  `rol_grp_id` bigint(20) DEFAULT NULL COMMENT '组ID',
  PRIMARY KEY (`rol_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='角色表';

-- 组表
CREATE TABLE `lim_group` (
  `grp_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '组ID',
  `grp_name` varchar(50) NOT NULL COMMENT '组名称',
  `grp_desc` varchar(200) DEFAULT NULL COMMENT '组描述',
  `grp_cre_time` datetime DEFAULT NULL COMMENT '创建时间',
  `grp_cre_man` varchar(50) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`grp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='组表';

-- 操作权限表
CREATE TABLE `lim_operate` (
  `ope_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '操作ID',
  `ope_code` varchar(50) NOT NULL COMMENT '操作编码',
  `ope_desc` varchar(200) DEFAULT NULL COMMENT '操作描述',
  PRIMARY KEY (`ope_id`),
  UNIQUE KEY `uk_ope_code` (`ope_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='操作权限表';

-- 功能模块表
CREATE TABLE `lim_function` (
  `fun_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '功能ID',
  `fun_code` varchar(50) NOT NULL COMMENT '功能编码',
  `fun_desc` varchar(200) DEFAULT NULL COMMENT '功能描述',
  `fun_type` varchar(50) DEFAULT NULL COMMENT '功能类型',
  PRIMARY KEY (`fun_id`),
  UNIQUE KEY `uk_fun_code` (`fun_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='功能模块表';

-- 权限表
CREATE TABLE `lim_right` (
  `rig_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `rig_code` varchar(50) NOT NULL COMMENT '权限编码',
  `rig_fun_code` varchar(50) DEFAULT NULL COMMENT '功能编码',
  `rig_ope_code` varchar(50) DEFAULT NULL COMMENT '操作编码',
  `rig_wms_name` varchar(100) DEFAULT NULL COMMENT 'WMS名称',
  PRIMARY KEY (`rig_id`),
  UNIQUE KEY `uk_rig_code` (`rig_code`),
  KEY `idx_rig_fun_code` (`rig_fun_code`),
  KEY `idx_rig_ope_code` (`rig_ope_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='权限表';

-- 类型字典表
CREATE TABLE `type_list` (
  `typ_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `typ_name` varchar(50) NOT NULL COMMENT '类型名称',
  `typ_desc` varchar(200) DEFAULT NULL COMMENT '类型描述',
  `typ_type` varchar(50) DEFAULT NULL COMMENT '类型分类',
  `typ_isenabled` char(1) DEFAULT '1' COMMENT '是否启用(0否 1是)',
  PRIMARY KEY (`typ_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='类型字典表';

-- 省份表
CREATE TABLE `cus_province` (
  `prv_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '省份ID',
  `prv_area_id` bigint(20) DEFAULT NULL COMMENT '区域ID',
  `prv_name` varchar(50) NOT NULL COMMENT '省份名称',
  `prv_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`prv_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='省份表';

-- 城市表
CREATE TABLE `cus_city` (
  `city_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '城市ID',
  `city_prv_id` bigint(20) DEFAULT NULL COMMENT '省份ID',
  `city_name` varchar(50) NOT NULL COMMENT '城市名称',
  `city_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`city_id`),
  KEY `idx_city_prv_id` (`city_prv_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='城市表';

-- 区域表
CREATE TABLE `cus_area` (
  `are_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '区域ID',
  `are_name` varchar(50) NOT NULL COMMENT '区域名称',
  `are_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`are_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='区域表';

-- 锁表
CREATE TABLE `lock_table` (
  `lock_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '锁ID',
  `table_name` varchar(100) NOT NULL COMMENT '表名',
  `table_max` bigint(20) DEFAULT '0' COMMENT '最大值',
  PRIMARY KEY (`lock_id`),
  UNIQUE KEY `uk_table_name` (`table_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='锁表';

-- 银行案件表
CREATE TABLE `bank_case` (
  `cas_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '案件ID',
  `cas_code` varchar(50) DEFAULT NULL COMMENT '案件编码',
  `cas_group` varchar(50) DEFAULT NULL COMMENT '案件组',
  `cas_state` int(11) DEFAULT '0' COMMENT '案件状态(0待分配 1催收中 2已结案 3暂停催收 4法务处理)',
  `cas_typ_hid` bigint(20) DEFAULT NULL COMMENT '类型ID',
  `cas_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `cas_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `cas_m` decimal(15,2) DEFAULT NULL COMMENT '案件金额',
  `cas_paid_m` decimal(15,2) DEFAULT '0.00' COMMENT '已还金额',
  `cas_se_no` bigint(20) DEFAULT NULL COMMENT '员工编号',
  `cas_ins_time` datetime DEFAULT NULL COMMENT '创建时间',
  `cas_ins_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `cas_alt_time` datetime DEFAULT NULL COMMENT '修改时间',
  `cas_alt_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `cas_remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `cas_id_no` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `cas_address` varchar(200) DEFAULT NULL COMMENT '地址',
  `cas_work_address` varchar(200) DEFAULT NULL COMMENT '工作地址',
  `cas_work_phone` varchar(20) DEFAULT NULL COMMENT '工作电话',
  `cas_email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `cas_bank` varchar(50) DEFAULT NULL COMMENT '银行',
  `cas_card_no` varchar(30) DEFAULT NULL COMMENT '银行卡号',
  `cas_overdue_date` date DEFAULT NULL COMMENT '逾期日期',
  `cas_pback_p` decimal(5,2) DEFAULT NULL COMMENT '回收率',
  `cas_wpost_code` varchar(10) DEFAULT NULL COMMENT '工作邮编',
  `cas_deadline` date DEFAULT NULL COMMENT '截止日期',
  `cas_is_host` char(1) DEFAULT '0' COMMENT '是否主案件',
  `cas_bill_date` date DEFAULT NULL COMMENT '账单日期',
  `cas_last_paid` date DEFAULT NULL COMMENT '最后还款日期',
  `cas_count` int(11) DEFAULT '0' COMMENT '催收次数',
  `cas_left_pri` decimal(15,2) DEFAULT NULL COMMENT '剩余本金',
  `cas_assign_ids` varchar(200) DEFAULT NULL COMMENT '分配ID列表',
  `cas_assign_names` varchar(200) DEFAULT NULL COMMENT '分配姓名列表',
  `cas_last_assign_time` datetime DEFAULT NULL COMMENT '最后分配时间',
  `cas_overdue_days` int(11) DEFAULT '0' COMMENT '逾期天数',
  `cas_overdue_days_str` varchar(20) DEFAULT NULL COMMENT '逾期天数字符串',
  `cas_bir` date DEFAULT NULL COMMENT '生日',
  `cas_mpost_code` varchar(10) DEFAULT NULL COMMENT '邮编',
  `cas_perm_crline` decimal(15,2) DEFAULT NULL COMMENT '永久信用额度',
  `cas_alt_hold` char(1) DEFAULT '0' COMMENT '是否暂停',
  PRIMARY KEY (`cas_id`),
  KEY `idx_cas_code` (`cas_code`),
  KEY `idx_cas_name` (`cas_name`),
  KEY `idx_cas_phone` (`cas_phone`),
  KEY `idx_cas_se_no` (`cas_se_no`),
  KEY `idx_cas_state` (`cas_state`),
  KEY `idx_cas_ins_time` (`cas_ins_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='银行案件表';

-- 催收记录表
CREATE TABLE `pho_red` (
  `pr_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `pr_typ_id` bigint(20) DEFAULT NULL COMMENT '类型ID',
  `pr_contact` varchar(50) DEFAULT NULL COMMENT '联系人',
  `pr_cas_id` bigint(20) NOT NULL COMMENT '案件ID',
  `pr_content` text COMMENT '催收内容',
  `pr_time` datetime DEFAULT NULL COMMENT '催收时间',
  `pr_se_no` bigint(20) DEFAULT NULL COMMENT '员工编号',
  `pr_con_type` varchar(20) DEFAULT NULL COMMENT '联系方式',
  `pr_result` varchar(50) DEFAULT NULL COMMENT '催收结果',
  `pr_next_time` datetime DEFAULT NULL COMMENT '下次联系时间',
  `pr_remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `pr_isdel` char(1) DEFAULT '0' COMMENT '是否删除(0否 1是)',
  PRIMARY KEY (`pr_id`),
  KEY `idx_pr_cas_id` (`pr_cas_id`),
  KEY `idx_pr_se_no` (`pr_se_no`),
  KEY `idx_pr_time` (`pr_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='催收记录表';

-- 还款记录表
CREATE TABLE `case_paid` (
  `pa_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '还款ID',
  `pa_state` int(11) DEFAULT '0' COMMENT '状态(0承诺中 1已还款 2逾期未还 3部分还款)',
  `pa_cas_id` bigint(20) NOT NULL COMMENT '案件ID',
  `pa_ptp_d` datetime DEFAULT NULL COMMENT '承诺还款日期',
  `pa_ptp_num` decimal(15,2) DEFAULT NULL COMMENT '承诺还款金额',
  `pa_paid_date` datetime DEFAULT NULL COMMENT '实际还款日期',
  `pa_paid_num` decimal(15,2) DEFAULT NULL COMMENT '实际还款金额',
  `pa_se_no` bigint(20) DEFAULT NULL COMMENT '员工编号',
  `pa_remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `pa_type` varchar(20) DEFAULT NULL COMMENT '还款类型',
  `pa_method` varchar(20) DEFAULT NULL COMMENT '还款方式',
  `pa_ins_time` datetime DEFAULT NULL COMMENT '创建时间',
  `pa_ins_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `pa_alt_time` datetime DEFAULT NULL COMMENT '修改时间',
  `pa_alt_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  PRIMARY KEY (`pa_id`),
  KEY `idx_pa_cas_id` (`pa_cas_id`),
  KEY `idx_pa_se_no` (`pa_se_no`),
  KEY `idx_pa_paid_date` (`pa_paid_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='还款记录表';

-- ========================================
-- 插入基础数据
-- ========================================

-- 插入系统偏好设置
INSERT INTO `sys_pref` (`syp_name`, `syp_is_def`, `syp_is_app`, `syp_pwd_len`, `syp_pwd_rule`, `syp_pwd_upd_days`, `syp_login_fail`, `syp_offline_days`, `syp_has_captcha`)
VALUES ('系统默认', 1, 1, 6, '0', 0, 5, 0, 1);

-- 插入默认用户（密码：123456）
INSERT INTO `sys_user` (`user_code`, `user_login_name`, `user_pwd`, `user_name`, `user_isenabled`)
VALUES ('admin', 'admin', 'e10adc3949ba59abbe56e057f20f883e', '系统管理员', '1');

-- 插入默认组织
INSERT INTO `sal_org` (`so_code`, `so_name`, `so_con_area`, `so_loc`, `so_user_code`, `so_isenabled`)
VALUES ('ORG001', '总公司', '全国', '总部', 'admin', '1');

-- 插入默认员工
INSERT INTO `sal_emp` (`se_name`, `se_user_code`, `se_so_code`, `se_isenabled`, `se_position`)
VALUES ('系统管理员', 'admin', 'ORG001', '1', '管理员');

-- 插入默认角色
INSERT INTO `lim_role` (`rol_name`, `rol_lev`, `rol_desc`)
VALUES ('系统管理员', 1, '系统管理员角色');

-- 插入默认组
INSERT INTO `lim_group` (`grp_name`, `grp_desc`, `grp_cre_time`, `grp_cre_man`)
VALUES ('管理组', '系统管理组', NOW(), 'admin');

-- 插入区域数据
INSERT INTO `cus_area` (`are_id`, `are_name`, `are_isenabled`) VALUES
(1, '请选择', '1'),
(2, '华北', '1'),
(3, '华东', '1'),
(4, '华南', '1'),
(5, '华中', '1'),
(6, '西北', '1'),
(7, '西南', '1'),
(8, '东北', '1');

-- 插入省份数据
INSERT INTO `cus_province` (`prv_id`, `prv_area_id`, `prv_name`, `prv_isenabled`) VALUES
(1, 1, '请选择', '1'),
(2, 2, '北京市', '1'),
(3, 2, '天津市', '1'),
(4, 2, '河北省', '1'),
(5, 2, '山西省', '1'),
(6, 2, '内蒙古', '1'),
(7, 3, '上海市', '1'),
(8, 3, '江苏省', '1'),
(9, 3, '浙江省', '1'),
(10, 3, '安徽省', '1'),
(11, 3, '福建省', '1'),
(12, 3, '江西省', '1'),
(13, 3, '山东省', '1'),
(14, 4, '广东省', '1'),
(15, 4, '广西省', '1'),
(16, 4, '海南省', '1'),
(17, 5, '河南省', '1'),
(18, 5, '湖北省', '1'),
(19, 5, '湖南省', '1'),
(20, 6, '陕西省', '1'),
(21, 6, '甘肃省', '1'),
(22, 6, '青海省', '1'),
(23, 6, '宁夏', '1'),
(24, 6, '新疆', '1'),
(25, 7, '重庆市', '1'),
(26, 7, '四川省', '1'),
(27, 7, '贵州省', '1'),
(28, 7, '云南省', '1'),
(29, 7, '西藏', '1'),
(30, 8, '辽宁省', '1'),
(31, 8, '吉林省', '1'),
(32, 8, '黑龙江', '1');

-- 插入城市数据（部分主要城市）
INSERT INTO `cus_city` (`city_id`, `city_prv_id`, `city_name`, `city_isenabled`) VALUES
(1, 1, '请选择', '1'),
(2, 2, '东城区', '1'),
(3, 2, '西城区', '1'),
(4, 2, '朝阳区', '1'),
(5, 2, '丰台区', '1'),
(6, 2, '石景山区', '1'),
(7, 2, '海淀区', '1'),
(8, 2, '门头沟区', '1'),
(9, 7, '黄浦区', '1'),
(10, 7, '徐汇区', '1'),
(11, 7, '长宁区', '1'),
(12, 7, '静安区', '1'),
(13, 7, '普陀区', '1'),
(14, 7, '虹口区', '1'),
(15, 8, '南京市', '1'),
(16, 8, '无锡市', '1'),
(17, 8, '徐州市', '1'),
(18, 8, '常州市', '1'),
(19, 8, '苏州市', '1'),
(20, 8, '南通市', '1'),
(21, 9, '杭州市', '1'),
(22, 9, '宁波市', '1'),
(23, 9, '温州市', '1'),
(24, 9, '嘉兴市', '1'),
(25, 9, '湖州市', '1'),
(26, 14, '广州市', '1'),
(27, 14, '深圳市', '1'),
(28, 14, '珠海市', '1'),
(29, 14, '汕头市', '1'),
(30, 14, '佛山市', '1');

-- 插入操作权限数据
INSERT INTO `lim_operate` (`ope_code`, `ope_desc`) VALUES
('case001', '导入案件'),
('case002', '删除批次'),
('case003', '编辑批次'),
('case004', '编辑案件'),
('case005', '退案'),
('case006', '分配案件'),
('case007', '暂停案件'),
('case008', '关闭案件'),
('case009', '恢复案件'),
('case010', '批量评语'),
('case011', '导出案件'),
('case012', '导出催收记录'),
('fun001', '添加权限'),
('fun002', '删除权限'),
('fun003', '修改权限'),
('fun004', '查看详情权限'),
('fun005', '访问权限'),
('fun006', '查看全部'),
('fun007', '管理权限'),
('fun008', '审核权限'),
('fun009', '导入权限'),
('fun010', '添加修改权限'),
('hurr001', '添加评语'),
('hurr002', '添加警告'),
('hurr003', '修改催收小结'),
('hurr004', '修改案件地区'),
('hurr022', '添加电催记录'),
('hurr023', '修改电话'),
('hurr024', '删除电话'),
('hurr025', '修改地址'),
('hurr026', '删除地址'),
('hurr027', '访问操作记录'),
('hurr028', '修改操作记录'),
('hurr029', '删除操作记录'),
('hurr030', '修改预计退案日'),
('hurr031', '添加电话'),
('hurr032', '添加地址'),
('hurr033', '添加辅助催记'),
('hurr034', '添加协催记录'),
('hurr035', '添加案人数据'),
('hurr036', '删除案件附件'),
('hurr037', '访问共债案件'),
('hurr038', '批量标色'),
('hurr039', '发送短信'),
('sys001', '锁定账号'),
('sys002', '查看账号日志'),
('sys003', '删除账号日志'),
('sys004', '设置短信额度'),
('file001', '上传附件'),
('file002', '查看附件');

-- 插入功能模块数据
INSERT INTO `lim_function` (`fun_code`, `fun_desc`, `fun_type`) VALUES
('c000', '访问权限', 'case'),
('c001', '批次管理', 'case'),
('c002', '案件管理', 'case'),
('c003', '催记管理', 'case'),
('c004', '导出', 'case'),
('c005', '案件详情', 'case'),
('hurry000', '访问权限', 'hurry'),
('hurry001', '我的案件', 'hurry'),
('hurry002', '来电查询', 'hurry'),
('hurry003', '主管协催', 'hurry'),
('sys000', '访问权限', 'sys'),
('sys001', '帐号设置', 'sys'),
('sys002', '职位设置', 'sys'),
('sys003', '部门设置', 'sys'),
('sys004', '类别管理', 'sys'),
('sys006', '安全设置', 'sys'),
('sys007', '权限组设置', 'sys'),
('sta000', '访问权限', 'sta'),
('sta001', '统计报表', 'sta'),
('sta002', '绩效统计', 'sta'),
('sta003', '回收率统计', 'sta');

-- 插入类型字典数据
INSERT INTO `type_list` (`typ_name`, `typ_desc`, `typ_type`, `typ_isenabled`) VALUES
('待分配', '案件待分配状态', 'caseState', '1'),
('催收中', '案件催收中状态', 'caseState', '1'),
('已结案', '案件已结案状态', 'caseState', '1'),
('暂停催收', '案件暂停催收状态', 'caseState', '1'),
('法务处理', '案件法务处理状态', 'caseState', '1'),
('电话', '电话联系方式', 'contactType', '1'),
('短信', '短信联系方式', 'contactType', '1'),
('邮件', '邮件联系方式', 'contactType', '1'),
('上门', '上门联系方式', 'contactType', '1'),
('承诺还款', '承诺还款结果', 'callResult', '1'),
('拒绝还款', '拒绝还款结果', 'callResult', '1'),
('无人接听', '无人接听结果', 'callResult', '1'),
('关机', '关机结果', 'callResult', '1'),
('空号', '空号结果', 'callResult', '1'),
('忙音', '忙音结果', 'callResult', '1'),
('工商银行', '中国工商银行', 'caseBank', '1'),
('建设银行', '中国建设银行', 'caseBank', '1'),
('农业银行', '中国农业银行', 'caseBank', '1'),
('中国银行', '中国银行', 'caseBank', '1'),
('交通银行', '交通银行', 'caseBank', '1'),
('招商银行', '招商银行', 'caseBank', '1'),
('民生银行', '民生银行', 'caseBank', '1'),
('光大银行', '光大银行', 'caseBank', '1'),
('华夏银行', '华夏银行', 'caseBank', '1'),
('平安银行', '平安银行', 'caseBank', '1'),
('浦发银行', '浦发银行', 'caseBank', '1'),
('中信银行', '中信银行', 'caseBank', '1'),
('兴业银行', '兴业银行', 'caseBank', '1'),
('广发银行', '广发银行', 'caseBank', '1'),
('邮储银行', '邮储银行', 'caseBank', '1');

-- 插入锁表数据
INSERT INTO `lock_table` (`table_name`, `table_max`) VALUES
('bank_case', 0),
('pho_red', 0),
('case_paid', 0),
('sal_emp', 0),
('sys_user', 0);

-- ========================================
-- 插入测试数据
-- ========================================

-- 插入测试用户（密码都是：123456）
INSERT INTO `sys_user` (`user_code`, `user_login_name`, `user_pwd`, `user_name`, `user_isenabled`) VALUES
('USER001', 'zhangsan', 'e10adc3949ba59abbe56e057f20f883e', '张三', '1'),
('USER002', 'lisi', 'e10adc3949ba59abbe56e057f20f883e', '李四', '1'),
('USER003', 'wangwu', 'e10adc3949ba59abbe56e057f20f883e', '王五', '1'),
('USER004', 'zhaoliu', 'e10adc3949ba59abbe56e057f20f883e', '赵六', '1'),
('USER005', 'sunqi', 'e10adc3949ba59abbe56e057f20f883e', '孙七', '1'),
('USER006', 'zhouba', 'e10adc3949ba59abbe56e057f20f883e', '周八', '1'),
('USER007', 'wujiu', 'e10adc3949ba59abbe56e057f20f883e', '吴九', '1'),
('USER008', 'zhengshi', 'e10adc3949ba59abbe56e057f20f883e', '郑十', '1');

-- 插入测试组织
INSERT INTO `sal_org` (`so_code`, `so_name`, `so_con_area`, `so_loc`, `so_user_code`, `so_isenabled`, `so_up_code`) VALUES
('ORG002', '华东分公司', '华东地区', '上海', 'USER004', '1', 'ORG001'),
('ORG003', '华南分公司', '华南地区', '广州', 'USER005', '1', 'ORG001'),
('ORG004', '华北分公司', '华北地区', '北京', 'USER006', '1', 'ORG001'),
('ORG005', '催收一部', '江苏省', '南京', 'USER007', '1', 'ORG002'),
('ORG006', '催收二部', '浙江省', '杭州', 'USER008', '1', 'ORG002');

-- 插入测试员工
INSERT INTO `sal_emp` (`se_name`, `se_user_code`, `se_so_code`, `se_isenabled`, `se_position`, `se_phone`, `se_email`, `se_entry_date`) VALUES
('张三', 'USER001', 'ORG005', '1', '催收专员', '13800138001', '<EMAIL>', '2024-01-01'),
('李四', 'USER002', 'ORG005', '1', '催收专员', '13800138002', '<EMAIL>', '2024-01-01'),
('王五', 'USER003', 'ORG005', '1', '高级催收专员', '13800138003', '<EMAIL>', '2024-01-01'),
('赵六', 'USER004', 'ORG002', '1', '分公司经理', '***********', '<EMAIL>', '2023-06-01'),
('孙七', 'USER005', 'ORG003', '1', '分公司经理', '***********', '<EMAIL>', '2023-06-01'),
('周八', 'USER006', 'ORG004', '1', '分公司经理', '***********', '<EMAIL>', '2023-06-01'),
('吴九', 'USER007', 'ORG005', '1', '部门主管', '***********', '<EMAIL>', '2023-08-01'),
('郑十', 'USER008', 'ORG006', '1', '部门主管', '***********', '<EMAIL>', '2023-08-01');

-- 插入测试案件
INSERT INTO `bank_case` (
    `cas_code`, `cas_group`, `cas_state`, `cas_name`, `cas_phone`, `cas_m`, `cas_paid_m`,
    `cas_se_no`, `cas_ins_time`, `cas_ins_user`, `cas_id_no`, `cas_address`,
    `cas_email`, `cas_bank`, `cas_card_no`, `cas_overdue_date`, `cas_overdue_days`,
    `cas_work_phone`, `cas_work_address`
) VALUES
('CASE001', 'A组', 1, '陈小明', '***********', 50000.00, 0.00, 2, NOW(), 'admin', '320101199001011234', '江苏省南京市玄武区中山路1号', '<EMAIL>', '工商银行', '622202********90123', '2024-01-15', 30, '025-********', '江苏省南京市建邺区江东中路100号'),
('CASE002', 'A组', 1, '李小红', '***********', 80000.00, 10000.00, 2, NOW(), 'admin', '320101199002021234', '江苏省南京市鼓楼区中央路2号', '<EMAIL>', '建设银行', '622202********90124', '2024-01-10', 35, '025-********', '江苏省南京市秦淮区太平南路200号'),
('CASE003', 'B组', 0, '王小刚', '***********', 120000.00, 0.00, NULL, NOW(), 'admin', '320101199003031234', '江苏省南京市秦淮区太平南路3号', '<EMAIL>', '农业银行', '622202********90125', '2024-01-20', 25, '025-********', '江苏省南京市雨花台区雨花路300号'),
('CASE004', 'B组', 1, '张小丽', '13912345681', 60000.00, 20000.00, 3, NOW(), 'admin', '320101199004041234', '江苏省南京市建邺区江东中路4号', '<EMAIL>', '中国银行', '622202********90126', '2024-01-05', 40, '025-12345681', '江苏省南京市栖霞区仙林大道400号'),
('CASE005', 'C组', 2, '刘小强', '13912345682', 90000.00, 90000.00, 4, NOW(), 'admin', '320101199005051234', '江苏省南京市雨花台区雨花路5号', '<EMAIL>', '交通银行', '622202********90127', '2024-01-01', 44, '025-12345682', '江苏省南京市江宁区胜太路500号'),
('CASE006', 'A组', 1, '赵小美', '13912345683', 75000.00, 5000.00, 2, NOW(), 'admin', '320101199006061234', '江苏省南京市栖霞区仙林大道6号', '<EMAIL>', '招商银行', '622202********90128', '2024-01-12', 33, '025-12345683', '江苏省南京市浦口区江浦街道600号'),
('CASE007', 'B组', 0, '孙小华', '13912345684', 110000.00, 0.00, NULL, NOW(), 'admin', '320101199007071234', '江苏省南京市江宁区胜太路7号', '<EMAIL>', '民生银行', '622202********90129', '2024-01-18', 27, '025-12345684', '江苏省南京市六合区雄州街道700号'),
('CASE008', 'C组', 1, '周小军', '13912345685', 85000.00, 15000.00, 4, NOW(), 'admin', '320101199008081234', '江苏省南京市浦口区江浦街道8号', '<EMAIL>', '光大银行', '622202********90130', '2024-01-08', 37, '025-12345685', '江苏省南京市溧水区永阳街道800号'),
('CASE009', 'A组', 3, '吴小芳', '13912345686', 95000.00, 30000.00, 3, NOW(), 'admin', '320101199009091234', '江苏省南京市六合区雄州街道9号', '<EMAIL>', '华夏银行', '622202********90131', '2024-01-03', 42, '025-12345686', '江苏省南京市高淳区淳溪街道900号'),
('CASE010', 'B组', 1, '郑小伟', '13912345687', 70000.00, 8000.00, 3, NOW(), 'admin', '320101199010101234', '江苏省南京市溧水区永阳街道10号', '<EMAIL>', '平安银行', '622202********90132', '2024-01-14', 31, '025-12345687', '江苏省南京市玄武区中山路1000号'),
('CASE011', 'C组', 1, '黄小东', '13912345688', 65000.00, 12000.00, 4, NOW(), 'admin', '320101199011111234', '江苏省南京市高淳区淳溪街道11号', '<EMAIL>', '浦发银行', '622202********90133', '2024-01-16', 29, '025-12345688', '江苏省南京市鼓楼区中央路1100号'),
('CASE012', 'A组', 0, '林小西', '13912345689', 55000.00, 0.00, NULL, NOW(), 'admin', '320101199012121234', '江苏省南京市玄武区中山路12号', '<EMAIL>', '中信银行', '622202********90134', '2024-01-22', 23, '025-12345689', '江苏省南京市建邺区江东中路1200号'),
('CASE013', 'B组', 1, '何小南', '13912345690', 88000.00, 18000.00, 3, NOW(), 'admin', '320101199101011234', '江苏省南京市鼓楼区中央路13号', '<EMAIL>', '兴业银行', '622202********90135', '2024-01-09', 36, '025-12345690', '江苏省南京市秦淮区太平南路1300号'),
('CASE014', 'C组', 2, '谢小北', '13912345691', 72000.00, 72000.00, 4, NOW(), 'admin', '320101199102021234', '江苏省南京市秦淮区太平南路14号', '<EMAIL>', '广发银行', '622202********90136', '2024-01-02', 43, '025-12345691', '江苏省南京市雨花台区雨花路1400号'),
('CASE015', 'A组', 1, '罗小中', '13912345692', 63000.00, 8000.00, 2, NOW(), 'admin', '320101199103031234', '江苏省南京市建邺区江东中路15号', '<EMAIL>', '邮储银行', '622202********90137', '2024-01-13', 32, '025-12345692', '江苏省南京市栖霞区仙林大道1500号');

-- 插入催收记录测试数据
INSERT INTO `pho_red` (
    `pr_typ_id`, `pr_contact`, `pr_cas_id`, `pr_content`, `pr_time`, `pr_se_no`,
    `pr_con_type`, `pr_result`, `pr_next_time`, `pr_remark`
) VALUES
(1, '陈小明', 1, '电话联系客户，了解还款意愿和困难情况', '2024-01-16 09:00:00', 2, '电话', '承诺还款', '2024-01-20 09:00:00', '客户表示本月底前还款，目前资金紧张'),
(1, '李小红', 2, '电话催收，客户已部分还款，态度配合', '2024-01-17 10:30:00', 2, '电话', '部分还款', '2024-01-25 10:00:00', '客户已还1万，承诺月底还清余款'),
(1, '王小刚', 3, '电话无人接听，多次拨打均无法联系', '2024-01-21 14:00:00', 2, '电话', '无人接听', '2024-01-22 14:00:00', '多次拨打无人接听，尝试联系紧急联系人'),
(1, '张小丽', 4, '电话联系，客户配合度较高，有还款计划', '2024-01-18 11:00:00', 3, '电话', '承诺还款', '2024-01-28 11:00:00', '客户承诺分期还款，制定了详细计划'),
(2, '刘小强', 5, '发送催收短信，提醒还款义务', '2024-01-19 16:00:00', 4, '短信', '已发送', NULL, '短信已发送，等待客户回复'),
(1, '赵小美', 6, '电话催收，客户态度良好，有还款意愿', '2024-01-20 09:30:00', 2, '电话', '承诺还款', '2024-01-30 09:30:00', '客户表示理解，承诺尽快还款'),
(1, '孙小华', 7, '电话联系客户家属，了解客户情况', '2024-01-22 15:00:00', 3, '电话', '联系家属', '2024-01-25 15:00:00', '联系到客户家属，了解客户近况'),
(1, '周小军', 8, '电话催收，客户有还款计划和能力', '2024-01-23 10:00:00', 4, '电话', '承诺还款', '2024-02-01 10:00:00', '客户制定了详细的还款计划'),
(1, '吴小芳', 9, '电话联系，客户暂时有困难，协商延期', '2024-01-24 14:30:00', 3, '电话', '困难协商', '2024-02-05 14:30:00', '客户目前有困难，协商延期还款'),
(1, '郑小伟', 10, '电话催收，客户积极配合，态度良好', '2024-01-25 11:30:00', 3, '电话', '承诺还款', '2024-01-31 11:30:00', '客户积极配合，承诺月底前还款'),
(1, '黄小东', 11, '电话联系，客户表示近期会安排还款', '2024-01-26 13:00:00', 4, '电话', '承诺还款', '2024-02-02 13:00:00', '客户表示近期会安排还款'),
(1, '林小西', 12, '电话无法接通，显示关机状态', '2024-01-27 16:00:00', 2, '电话', '关机', '2024-01-28 16:00:00', '电话关机，尝试其他联系方式'),
(1, '何小南', 13, '电话催收，客户承诺本周内还款', '2024-01-28 10:00:00', 3, '电话', '承诺还款', '2024-02-03 10:00:00', '客户承诺本周内还款'),
(1, '谢小北', 14, '电话确认，客户已全额还清欠款', '2024-01-29 14:00:00', 4, '电话', '已还清', NULL, '客户已全额还清，案件可以结案'),
(1, '罗小中', 15, '电话催收，客户部分还款后承诺继续还款', '2024-01-30 11:00:00', 2, '电话', '承诺还款', '2024-02-05 11:00:00', '客户已部分还款，承诺继续还款');

-- 插入还款记录测试数据
INSERT INTO `case_paid` (
    `pa_state`, `pa_cas_id`, `pa_ptp_d`, `pa_ptp_num`, `pa_paid_date`, `pa_paid_num`,
    `pa_se_no`, `pa_remark`, `pa_type`, `pa_method`, `pa_ins_time`, `pa_ins_user`
) VALUES
(1, 2, '2024-01-15 00:00:00', 10000.00, '2024-01-15 14:30:00', 10000.00, 2, '客户主动还款，态度积极', '部分还款', '银行转账', NOW(), 'USER002'),
(0, 1, '2024-01-31 00:00:00', 50000.00, NULL, NULL, 2, '客户承诺月底前全额还款', '承诺还款', '银行转账', NOW(), 'USER002'),
(1, 4, '2024-01-20 00:00:00', 20000.00, '2024-01-20 16:00:00', 20000.00, 3, '客户按承诺还款，信用良好', '部分还款', '银行转账', NOW(), 'USER003'),
(1, 5, '2024-01-02 00:00:00', 90000.00, '2024-01-02 10:00:00', 90000.00, 4, '客户全额还清，案件结案', '全额还款', '银行转账', NOW(), 'USER004'),
(0, 6, '2024-02-01 00:00:00', 70000.00, NULL, NULL, 2, '客户承诺下月初还清余款', '承诺还款', '银行转账', NOW(), 'USER002'),
(1, 6, '2024-01-18 00:00:00', 5000.00, '2024-01-18 09:00:00', 5000.00, 2, '客户先还部分金额表示诚意', '部分还款', '银行转账', NOW(), 'USER002'),
(0, 8, '2024-02-05 00:00:00', 70000.00, NULL, NULL, 4, '客户制定分期还款计划', '承诺还款', '银行转账', NOW(), 'USER004'),
(1, 8, '2024-01-22 00:00:00', 15000.00, '2024-01-22 11:30:00', 15000.00, 4, '客户按计划第一期还款', '部分还款', '银行转账', NOW(), 'USER004'),
(0, 9, '2024-02-10 00:00:00', 65000.00, NULL, NULL, 3, '协商延期还款，客户有困难', '承诺还款', '银行转账', NOW(), 'USER003'),
(1, 9, '2024-01-10 00:00:00', 30000.00, '2024-01-10 15:20:00', 30000.00, 3, '客户前期还款记录', '部分还款', '银行转账', NOW(), 'USER003'),
(0, 10, '2024-01-31 00:00:00', 62000.00, NULL, NULL, 3, '客户承诺月底前还清余款', '承诺还款', '银行转账', NOW(), 'USER003'),
(1, 10, '2024-01-25 00:00:00', 8000.00, '2024-01-25 13:45:00', 8000.00, 3, '客户部分还款', '部分还款', '银行转账', NOW(), 'USER003'),
(0, 11, '2024-02-02 00:00:00', 53000.00, NULL, NULL, 4, '客户承诺下月初还清余款', '承诺还款', '银行转账', NOW(), 'USER004'),
(1, 11, '2024-01-26 00:00:00', 12000.00, '2024-01-26 10:15:00', 12000.00, 4, '客户按时还款', '部分还款', '银行转账', NOW(), 'USER004'),
(1, 14, '2024-01-29 00:00:00', 72000.00, '2024-01-29 16:30:00', 72000.00, 4, '客户全额还清，案件结案', '全额还款', '银行转账', NOW(), 'USER004'),
(0, 15, '2024-02-05 00:00:00', 55000.00, NULL, NULL, 2, '客户承诺分期还清余款', '承诺还款', '银行转账', NOW(), 'USER002'),
(1, 15, '2024-01-30 00:00:00', 8000.00, '2024-01-30 14:20:00', 8000.00, 2, '客户部分还款', '部分还款', '银行转账', NOW(), 'USER002');

-- ========================================
-- 数据完整性检查和统计
-- ========================================

-- 提交所有事务
COMMIT;

-- 显示数据库创建结果
SELECT '========================================' AS '';
SELECT '数据库创建完成！' AS '状态';
SELECT '========================================' AS '';

-- 统计表数量
SELECT '表结构统计' AS '类型', COUNT(*) AS '数量'
FROM information_schema.tables
WHERE table_schema = 'ccds';

-- 统计基础数据
SELECT '基础数据统计' AS '';
SELECT '用户数量' AS '类型', COUNT(*) AS '数量' FROM sys_user;
SELECT '员工数量' AS '类型', COUNT(*) AS '数量' FROM sal_emp;
SELECT '组织数量' AS '类型', COUNT(*) AS '数量' FROM sal_org;
SELECT '操作权限数量' AS '类型', COUNT(*) AS '数量' FROM lim_operate;
SELECT '功能模块数量' AS '类型', COUNT(*) AS '数量' FROM lim_function;
SELECT '类型字典数量' AS '类型', COUNT(*) AS '数量' FROM type_list;
SELECT '省份数量' AS '类型', COUNT(*) AS '数量' FROM cus_province;
SELECT '城市数量' AS '类型', COUNT(*) AS '数量' FROM cus_city;

-- 统计业务数据
SELECT '业务数据统计' AS '';
SELECT '案件数量' AS '类型', COUNT(*) AS '数量' FROM bank_case;
SELECT '催收记录数量' AS '类型', COUNT(*) AS '数量' FROM pho_red;
SELECT '还款记录数量' AS '类型', COUNT(*) AS '数量' FROM case_paid;

-- 案件状态分布
SELECT '案件状态分布' AS '';
SELECT
    CASE cas_state
        WHEN 0 THEN '待分配'
        WHEN 1 THEN '催收中'
        WHEN 2 THEN '已结案'
        WHEN 3 THEN '暂停催收'
        WHEN 4 THEN '法务处理'
        ELSE '未知'
    END AS '状态',
    COUNT(*) AS '数量',
    CONCAT(ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM bank_case), 2), '%') AS '占比'
FROM bank_case
GROUP BY cas_state
ORDER BY cas_state;

-- 员工案件分配情况
SELECT '员工案件分配情况' AS '';
SELECT
    COALESCE(e.se_name, '未分配') AS '员工姓名',
    COUNT(c.cas_id) AS '案件数量',
    CONCAT(FORMAT(SUM(c.cas_m), 2), ' 元') AS '案件总金额',
    CONCAT(FORMAT(SUM(c.cas_paid_m), 2), ' 元') AS '已还金额',
    CONCAT(ROUND(COALESCE(SUM(c.cas_paid_m) / NULLIF(SUM(c.cas_m), 0) * 100, 0), 2), '%') AS '回收率'
FROM bank_case c
LEFT JOIN sal_emp e ON c.cas_se_no = e.se_no
GROUP BY c.cas_se_no, e.se_name
ORDER BY COUNT(c.cas_id) DESC;

-- 银行分布统计
SELECT '银行分布统计' AS '';
SELECT
    cas_bank AS '银行',
    COUNT(*) AS '案件数量',
    CONCAT(FORMAT(SUM(cas_m), 2), ' 元') AS '案件总金额',
    CONCAT(FORMAT(AVG(cas_m), 2), ' 元') AS '平均金额'
FROM bank_case
WHERE cas_bank IS NOT NULL
GROUP BY cas_bank
ORDER BY COUNT(*) DESC;

-- 逾期天数分布
SELECT '逾期天数分布' AS '';
SELECT
    CASE
        WHEN cas_overdue_days <= 30 THEN 'M1(1-30天)'
        WHEN cas_overdue_days <= 60 THEN 'M2(31-60天)'
        WHEN cas_overdue_days <= 90 THEN 'M3(61-90天)'
        WHEN cas_overdue_days <= 120 THEN 'M4(91-120天)'
        WHEN cas_overdue_days <= 150 THEN 'M5(121-150天)'
        WHEN cas_overdue_days <= 180 THEN 'M6(151-180天)'
        ELSE 'M7+(180天以上)'
    END AS '逾期等级',
    COUNT(*) AS '案件数量',
    CONCAT(ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM bank_case), 2), '%') AS '占比'
FROM bank_case
GROUP BY
    CASE
        WHEN cas_overdue_days <= 30 THEN 'M1(1-30天)'
        WHEN cas_overdue_days <= 60 THEN 'M2(31-60天)'
        WHEN cas_overdue_days <= 90 THEN 'M3(61-90天)'
        WHEN cas_overdue_days <= 120 THEN 'M4(91-120天)'
        WHEN cas_overdue_days <= 150 THEN 'M5(121-150天)'
        WHEN cas_overdue_days <= 180 THEN 'M6(151-180天)'
        ELSE 'M7+(180天以上)'
    END
ORDER BY MIN(cas_overdue_days);

SELECT '========================================' AS '';
SELECT '数据库初始化完成！可以开始使用系统了。' AS '提示';
SELECT '默认管理员账号：admin / 123456' AS '登录信息';
SELECT '========================================' AS '';
