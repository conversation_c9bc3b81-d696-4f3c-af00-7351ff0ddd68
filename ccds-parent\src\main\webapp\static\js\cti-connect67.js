function connectCTI(phone){
	var url = "connectCTI.do?op=connectToOM";
	var pars = [];
	pars.phone = phone;
	pars.serverIP = "***********";
	//pars.localNo = "001";
	//pars.phone = "113";
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var rs = response.responseText;
			alert(rs);
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}