<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frsoft.ccds.system.mapper.SysUserMapper">

    <resultMap type="com.frsoft.ccds.system.domain.SysUser" id="SysUserResult">
        <id     property="userCode"      column="user_code"/>
        <result property="loginName"     column="user_login_name"/>
        <result property="password"      column="user_pwd"/>
        <result property="userName"      column="user_name"/>
        <result property="enabled"       column="user_isenabled"/>
        <result property="ctiServer"     column="user_cti_server"/>
        <result property="ctiPhone"      column="user_cti_phone"/>
        <result property="smsMaxNum"     column="user_sms_max_num"/>
    </resultMap>

    <sql id="selectUserVo">
        select user_code, user_login_name, user_pwd, user_name, user_isenabled,
        user_cti_server, user_cti_phone, user_sms_max_num
        from sys_user
    </sql>

    <select id="selectUserList" parameterType="com.frsoft.ccds.system.domain.SysUser" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        <where>
            <if test="loginName != null and loginName != ''">
                AND user_loginName like concat('%', #{loginName}, '%')
            </if>
            <if test="enabled != null and enabled != ''">
                AND user_isenabled = #{enabled}
            </if>
            <if test="empName != null and empName != ''">
                AND user_se_name like concat('%', #{empName}, '%')
            </if>
            <if test="userNumber != null and userNumber != ''">
                AND user_num = #{userNumber}
            </if>
            <if test="orgCode != null and orgCode != ''">
                AND user_so_code = #{orgCode}
            </if>
        </where>
    </select>

    <select id="selectUserByLoginName" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where user_login_name = #{loginName}
    </select>

    <select id="selectUserByUserCode" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where user_code = #{userCode}
    </select>

    <insert id="insertUser" parameterType="com.frsoft.ccds.system.domain.SysUser">
        insert into lim_user(
            user_code, user_role_id, user_se_id, user_so_code, user_up_code,
            user_loginName, user_pwd, user_lev, user_se_name, user_desc,
            user_isenabled, user_num, user_cti_login, user_cti_pwd,
            user_cti_server, user_cti_phone, user_sms_max_num,
            create_time, create_by, remark
        )values(
            #{userCode}, #{roleId}, #{empId}, #{orgCode}, #{parentUserCode},
            #{loginName}, #{password}, #{userLevel}, #{empName}, #{description},
            #{enabled}, #{userNumber}, #{ctiLoginName}, #{ctiPassword},
            #{ctiServer}, #{ctiPhone}, #{smsMaxNum},
            NOW(), #{createBy}, #{remark}
        )
    </insert>

    <update id="updateUser" parameterType="com.frsoft.ccds.system.domain.SysUser">
        update lim_user
        <set>
            <if test="roleId != null">user_role_id = #{roleId},</if>
            <if test="empId != null">user_se_id = #{empId},</if>
            <if test="orgCode != null">user_so_code = #{orgCode},</if>
            <if test="parentUserCode != null">user_up_code = #{parentUserCode},</if>
            <if test="loginName != null">user_loginName = #{loginName},</if>
            <if test="password != null">user_pwd = #{password},</if>
            <if test="userLevel != null">user_lev = #{userLevel},</if>
            <if test="empName != null">user_se_name = #{empName},</if>
            <if test="description != null">user_desc = #{description},</if>
            <if test="enabled != null">user_isenabled = #{enabled},</if>
            <if test="userNumber != null">user_num = #{userNumber},</if>
            <if test="ctiLoginName != null">user_cti_login = #{ctiLoginName},</if>
            <if test="ctiPassword != null">user_cti_pwd = #{ctiPassword},</if>
            <if test="ctiServer != null">user_cti_server = #{ctiServer},</if>
            <if test="ctiPhone != null">user_cti_phone = #{ctiPhone},</if>
            <if test="smsMaxNum != null">user_sms_max_num = #{smsMaxNum},</if>
            <if test="groupId != null">user_grp_id = #{groupId},</if>
            <if test="ctiServerId != null">user_ctis_id = #{ctiServerId},</if>
            update_time = NOW(),
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark}</if>
        </set>
        where user_code = #{userCode}
    </update>

    <update id="updateUserPassword">
        update lim_user
        set user_pwd = #{password},
            user_pwd_upd_date = NOW()
        where user_code = #{userCode}
    </update>

    <update id="updateUserStatus">
        update lim_user
        set user_isenabled = #{enabled}
        where user_code = #{userCode}
    </update>

    <delete id="deleteUserByUserCode" parameterType="String">
        delete from lim_user where user_code = #{userCode}
    </delete>

    <delete id="deleteUserByUserCodes" parameterType="String">
        delete from lim_user where user_code in
        <foreach collection="array" item="userCode" open="(" separator="," close=")">
            #{userCode}
        </foreach>
    </delete>
</mapper>