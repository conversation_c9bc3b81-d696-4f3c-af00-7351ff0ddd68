-- MySQL 5.7 完整CCDS数据库 - 所有106个表
-- 从MSSQL完整转换，确保100%功能兼容

CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;
USE `ccds`;

SET NAMES utf8;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- ========================================
-- 所有106个表的完整定义
-- ========================================

SET NAMES utf8;
SET FOREIGN_KEY_CHECKS = 0;

use ccds;

CREATE TABLE `spo_paid_past`(
	`spa_id` `bigint` AUTO_INCREMENT NOT NULL,
	`spa_code` `varchar`(300)  NULL,
	`spa_spo_id` `bigint` NULL,
	`spa_aco_id` `bigint` NULL,
	`spa_ssu_id` `bigint` NULL,
	`spa_fct_date` `datetime` NULL,
	`spa_type_id` `bigint` NULL,
	`spa_pay_type` `nvarchar`(50)  NULL,
	`spa_pay_mon` `decimal`(18, 2) NULL,
	`spa_in_name` `nvarchar`(100)  NULL,
	`spa_inp_user` `nvarchar`(50)  NULL,
	`spa_se_no` `bigint` NULL,
	`spa_isinv` `char`(1)  NULL,
	`spa_remark` `nvarchar`(max)  NULL,
	`spa_cre_date` `datetime` NULL,
	`spa_isdel` `char`(1)  NULL,
	`spa_content` `nvarchar`(100)  NULL,
	`spa_acc_type_id` `bigint` NULL,
	`spa_alt_date` `datetime` NULL,
	`spa_alt_user` `nvarchar`(50)  NULL,
	`spa_undo_date` `datetime` NULL,
	`spa_undo_user` `nvarchar`(50)  NULL,
 CONSTRAINT `PK_spo_paid_past` PRIMARY KEY CLUSTERED 
(
	`spa_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `sal_emp`(
	`se_no` `bigint` AUTO_INCREMENT NOT NULL,
	`se_so_code` `varchar`(50)  NULL,
	`se_name` `varchar`(100)  NULL,
	`se_ide_code` `varchar`(50)  NULL,
	`se_pos` `nvarchar`(50)  NULL,
	`se_sex` `varchar`(50)  NULL,
	`se_prob` `varchar`(50)  NULL,
	`se_bir_place` `nvarchar`(50)  NULL,
	`se_acc_place` `nvarchar`(100)  NULL,
	`se_birth` `varchar`(50)  NULL,
	`se_marry` `varchar`(10)  NULL,
	`se_type` `varchar`(50)  NULL,
	`se_job_lev` `bigint` NULL,
	`se_job_cate` `varchar`(50)  NULL,
	`se_job_title` `nvarchar`(50)  NULL,
	`se_start_day` `datetime` NULL,
	`se_year_pay` `nvarchar`(50)  NULL,
	`se_cost_center` `varchar`(50)  NULL,
	`se_email` `nvarchar`(50)  NULL,
	`se_nation` `varchar`(50)  NULL,
	`se_poli_status` `varchar`(50)  NULL,
	`se_edu` `nvarchar`(50)  NULL,
	`se_tel` `varchar`(50)  NULL,
	`se_phone` `varchar`(50)  NULL,
	`se_qq` `varchar`(50)  NULL,
	`se_msn` `nvarchar`(50)  NULL,
	`se_rec_source` `nvarchar`(100)  NULL,
	`se_prov_fund` `nvarchar`(50)  NULL,
	`se_job_date` `datetime` NULL,
	`se_hou_reg` `varchar`(50)  NULL,
	`se_social_code` `nvarchar`(50)  NULL,
	`se_rap` `varchar`(50)  NULL,
	`se_address` `nvarchar`(500)  NULL,
	`se_remark` `nvarchar`(max)  NULL,
	`se_bank_name` `nvarchar`(50)  NULL,
	`se_bank_card` `nvarchar`(50)  NULL,
	`se_weal_address` `nvarchar`(50)  NULL,
	`se_weal_pos` `varchar`(50)  NULL,
	`se_isovertime` `varchar`(50)  NULL,
	`se_attendance` `varchar`(50)  NULL,
	`se_card_num` `varchar`(50)  NULL,
	`se_pic` `nvarchar`(max)  NULL,
	`se_isenabled` `char`(1)  NULL,
	`se_inser_date` `datetime` NULL,
	`se_code` `varchar`(50)  NULL,
	`se_log` `nvarchar`(max)  NULL,
	`se_alt_date` `datetime` NULL,
	`se_inser_user` `nvarchar`(50)  NULL,
	`se_alt_user` `nvarchar`(50)  NULL,
	`se_end_date` `datetime` NULL,
	`se_edc_bac` `nvarchar`(max)  NULL,
	`se_work_ex` `nvarchar`(max)  NULL,
	`se_user_code` `varchar`(50)  NULL,
	`se_per_tel` `varchar`(50) NULL,
	`se_plan_sign_date` `datetime` NULL,
	`se_sign_date` `datetime` NULL,
	`se_credit_date` `datetime` NULL,
	`se_college` `nvarchar`(200) NULL,
	`se_transfer` `text` NULL,
 CONSTRAINT `PK_sal_emp_1` PRIMARY KEY CLUSTERED 
(
	`se_no` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
END
 AND name = N'IX_sal_emp')
CREATE NONCLUSTERED INDEX `IX_sal_emp` ON `sal_emp` 
(
	`se_name` ASC
)WITH (IGNORE_DUP_KEY = OFF)

 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `r_rep_lim`(
	`rrl_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rrl_rep_code` `bigint` NULL,
	`rrl_se_no` `bigint` NULL,
	`rrl_date` `datetime` NULL,
	`rrl_content` `nvarchar`(max)  NULL,
	`rrl_isappro` `char`(1)  NULL,
	`rrl_oppro_date` `datetime` NULL,
	`rrl_isdel` `char`(1)  NULL,
	`rrl_app_order` `int` NULL,
	`rrl_isview` `char`(1)  NULL,
	`rrl_is_all_appro` `char`(1)  NULL,
	`rrl_rec_user` `nvarchar`(50)  NULL,
 CONSTRAINT `PK_r_rep_lim` PRIMARY KEY CLUSTERED 
(
	`rrl_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `sal_paid_plan`(
	`spd_id` `bigint` AUTO_INCREMENT NOT NULL,
	`spd_ord_code` `bigint` NULL,
	`spd_prm_date` `datetime` NULL,
	`spd_count` `int` NULL,
	`spd_pay_mon` `decimal`(18, 2) NULL,
	`spd_mon_type` `nvarchar`(50)  NULL,
	`spd_user_code` `nvarchar`(50)  NULL,
	`spd_isp` `char`(1)  NULL,
	`spd_resp` `varchar`(50)  NULL,
	`spd_cre_date` `datetime` NULL,
	`spd_alt_date` `datetime` NULL,
	`spd_alt_user` `nvarchar`(50)  NULL,
	`spd_isdel` `char`(1)  NULL,
	`spd_content` `nvarchar`(100)  NULL,
	`spd_cor_code` `bigint` NULL,
 CONSTRAINT `PK_sal_paid` PRIMARY KEY CLUSTERED 
(
	`spd_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `r_new_lim`(
	`rnl_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rnl_new_code` `bigint` NULL,
	`rnl_se_no` `bigint` NULL,
	`rnl_date` `datetime` NULL,
 CONSTRAINT `PK_r_new_lim` PRIMARY KEY CLUSTERED 
(
	`rnl_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `account`(
	`aco_id` `bigint` AUTO_INCREMENT NOT NULL,
	`aco_type` `nvarchar`(50)  NULL,
	`aco_name` `nvarchar`(100)  NULL,
	`aco_bank_num` `nvarchar`(50)  NULL,
	`aco_bank` `nvarchar`(100)  NULL,
	`aco_bank_name` `nvarchar`(50)  NULL,
	`aco_cre_date` `datetime` NULL,
	`aco_org_mon` `decimal`(18, 2) NULL,
	`aco_cur_mon` `decimal`(18, 2) NULL,
	`aco_remark` `nvarchar`(max)  NULL,
	`aco_inp_user` `nvarchar`(50)  NULL,
	`aco_inp_date` `datetime` NULL,
	`aco_alt_date` `datetime` NULL,
	`aco_alt_user` `nvarchar`(50)  NULL,
 CONSTRAINT `PK_account` PRIMARY KEY CLUSTERED 
(
	`aco_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `lim_user`(
	`user_code` `varchar`(50)  NOT NULL,
	`user_loginName` `varchar`(50)  NULL,
	`user_pwd` `varchar`(50)  NULL,
	`user_up_code` `varchar`(50)  NULL,
	`user_lev` `char`(1)  NULL,
	`user_so_code` `varchar`(50)  NULL,
	`user_se_id` `bigint` NULL,
	`user_se_name` `nvarchar`(100)  NULL,
	`user_desc` `nvarchar`(max)  NULL,
	`user_isenabled` `char`(1)  NULL,
	`user_num` `varchar`(200)  NULL,
	`user_role_id` `bigint` NULL,
	`user_islogin` `char`(1)  NULL,
	`user_ip` `varchar`(50)  NULL,
	`user_fail` `int` NULL,
	`user_pwd_upd_date` datetime NULL,
	`user_cti_login` varchar(255) NULL,
	`user_cti_pwd` varchar(255) NULL,
	`user_cti_server` varchar(50) NULL,
	`user_cti_phone` varchar(50) NULL,
	`user_grp_id` `bigint` NULL,
	`user_sms_max_num` `int` NULL,
 CONSTRAINT `PK_lim_user` PRIMARY KEY CLUSTERED 
(
	`user_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `inquiry`(
	`inq_id` `bigint` AUTO_INCREMENT NOT NULL,
	`inq_ssu_id` `bigint` NULL,
	`inq_pro_id` `bigint` NULL,
	`inq_title` `nvarchar`(100)  NULL,
	`inq_price` `decimal`(18, 2) NULL,
	`inq_se_no` `bigint` NULL,
	`inq_date` `datetime` NULL,
	`inq_inp_user` `nvarchar`(50)  NULL,
	`inq_upd_user` `nvarchar`(50)  NULL,
	`inq_ins_date` `datetime` NULL,
	`inq_upd_date` `datetime` NULL,
	`inq_remark` `nvarchar`(max)  NULL,
	`inq_isdel` `char`(1)  NULL,
 CONSTRAINT `PK_inquiry` PRIMARY KEY CLUSTERED 
(
	`inq_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `quote`(
	`quo_id` `bigint` AUTO_INCREMENT NOT NULL,
	`quo_opp_id` `bigint` NULL,
	`quo_title` `nvarchar`(100)  NULL,
	`quo_price` `decimal`(18, 2) NULL,
	`quo_se_no` `bigint` NULL,
	`quo_remark` `nvarchar`(max)  NULL,
	`quo_date` `datetime` NULL,
	`quo_desc` `nvarchar`(max)  NULL,
	`quo_ins_date` `datetime` NULL,
	`quo_upd_date` `datetime` NULL,
	`quo_inp_user` `nvarchar`(50)  NULL,
	`quo_upd_user` `nvarchar`(50)  NULL,
	`quo_isdel` `char`(1)  NULL,
	`quo_pro_id` `bigint` NULL,
 CONSTRAINT `PK_quote` PRIMARY KEY CLUSTERED 
(
	`quo_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `cus_cor_cus`(
	`cor_code` `bigint` AUTO_INCREMENT NOT NULL,
	`cor_num` `varchar`(50)  NULL,
	`cor_user_code` `varchar`(50)  NULL,
	`cor_name` `nvarchar`(100)  NULL,
	`cor_hot` `nvarchar`(50)  NULL,
	`cor_mne` `nvarchar`(50)  NULL,
	`cor_lic_code` `varchar`(50)  NULL,
	`cor_org_code` `varchar`(50)  NULL,
	`cor_star` `varchar`(50)  NULL,
	`cor_cre_lev` `varchar`(50)  NULL,
	`cor_cre_lim` `varchar`(50)  NULL,
	`cor_ind_id` `bigint` NULL,
	`cor_per_size` `varchar`(50)  NULL,
	`cor_acc_bank` `nvarchar`(100)  NULL,
	`cor_bank_num` `varchar`(50)  NULL,
	`cor_sou_id` `bigint` NULL,
	`cor_com_inf` `nvarchar`(max)  NULL,
	`cor_country` `bigint` NULL,
	`cor_province` `bigint` NULL,
	`cor_city` `bigint` NULL,
	`cor_phone` `varchar`(50)  NULL,
	`cor_fex` `varchar`(50)  NULL,
	`cor_net` `varchar`(500)  NULL,
	`cor_zip_code` `varchar`(50)  NULL,
	`cor_address` `nvarchar`(max)  NULL,
	`cor_remark` `nvarchar`(max)  NULL,
	`cor_creat_date` `datetime` NULL,
	`cor_upd_date` `datetime` NULL,
	`cor_issuc` `char`(1)  NULL,
	`cor_last_date` `datetime` NULL,
	`cor_temp_tag` `nvarchar`(50)  NULL,
	`cor_isdelete` `char`(1)  NULL,
	`cor_spe_write` `nvarchar`(max)  NULL,
	`cor_upd_user` `nvarchar`(50)  NULL,
	`cor_typ_id` `bigint` NULL,
	`cor_ins_user` `nvarchar`(50)  NULL,
 CONSTRAINT `PK_cus_cor_cus` PRIMARY KEY CLUSTERED 
(
	`cor_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
END
 AND name = N'user_index')
CREATE NONCLUSTERED INDEX `user_index` ON `cus_cor_cus` 
(
	`cor_user_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)

 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `sal_opp`(
	`opp_id` `bigint` AUTO_INCREMENT NOT NULL,
	`opp_cor_code` `bigint` NULL,
	`opp_title` `nvarchar`(300)  NULL,
	`opp_lev` `varchar`(50)  NULL,
	`opp_exe_date` `datetime` NULL,
	`opp_des` `nvarchar`(max)  NULL,
	`opp_remark` `nvarchar`(max)  NULL,
	`opp_ins_date` `datetime` NULL,
	`opp_isexe` `varchar`(10)  NULL,
	`opp_state` `varchar`(10)  NULL,
	`opp_upd_date` `datetime` NULL,
	`opp_inp_user` `nvarchar`(50)  NULL,
	`opp_upd_user` `nvarchar`(50)  NULL,
	`opp_isdel` `char`(1)  NULL,
	`opp_sign_date` `datetime` NULL,
	`opp_money` `decimal`(18, 2) NULL,
	`opp_stage` `bigint` NULL,
	`opp_possible` `varchar`(50)  NULL,
	`opp_sta_remark` `nvarchar`(100)  NULL,
	`opp_sta_update` `datetime` NULL,
	`opp_sta_log` `nvarchar`(max)  NULL,
	`opp_find_date` `datetime` NULL,
	`opp_user_code` `varchar`(50)  NULL,
	`opp_se_no` `bigint` NULL,
 CONSTRAINT `PK_sal_opp` PRIMARY KEY CLUSTERED 
(
	`opp_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `sal_pra`(
	`pra_id` `bigint` AUTO_INCREMENT NOT NULL,
	`pra_cor_code` `bigint` NULL,
	`pra_title` `nvarchar`(300)  NULL,
	`pra_content` `nvarchar`(max)  NULL,
	`pra_ins_date` `datetime` NULL,
	`pra_type` `nvarchar`(100)  NULL,
	`pra_state` `nvarchar`(100)  NULL,
	`pra_isPrice` `nvarchar`(10)  NULL,
	`pra_exe_date` `datetime` NULL,
	`pra_cost_time` `nvarchar`(20)  NULL,
	`pra_cus_link` `nvarchar`(50)  NULL,
	`pra_se_no` `bigint` NULL,
	`pra_back` `nvarchar`(max)  NULL,
	`pra_remark` `nvarchar`(max)  NULL,
	`pra_upd_date` `datetime` NULL,
	`pra_opp_id` `bigint` NULL,
	`pra_inp_user` `nvarchar`(50)  NULL,
	`pra_upd_user` `nvarchar`(50)  NULL,
	`pra_isdel` `char`(1)  NULL,
	`pra_user_code` `varchar`(50)  NULL,
 CONSTRAINT `PK_sal_pra` PRIMARY KEY CLUSTERED 
(
	`pra_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `pro_task`(
	`prta_id` `bigint` AUTO_INCREMENT NOT NULL,
	`prta_se_no` `bigint` NULL,
	`prta_pro_id` `bigint` NULL,
	`prta_sta_name` `nvarchar`(300)  NULL,
	`prta_name` `nvarchar`(50)  NULL,
	`prta_title` `nvarchar`(300)  NULL,
	`prta_rel_date` `datetime` NULL,
	`prta_change_date` `datetime` NULL,
	`prta_fin_date` `datetime` NULL,
	`prta_lev` `varchar`(50)  NULL,
	`prta_state` `char`(1)  NULL,
	`prta_cyc` `varchar`(50)  NULL,
	`prta_tag` `nvarchar`(max)  NULL,
	`prta_desc` `nvarchar`(max)  NULL,
	`prta_log` `nvarchar`(max)  NULL,
	`prta_remark` `nvarchar`(max)  NULL,
	`prta_isdel` `char`(1)  NULL,
	`prta_fct_date` `datetime` NULL,
	`prta_upd_user` `nvarchar`(50)  NULL,
 CONSTRAINT `PK_pro_task` PRIMARY KEY CLUSTERED 
(
	`prta_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `cus_serv`(
	`ser_code` `bigint` AUTO_INCREMENT NOT NULL,
	`ser_cor_code` `bigint` NULL,
	`ser_title` `nvarchar`(300)  NULL,
	`ser_cus_link` `nvarchar`(50)  NULL,
	`ser_method` `nvarchar`(100)  NULL,
	`ser_content` `nvarchar`(max)  NULL,
	`ser_exe_date` `datetime` NULL,
	`ser_cos_time` `varchar`(50)  NULL,
	`ser_state` `varchar`(10)  NULL,
	`ser_se_no` `bigint` NULL,
	`ser_feedback` `nvarchar`(max)  NULL,
	`ser_remark` `nvarchar`(max)  NULL,
	`ser_ins_date` `datetime` NULL,
	`ser_upd_date` `datetime` NULL,
	`ser_inp_user` `nvarchar`(50)  NULL,
	`ser_upd_user` `nvarchar`(50)  NULL,
	`ser_isdel` `char`(1)  NULL,
	`ser_user_code` `varchar`(50)  NULL,
 CONSTRAINT `PK_cus_ser` PRIMARY KEY CLUSTERED 
(
	`ser_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `sal_ord_con`(
	`sod_code` `bigint` AUTO_INCREMENT NOT NULL,
	`sod_num` `varchar`(300)  NULL,
	`sod_til` `nvarchar`(300)  NULL,
	`sod_type_id` `bigint` NULL,
	`sod_cus_code` `bigint` NULL,
	`sod_pro_id` `bigint` NULL,
	`sod_sum_mon` `decimal`(18, 2) NULL,
	`sod_paid_mon` `decimal`(18, 2) NULL,
	`sod_mon_type` `varchar`(50)  NULL,
	`sod_state` `varchar`(10)  NULL,
	`sod_ship_state` `varchar`(10)  NULL,
	`sod_own_code` `varchar`(50)  NULL,
	`sod_deadline` `datetime` NULL,
	`sod_end_date` `datetime` NULL,
	`sod_ord_date` `datetime` NULL,
	`sod_inp_date` `datetime` NULL,
	`sod_isfail` `char`(1)  NULL,
	`sod_remark` `nvarchar`(max)  NULL,
	`sod_change_date` `datetime` NULL,
	`sod_paid_method` `nvarchar`(20)  NULL,
	`sod_inp_code` `nvarchar`(50)  NULL,
	`sod_cus_con` `nvarchar`(100)  NULL,
	`sod_se_no` `bigint` NULL,
	`sod_con_date` `datetime` NULL,
	`sod_change_user` `nvarchar`(50)  NULL,
	`sod_app_date` `datetime` NULL,
	`sod_app_man` `nvarchar`(50)  NULL,
	`sod_app_desc` `nvarchar`(max)  NULL,
	`sod_app_isok` `char`(1)  NULL,
	`sod_content` `nvarchar`(max)  NULL,
 CONSTRAINT `PK_sal_order` PRIMARY KEY CLUSTERED 
(
	`sod_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `wms_line`(
	`wli_id` `bigint` AUTO_INCREMENT NOT NULL,
	`wli_type_code` `varchar`(50)  NULL,
	`wli_type` `varchar`(50)  NULL,
	`wli_stro_code` `varchar`(50)  NULL,
	`wli_wpr_id` `bigint` NULL,
	`wli_in_num` `decimal`(18, 2) NULL,
	`wli_out_num` `decimal`(18, 2) NULL,
	`wli_date` `datetime` NULL,
	`wli_state` `char`(1)  NULL,
	`wli_man` `varchar`(50)  NULL,
	`wli_wms_id` `bigint` NULL,
	`wli_isdel` `char`(1)  NULL,
	`wli_num` `decimal`(18, 2) NULL,
 CONSTRAINT `PK_wms_line` PRIMARY KEY CLUSTERED 
(
	`wli_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `r_ship_pro`(
	`rshp_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rshp_ship_code` `varchar`(50)  NULL,
	`rshp_pro_id` `bigint` NULL,
 CONSTRAINT `PK_r_ship_pro` PRIMARY KEY CLUSTERED 
(
	`rshp_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `doc_template`(
	`tmp_id` `bigint` AUTO_INCREMENT NOT NULL,
	`tmp_name` `nvarchar`(50)  NULL,
	`tmp_html` `varchar`(max)  NULL,
	`tmp_type` `varchar`(50)  NULL,
	`tmp_mark` `varchar`(100)  NULL,
 CONSTRAINT `PK_template` PRIMARY KEY CLUSTERED 
(
	`tmp_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `r_stro_pro`(
	`rsp_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rsp_stro_code` `varchar`(50)  NULL,
	`rsp_pro_id` `bigint` NULL,
	`rsp_pro_num` `decimal`(18, 2) NULL,
 CONSTRAINT `PK_r_stro_pro` PRIMARY KEY CLUSTERED 
(
	`rsp_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `address`(
	`adr_id` `bigint` AUTO_INCREMENT NOT NULL,
	`adr_state` `int` NULL,
	`adr_name` `nvarchar`(50)  NULL,
	`adr_add` `nvarchar`(200)  NULL,
	`adr_cas_id` `bigint` NULL,
	`adr_cat` `nvarchar`(50)  NULL,
	`adr_remark` `nvarchar`(max)  NULL,
	`adr_isdel` `char`(1)  NULL,
	`adr_num` `int` NULL,
	`adr_check_app` `int` NULL,
	`adr_mail_app` `int` NULL,
	`adr_vis_app` `int` NULL,
	`adr_rel` `nvarchar`(50) NULL,
	`adr_mail_count` `int` NULL,
	`adr_isnew` `int` NULL,
 CONSTRAINT `PK_address` PRIMARY KEY CLUSTERED 
(
	`adr_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `wms_product`(
	`wpr_id` `bigint` AUTO_INCREMENT NOT NULL,
	`wpr_name` `nvarchar`(100)  NULL,
	`wpr_type_id` `bigint` NULL,
	`wpr_model` `nvarchar`(100)  NULL,
	`wpr_unit` `bigint` NULL,
	`wpr_color` `varchar`(50)  NULL,
	`wpr_size` `varchar`(50)  NULL,
	`wpr_provider` `nvarchar`(100)  NULL,
	`wpr_up_lim` `int` NULL,
	`wpr_low_lim` `int` NULL,
	`wpr_cost_prc` `decimal`(18, 2) NULL,
	`wpr_sale_prc` `decimal`(18, 2) NULL,
	`wpr_pic` `varchar`(max)  NULL,
	`wpr_cuser_code` `nvarchar`(50)  NULL,
	`wpr_cre_date` `datetime` NULL,
	`wpr_euser_code` `nvarchar`(50)  NULL,
	`wpr_edit_date` `datetime` NULL,
	`wpr_desc` `nvarchar`(max)  NULL,
	`wpr_remark` `nvarchar`(max)  NULL,
	`wpr_states` `char`(1)  NULL,
	`wpr_range` `nvarchar`(max)  NULL,
	`wpr_technology` `nvarchar`(max)  NULL,
	`wpr_problem` `nvarchar`(max)  NULL,
	`wpr_isdel` `char`(1)  NULL,
	`wpr_code` `varchar`(50)  NULL,
	`wpr_iscount` `char`(1)  NULL,
 CONSTRAINT `PK_wms_product` PRIMARY KEY CLUSTERED 
(
	`wpr_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `r_spo_pro`(
	`rpp_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rpp_spo_id` `bigint` NULL,
	`rpp_pro_id` `bigint` NULL,
	`rpp_num` `decimal`(18, 2) NULL,
	`rpp_price` `decimal`(18, 2) NULL,
	`rpp_sum_mon` `decimal`(18, 2) NULL,
	`rpp_remark` `nvarchar`(max)  NULL,
	`rpp_out_num` `decimal`(18, 2) NULL,
	`rpp_real_num` `decimal`(18, 2) NULL,
 CONSTRAINT `PK_r_spo_pro` PRIMARY KEY CLUSTERED 
(
	`rpp_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `cus_province`(
	`prv_id` `bigint` AUTO_INCREMENT NOT NULL,
	`prv_area_id` `bigint` NULL,
	`prv_name` `nvarchar`(100)  NULL,
	`prv_isenabled` `varchar`(10)  NULL,
 CONSTRAINT `PK_cus_province` PRIMARY KEY CLUSTERED 
(
	`prv_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `sal_supplier`(
	`ssu_id` `bigint` AUTO_INCREMENT NOT NULL,
	`ssu_code` `varchar`(300)  NULL,
	`ssu_name` `nvarchar`(100)  NULL,
	`ssu_phone` `varchar`(50)  NULL,
	`ssu_fex` `varchar`(50)  NULL,
	`ssu_email` `varchar`(50)  NULL,
	`ssu_net` `varchar`(200)  NULL,
	`ssu_add` `nvarchar`(max)  NULL,
	`ssu_prd` `nvarchar`(max)  NULL,
	`ssu_county` `bigint` NULL,
	`ssu_pro` `bigint` NULL,
	`ssu_city` `bigint` NULL,
	`ssu_zip_code` `varchar`(50)  NULL,
	`ssu_bank` `nvarchar`(50)  NULL,
	`ssu_bank_code` `varchar`(50)  NULL,
	`ssu_isdel` `char`(1)  NULL,
	`ssu_remark` `nvarchar`(max)  NULL,
	`ssu_inp_user` `nvarchar`(50)  NULL,
	`ssu_cre_date` `datetime` NULL,
	`ssu_alt_date` `datetime` NULL,
	`ssu_alt_user` `nvarchar`(50)  NULL,
	`ssu_bank_name` `nvarchar`(50)  NULL,
	`ssu_type_id` `bigint` NULL,
 CONSTRAINT `PK_sal_supplier` PRIMARY KEY CLUSTERED 
(
	`ssu_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `sal_all_task`(
	`sat_id` `bigint` AUTO_INCREMENT NOT NULL,
	`sat_date` `varchar`(50)  NULL,
	`sat_se_no` `bigint` NULL,
	`sat_inp_date` `datetime` NULL,
	`sat_alt_date` `datetime` NULL,
	`sat_inp_name` `nvarchar`(50)  NULL,
	`sat_alt_name` `nvarchar`(50)  NULL,
	`sat_ht_mon` `decimal`(18, 2) NULL,
	`sat_paid_mon` `decimal`(18, 2) NULL,
	`sat_cus_num` `int` NULL,
 CONSTRAINT `PK_sal_all_task` PRIMARY KEY CLUSTERED 
(
	`sat_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `type_list`(
	`typ_id` `bigint` AUTO_INCREMENT NOT NULL,
	`typ_name` `nvarchar`(50)  NULL,
	`typ_desc` `varchar`(max)  NULL,
	`typ_type` `varchar`(50)  NULL,
	`typ_isenabled` `char`(1)  NULL,
 CONSTRAINT `PK_type_list` PRIMARY KEY CLUSTERED 
(
	`typ_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
END
 AND name = N'IX_type_list')
CREATE NONCLUSTERED INDEX `IX_type_list` ON `type_list` 
(
	`typ_name` ASC
)WITH (IGNORE_DUP_KEY = OFF)
 AND name = N'IX_type_list_1')
CREATE NONCLUSTERED INDEX `IX_type_list_1` ON `type_list` 
(
	`typ_type` ASC
)WITH (IGNORE_DUP_KEY = OFF)
 AND name = N'IX_type_list_2')
CREATE NONCLUSTERED INDEX `IX_type_list_2` ON `type_list` 
(
	`typ_isenabled` ASC
)WITH (IGNORE_DUP_KEY = OFF)

 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `pho_red`(
	`pr_id` `bigint` AUTO_INCREMENT NOT NULL,
	`pr_typ_id` `bigint` NULL,
	`pr_contact` `nvarchar`(200)  NULL,
	`pr_cas_id` `bigint` NULL,
	`pr_content` `nvarchar`(max)  NULL,
	`pr_time` `datetime` NULL,
	`pr_se_no` `bigint` NULL,
	`pr_con_type` `nvarchar`(50)  NULL,
	`pr_pa_id` `bigint` NULL,
	`pr_name` `nvarchar`(50)  NULL,
	`pr_cat` `int` NULL,
	`pr_ptp_date` `datetime` NULL,
	`pr_ptp_num` `decimal`(18, 2) NULL,
	`pr_rel` `nvarchar`(50) NULL,
	`pr_state_id` `bigint` NULL,
	`pr_netiation` `nvarchar`(50) NULL,
	`pr_cc_id` `bigint` NULL,
	`pr_call_id` `varchar`(max) NULL,
	`pr_sms_id` `varchar`(max) NULL,
 CONSTRAINT `PK_pho_red` PRIMARY KEY CLUSTERED 
(
	`pr_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `spo_paid_plan`(
	`spp_id` `bigint` AUTO_INCREMENT NOT NULL,
	`spp_spo_id` `bigint` NULL,
	`spp_prm_date` `datetime` NULL,
	`spp_pay_mon` `decimal`(18, 2) NULL,
	`spp_inp_user` `nvarchar`(50)  NULL,
	`spp_resp` `varchar`(50)  NULL,
	`spp_isp` `char`(1)  NULL,
	`spp_cre_date` `datetime` NULL,
	`spp_alt_date` `datetime` NULL,
	`spp_alt_user` `nvarchar`(50)  NULL,
	`spp_isdel` `char`(1)  NULL,
	`spp_content` `nvarchar`(100)  NULL,
 CONSTRAINT `PK_spo_paid_plan` PRIMARY KEY CLUSTERED 
(
	`spp_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
END

EXEC dbo.sp_executesql @statement = N'






CREATE TABLE `pro_actor`(
	`act_id` `bigint` AUTO_INCREMENT NOT NULL,
	`act_pro_id` `bigint` NULL,
	`act_se_no` `bigint` NULL,
	`act_isdel` `char`(1)  NULL,
	`act_duty` `nvarchar`(100)  NULL,
 CONSTRAINT `PK_pro_actor` PRIMARY KEY CLUSTERED 
(
	`act_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `sup_contact`(
	`scn_id` `bigint` AUTO_INCREMENT NOT NULL,
	`scn_ssu_id` `bigint` NULL,
	`scn_name` `nvarchar`(50)  NULL,
	`scn_sex` `nvarchar`(50)  NULL,
	`scn_dep` `nvarchar`(max)  NULL,
	`scn_service` `nvarchar`(100)  NULL,
	`scn_phone` `varchar`(50)  NULL,
	`scn_work_pho` `varchar`(50)  NULL,
	`scn_home_pho` `varchar`(50)  NULL,
	`scn_fex` `varchar`(50)  NULL,
	`scn_zip_code` `varchar`(50)  NULL,
	`scn_email` `varchar`(100)  NULL,
	`scn_qq` `varchar`(50)  NULL,
	`scn_msn` `varchar`(100)  NULL,
	`scn_add` `nvarchar`(max)  NULL,
	`scn_oth_link` `nvarchar`(max)  NULL,
	`scn_remark` `nvarchar`(max)  NULL,
	`scn_inp_user` `nvarchar`(50)  NULL,
	`scn_upd_user` `nvarchar`(50)  NULL,
	`scn_cre_date` `datetime` NULL,
	`scn_mod_date` `datetime` NULL,
	`scn_isdel` `char`(1)  NULL,
 CONSTRAINT `PK_sup_contact` PRIMARY KEY CLUSTERED 
(
	`scn_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `pro_task_lim`(
	`ptl_id` `bigint` AUTO_INCREMENT NOT NULL,
	`ptl_prta_id` `bigint` NULL,
	`ptl_se_no` `bigint` NULL,
	`ptl_name` `nvarchar`(50)  NULL,
	`ptl_isfin` `char`(1)  NULL,
	`ptl_fin_date` `datetime` NULL,
	`ptl_isdel` `char`(1)  NULL,
	`ptl_desc` `nvarchar`(max)  NULL,
 CONSTRAINT `PK_pro_task_lim` PRIMARY KEY CLUSTERED 
(
	`ptl_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `cus_contact`(
	`con_id` `bigint` AUTO_INCREMENT NOT NULL,
	`con_cor_code` `bigint` NULL,
	`con_name` `nvarchar`(50)  NULL,
	`con_sex` `nvarchar`(10)  NULL,
	`con_dep` `nvarchar`(max)  NULL,
	`con_service` `nvarchar`(100)  NULL,
	`con_lev` `varchar`(50)  NULL,
	`con_phone` `varchar`(50)  NULL,
	`con_work_pho` `varchar`(50)  NULL,
	`con_home_pho` `varchar`(50)  NULL,
	`con_fex` `varchar`(50)  NULL,
	`con_zip_code` `varchar`(50)  NULL,
	`con_email` `varchar`(100)  NULL,
	`con_qq` `varchar`(50)  NULL,
	`con_msn` `varchar`(100)  NULL,
	`con_add` `nvarchar`(max)  NULL,
	`con_oth_link` `nvarchar`(max)  NULL,
	`con_bir` `datetime` NULL,
	`con_hob` `nvarchar`(100)  NULL,
	`con_taboo` `nvarchar`(100)  NULL,
	`con_edu` `nvarchar`(100)  NULL,
	`con_photo` `varchar`(max)  NULL,
	`con_remark` `nvarchar`(max)  NULL,
	`con_cre_date` `datetime` NULL,
	`con_mod_date` `datetime` NULL,
	`con_inp_user` `nvarchar`(50)  NULL,
	`con_upd_user` `nvarchar`(50)  NULL,
	`con_isdel` `char`(1)  NULL,
	`con_type` `nvarchar`(50)  NULL,
	`con_user_code` `varchar`(50)  NULL,
 CONSTRAINT `PK_cus_contact` PRIMARY KEY CLUSTERED 
(
	`con_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `wms_shipment`(
	`wsh_code` `varchar`(50)  NOT NULL,
	`wsh_wout_code` `varchar`(50)  NULL,
	`wsh_ord_code` `bigint` NULL,
	`wsh_state` `char`(1)  NULL,
	`wsh_out_date` `datetime` NULL,
	`wsh_inp_date` `datetime` NULL,
	`wsh_user_code` `varchar`(50)  NULL,
	`wsh_rec_man` `nvarchar`(50)  NULL,
	`wsh_type` `nvarchar`(50)  NULL,
	`wsh_cost` `decimal`(18, 2) NULL,
	`wsh_remark` `nvarchar`(max)  NULL,
 CONSTRAINT `PK_wms_shipment` PRIMARY KEY CLUSTERED 
(
	`wsh_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `r_wms_wms`(
	`rww_id` `bigint` AUTO_INCREMENT NOT NULL,
	`wch_id` `bigint` NULL,
	`rww_pro_id` `bigint` NULL,
	`rww_num` `decimal`(18, 2) NULL,
	`rww_remark` `nvarchar`(max)  NULL,
 CONSTRAINT `PK_r_wms_wms` PRIMARY KEY CLUSTERED 
(
	`rww_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `cus_area`(
	`are_id` `bigint` AUTO_INCREMENT NOT NULL,
	`are_name` `nvarchar`(100)  NULL,
	`are_isenabled` `varchar`(10)  NULL,
 CONSTRAINT `PK_cus_area` PRIMARY KEY CLUSTERED 
(
	`are_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
END

EXEC dbo.sp_executesql @statement = N'





CREATE TABLE `r_wout_pro`(
	`rwo_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rwo_wout_id` `bigint` NULL,
	`rwo_pro_id` `bigint` NULL,
	`rwo_wout_num` `decimal`(18, 2) NULL,
	`rwo_remark` `nvarchar`(max)  NULL,
 CONSTRAINT `PK_r_wout_pro` PRIMARY KEY CLUSTERED 
(
	`rwo_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `case_int`(
	`cin_id` `bigint` AUTO_INCREMENT NOT NULL,
	`cin_cas_id` `bigint` NULL,
	`cin_name` `nvarchar`(50)  NULL,
	`cin_m_cat` `nvarchar`(200)  NULL,
	`cin_m` `decimal`(18, 2) NULL,
	`cin_principal` `nvarchar`(200)  NULL,
	`cin_int` `nvarchar`(200)  NULL,
	`cin_overdue_paid` `nvarchar`(200)  NULL,
	`cin_over_limit` `nvarchar`(200)  NULL,
	`cin_service` `nvarchar`(200)  NULL,
	`cin_year` `nvarchar`(200)  NULL,
	`cin_other` `nvarchar`(200)  NULL,
	`cin_out` `nvarchar`(200)  NULL,
	`cin_ins_time` `datetime` NULL,
	`cin_end_date` `varchar`(100) NULL,
	`cin_damages_amt` `nvarchar`(200) NULL,
 CONSTRAINT `PK_case_int` PRIMARY KEY CLUSTERED 
(
	`cin_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `wms_war_in`(
	`wwi_id` `bigint` AUTO_INCREMENT NOT NULL,
	`wwi_code` `varchar`(50)  NULL,
	`wwi_title` `nvarchar`(max)  NULL,
	`wwi_stro_code` `varchar`(50)  NULL,
	`wwi_user_code` `varchar`(50)  NULL,
	`wwi_state` `char`(1)  NULL,
	`wwi_remark` `nvarchar`(max)  NULL,
	`wwi_isdel` `char`(1)  NULL,
	`wwi_inp_name` `nvarchar`(50)  NULL,
	`wwi_alt_name` `nvarchar`(50)  NULL,
	`wwi_inp_time` `datetime` NULL,
	`wwi_alt_time` `datetime` NULL,
	`wwi_in_date` `datetime` NULL,
	`wwi_app_date` `datetime` NULL,
	`wwi_app_man` `nvarchar`(50)  NULL,
	`wwi_app_desc` `nvarchar`(max)  NULL,
	`wwi_app_isok` `char`(1)  NULL,
	`wwi_spo_code` `bigint` NULL,
	`wwi_can_date` `datetime` NULL,
	`wwi_can_man` `nvarchar`(50)  NULL,
 CONSTRAINT `PK_wms_war_in_1` PRIMARY KEY CLUSTERED 
(
	`wwi_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `r_win_pro`(
	`rwi_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rwi_win_id` `bigint` NULL,
	`rwi_pro_id` `bigint` NULL,
	`rwi_win_num` `decimal`(18, 2) NULL,
	`rwi_remark` `nvarchar`(max)  NULL,
 CONSTRAINT `PK_r_win_pro` PRIMARY KEY CLUSTERED 
(
	`rwi_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `wms_stro`(
	`wms_code` `varchar`(50)  NOT NULL,
	`wms_name` `nvarchar`(300)  NULL,
	`wms_type_id` `bigint` NULL,
	`wms_loc` `nvarchar`(max)  NULL,
	`wms_cre_date` `datetime` NULL,
	`wms_user_code` `varchar`(50)  NULL,
	`wms_remark` `nvarchar`(max)  NULL,
	`wms_isenabled` `char`(1)  NULL,
 CONSTRAINT `PK_wms_id` PRIMARY KEY CLUSTERED 
(
	`wms_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `case_grp`(
	`cg_id` `bigint` AUTO_INCREMENT NOT NULL,
	`cg_name` `nvarchar`(30)  NULL,
	`cg_user_code` `varchar`(50)  NULL,
 CONSTRAINT `PK_case_grp` PRIMARY KEY CLUSTERED 
(
	`cg_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `lim_right`(
	`rig_code` `varchar`(50)  NOT NULL,
	`rig_fun_code` `varchar`(50)  NULL,
	`rig_ope_code` `varchar`(50)  NULL,
	`rig_wms_name` `nvarchar`(300)  NULL,
 CONSTRAINT `PK_lim_right` PRIMARY KEY CLUSTERED 
(
	`rig_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `cus_city`(
	`city_id` `bigint` AUTO_INCREMENT NOT NULL,
	`city_prv_id` `bigint` NULL,
	`city_name` `nvarchar`(100)  NULL,
	`city_isenabled` `varchar`(10)  NULL,
 CONSTRAINT `PK_cus_city` PRIMARY KEY CLUSTERED 
(
	`city_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `wms_change`(
	`wch_id` `bigint` AUTO_INCREMENT NOT NULL,
	`wch_code` `varchar`(50)  NULL,
	`wch_title` `nvarchar`(max)  NULL,
	`wch_state` `char`(1)  NULL,
	`wch_in_date` `datetime` NULL,
	`wch_out_wms` `varchar`(50)  NULL,
	`wch_in_wms` `varchar`(50)  NULL,
	`wch_rec_man` `varchar`(50)  NULL,
	`wch_remark` `nvarchar`(max)  NULL,
	`wch_checkIn` `varchar`(50)  NULL,
	`wch_checkOut` `varchar`(50)  NULL,
	`wch_out_date` `datetime` NULL,
	`wch_isdel` `char`(1)  NULL,
	`wch_in_time` `datetime` NULL,
	`wch_out_time` `datetime` NULL,
	`wch_inp_name` `nvarchar`(50)  NULL,
	`wch_inp_date` `datetime` NULL,
	`wch_alt_name` `nvarchar`(50)  NULL,
	`wch_alt_date` `datetime` NULL,
	`wch_app_date` `datetime` NULL,
	`wch_app_man` `nvarchar`(50)  NULL,
	`wch_app_desc` `nvarchar`(max)  NULL,
	`wch_app_isok` `char`(1)  NULL,
	`wch_mat_name` `nvarchar`(50)  NULL,
	`wch_can_date` `datetime` NULL,
	`wch_can_man` `nvarchar`(50)  NULL,
 CONSTRAINT `PK_wms_change` PRIMARY KEY CLUSTERED 
(
	`wch_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `attachment`(
	`att_id` `bigint` AUTO_INCREMENT NOT NULL,
	`att_name` `nvarchar`(max)  NULL,
	`att_size` `bigint` NULL,
	`att_path` `varchar`(max)  NULL,
	`att_isJunk` `char`(1)  NULL,
	`att_date` `datetime` NULL,
	`att_type` `varchar`(100)  NULL,
	`att_fk_id` `bigint` NULL,
	`att_file_type` `varchar`(100) NULL,
 CONSTRAINT `PK_attachment` PRIMARY KEY CLUSTERED 
(
	`att_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `lim_function`(
	`fun_code` `varchar`(50)  NOT NULL,
	`fun_desc` `nvarchar`(max)  NULL,
	`fun_type` `varchar`(50)  NULL,
 CONSTRAINT `PK_lim_function_1` PRIMARY KEY CLUSTERED 
(
	`fun_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `bank_case`(
	`cas_id` `bigint` AUTO_INCREMENT NOT NULL,
	`cas_code` `varchar`(100)  NULL,
	`cas_group` `varchar`(50)  NULL,
	`cas_state` `int` NULL,
	`cas_typ_hid` `bigint` NULL,
	`cas_out_state` `int` NULL,
	`cas_cbat_id` `bigint` NULL,
	`cas_m` `decimal`(18, 2) NULL,
	`cas_ptp_m` `decimal`(18, 2) NULL,
	`cas_cp_m` `decimal`(18, 2) NULL,
	`cas_paid_m` `decimal`(18, 2) NULL,
	`cas_date` `datetime` NULL,
	`cas_typ_bid` `bigint` NULL,
	`cas_name` `nvarchar`(50)  NULL,
	`cas_sex` `nvarchar`(1)  NULL,
	`cas_ca_cd` `varchar`(50)  NULL,
	`cas_num` `varchar`(50)  NULL,
	`cas_area_1``nvarchar`(50)  NULL,
	`cas_area_2``nvarchar`(50)  NULL,
	`cas_area_3``nvarchar`(50)  NULL,
	`cas_post_code` `varchar`(50)  NULL,
	`cas_se_no` `bigint` NULL,
	`cas_ins_user` `nvarchar`(25)  NULL,
	`cas_ins_time` `datetime` NULL,
	`cas_alt_user` `nvarchar`(25)  NULL,
	`cas_alt_time` `datetime` NULL,
	`cas_tremark` `nvarchar`(300)  NULL,
	`cas_warn` `nvarchar`(300)  NULL,
	`cas_acc_num` `varchar`(100)  NULL,
	`cas_card_cat` `nvarchar`(200)  NULL,
	`cas_principal` `nvarchar`(200)  NULL,
	`cas_min_paid` `nvarchar`(200)  NULL,
	`cas_cred_lim` `nvarchar`(200)  NULL,
	`cas_delay_lv` `nvarchar`(200)  NULL,
	`cas_gua_m` `nvarchar`(200)  NULL,
	`cas_m_cat` `nvarchar`(200)  NULL,
	`cas_pre_rec` `nvarchar`(max)  NULL,
	`cas_exc_lim` `nvarchar`(200)  NULL,
	`cas_unit_name` `nvarchar`(200)  NULL,
	`cas_m_p` `float` NULL,
	`cas_name_1` `nvarchar`(50)  NULL,
	`cas_name_2` `nvarchar`(50)  NULL,
	`cas_name_3` `nvarchar`(50)  NULL,
	`cas_num_1` `nvarchar`(200)  NULL,
	`cas_num_2` `nvarchar`(200)  NULL,
	`cas_num_3` `nvarchar`(200)  NULL,
	`cas_re_1` `nvarchar`(200)  NULL,
	`cas_re_2` `nvarchar`(200)  NULL,
	`cas_re_3` `nvarchar`(200)  NULL,
	`cas_con_com1` `nvarchar`(200)  NULL,
	`cas_pr_time` `datetime` NULL,
	`cas_remark` `nvarchar`(max)  NULL,
	`cas_con_com2` `nvarchar`(200)  NULL,
	`cas_con_com3` `nvarchar`(200)  NULL,
	`cas_app_1` `int` NULL,
	`cas_app_2` `int` NULL,
	`cas_app_3` `int` NULL,
	`cas_app_4` `int` NULL,
	`cas_app_5` `int` NULL,
	`cas_app_6` `int` NULL,
	`cas_app_7` `int` NULL,
	`cas_app_8` `int` NULL,
	`cas_app_9` `int` NULL,
	`cas_app_10` `int` NULL,
	`cas_app_11` `int` NULL,
	`cas_app_12` `int` NULL,
	`cas_app_13` `int` NULL,
	`cas_app_14` `int` NULL,
	`cas_remark2` `nvarchar`(max)  NULL,
	`cas_remark3` `nvarchar`(max)  NULL,
	`cas_remark4` `nvarchar`(max)  NULL,
	`cas_ptp_c` `int` NULL,
	`cas_remark5` `nvarchar`(max)  NULL,
	`cas_card_bank` `nvarchar`(200)  NULL,
	`cas_tip_time` `datetime` NULL,
	`cas_hom_pho` `varchar`(50)  NULL,
	`cas_work_pho` `varchar`(50)  NULL,
	`cas_mob_pho` `varchar`(50)  NULL,
	`cas_hom_add` `varchar`(max)  NULL,
	`cas_work_add` `varchar`(max)  NULL,
	`cas_mail_add` `varchar`(max)  NULL,
	`cas_reg_add` `varchar`(max)  NULL,
	`cas_con_pho1` `varchar`(50)  NULL,
	`cas_con_mob1` `varchar`(50)  NULL,
	`cas_con_add1` `varchar`(max)  NULL,
	`cas_con_pho2` `varchar`(50)  NULL,
	`cas_con_mob2` `varchar`(50)  NULL,
	`cas_con_add2` `varchar`(max)  NULL,
	`cas_loan_type` `nvarchar`(200)  NULL,
	`cas_coll_type` `nvarchar`(200)  NULL,
	`cas_int` `nvarchar`(200)  NULL,
	`cas_overdue_paid` `nvarchar`(200)  NULL,
	`cas_cre_paid` `nvarchar`(200)  NULL,
	`cas_paid_lim` `nvarchar`(200)  NULL,
	`cas_paid_date` `nvarchar`(200)  NULL,
	`cas_con_date` `nvarchar`(200)  NULL,
	`cas_rai_date` `nvarchar`(200)  NULL,
	`cas_stop_date` `nvarchar`(200)  NULL,
	`cas_cre_date` `nvarchar`(200)  NULL,
	`cas_remark6` `nvarchar`(max)  NULL,
	`cas_note` `nvarchar`(max)  NULL,
	`cas_con_pho3` `varchar`(50)  NULL,
	`cas_con_mob3` `varchar`(50)  NULL,
	`cas_con_add3` `varchar`(max)  NULL,
	`cas_con_pho4` `varchar`(50)  NULL,
	`cas_con_mob4` `varchar`(50)  NULL,
	`cas_con_add4` `varchar`(max)  NULL,
	`cas_name_4` `nvarchar`(50)  NULL,
	`cas_num_4` `nvarchar`(200)  NULL,
	`cas_re_4` `nvarchar`(200)  NULL,
	`cas_con_com4` `nvarchar`(200)  NULL,
	`cas_file_no` `nvarchar`(100)  NULL,
	`cas_remark7` `nvarchar`(max)  NULL,
	`cas_remark8` `nvarchar`(max)  NULL,
	`cas_email` `varchar`(100)  NULL,
	`cas_is_oth` `int` NULL,
	`cas_is_newpr` `int` NULL,
	`cas_is_newpaid` `int` NULL,
	`cas_is_paidover` `int` NULL,
	`cas_is_updint` `int` NULL,
	`cas_rmb` `varchar`(100) NULL,
	`cas_gb` `varchar`(100) NULL,
	`cas_my` `varchar`(100) NULL,
	`cas_pos` `nvarchar`(200)  NULL,
	`cas_part` `nvarchar`(200)  NULL,
	`cas_backdate_p` `datetime` NULL,
	`cas_backdate` `datetime` NULL,
	`cas_back_p` `float` NULL,
	`cas_con_wpho1` `varchar`(50)  NULL,
	`cas_con_wpho2` `varchar`(50)  NULL,
	`cas_con_wpho3` `varchar`(50)  NULL,
	`cas_con_wpho4` `varchar`(50)  NULL,
	`cas_name_u` `nvarchar`(50)  NULL,
	`cas_num_u` `nvarchar`(200)  NULL,
	`cas_re_u` `nvarchar`(200)  NULL,
	`cas_con_u_com` `nvarchar`(200)  NULL,
	`cas_con_u_wpho` `varchar`(50)  NULL,
	`cas_con_u_pho` `varchar`(50)  NULL,
	`cas_con_u_mob` `varchar`(50)  NULL,
	`cas_con_u_add` `varchar`(max)  NULL,
	`cas_back_m` `decimal`(18, 2) NULL,
	`cas_name_5` `nvarchar`(50)  NULL,
	`cas_num_5` `nvarchar`(200)  NULL,
	`cas_re_5` `nvarchar`(200)  NULL,
	`cas_con_com_5` `nvarchar`(200)  NULL,
	`cas_con_wpho_5` `varchar`(50)  NULL,
	`cas_con_pho_5` `varchar`(50)  NULL,
	`cas_con_mob_5` `varchar`(50)  NULL,
	`cas_con_add_5` `varchar`(max)  NULL,
	`cas_loan_date` `varchar`(200)  NULL,
	`cas_app_no` `varchar`(100)  NULL,
	`cas_paid_count` `varchar`(100)  NULL,
	`cas_so_pcno` `varchar`(100)  NULL,
	`cas_so_no` `varchar`(100)  NULL,
	`cas_overdue_date` `varchar`(200)  NULL,
	`cas_pback_p` `float` NULL,
	`cas_wpost_code` `varchar`(50) NULL,
	`cas_deadline` `varchar`(200) NULL,
	`cas_is_host` `nvarchar`(50) NULL,
	`cas_bill_date` `varchar`(200) NULL,
	`cas_last_paid` `nvarchar`(200) NULL,
	`cas_count` `varchar`(100) NULL,
	`cas_left_pri` `varchar`(100) NULL,
	`cas_assign_ids` `varchar`(max) NULL,
	`cas_assign_names` `nvarchar`(max) NULL,
	`cas_last_assign_time` `datetime` NULL,
	`cas_overdue_days` `int` NULL,
	`cas_overdue_days_str` `varchar`(200) NULL,
	`cas_bir` `varchar`(50) NULL,
	`cas_mpost_code` `varchar`(50) NULL,
	`cas_perm_crline` `varchar`(50) NULL,
	`cas_alt_hold` `nvarchar`(50) NULL,
	`cas_cycle` `varchar`(50) NULL,
	`cas_noout` `varchar`(50) NULL,
	`cas_field_type` `varchar`(50) NULL,
	`cas_cl_area_id` `bigint` NULL,
	`cas_pr_count` `int` NULL,
	`cas_overdue_m` `varchar`(200) NULL,
	`cas_overdue_num` `varchar`(200) NULL,
	`cas_overdue_once` `int` NULL,
	`cas_loan_rate` `varchar`(200) NULL,
	`cas_month_paid` `varchar`(200) NULL,
	`cas_last_vis` `datetime` NULL,
	`cas_fst_cl_paid_date` `datetime` NULL,
	`cas_last_cl_paid_date` `datetime` NULL,
	`cas_color` `int` NULL,
	`cas_cc_id` `bigint` NULL,
	`cas_is_newass` `int` NULL,
	`cas_reg_post_code` `varchar`(50) NULL,
	`cas_last_m` `decimal`(18,2) NULL,
	`cas_last_int_date` `datetime` NULL,
	`cas_loan_end_date` `varchar`(200) NULL,
	`cas_over_limit` `nvarchar`(200) NULL,
	`cas_num_type` `nvarchar`(50) NULL,
	`cas_last_end_date` `varchar`(100) NULL,
	`cas_assign_times` `varchar`(max) NULL,
	`cas_cl_count` `nvarchar`(100) NULL,
 CONSTRAINT `PK_case` PRIMARY KEY CLUSTERED 
(
	`cas_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
END
 AND name = N'IX_bank_case_1')
CREATE NONCLUSTERED INDEX `IX_bank_case_1` ON `bank_case` 
(
	`cas_se_no` ASC
)WITH (IGNORE_DUP_KEY = OFF)
 AND name = N'IX_case_code')
CREATE NONCLUSTERED INDEX `IX_case_code` ON `bank_case` 
(
	`cas_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)
create index IX_cnum on bank_case(cas_num);
create index IX_cacd on bank_case(cas_ca_cd);
create index IX_bank on bank_case(cas_typ_bid);
create index IX_cl_area on bank_case(cas_cl_area_id);
create index IX_bat on bank_case(cas_cbat_id);
create index IX_file_no on bank_case(cas_file_no);
create index IX_acc_date on bank_case(cas_acc_num, cas_date);
create index IX_cacd_date on bank_case(cas_ca_cd, cas_date);

SET ANSI_PADDING ON
CREATE TABLE `oth_card`(
	`ocd_id` `bigint` AUTO_INCREMENT NOT NULL,
	`ocd_cas_id` `bigint` NULL,
	`ocd_area` `nvarchar`(50)  NULL,
	`ocd_acct` `varchar`(50)  NULL,
	`ocd_id_no` `varchar`(50)  NULL,
	`ocd_card` `varchar`(50)  NULL,
	`ocd_name` `nvarchar`(50)  NULL,
	`ocd_h_pho` `varchar`(50)  NULL,
	`ocd_o_pho` `varchar`(50)  NULL,
	`ocd_addr` `nvarchar`(200)  NULL,
	`ocd_employer` `nvarchar`(100)  NULL,
	`ocd_alt_name` `nvarchar`(50)  NULL,
	`ocd_alt_h_pho` `varchar`(50)  NULL,
	`ocd_alt_o_pho` `varchar`(50)  NULL,
	`ocd_con_name` `nvarchar`(50)  NULL,
	`ocd_con_pho` `varchar`(50)  NULL,
	`ocd_bir` `varchar`(50)  NULL,
	`ocd_r_addr` `nvarchar`(200)  NULL,
	`ocd_cyc` `varchar`(50)  NULL,
	`ocd_blk` `varchar`(50)  NULL,
	`ocd_m_post` `varchar`(50)  NULL,
	`ocd_msg` `nvarchar`(200)  NULL,
	`ocd_cre_man` `nvarchar`(50)  NULL,
	`ocd_cre_time` `datetime` NULL,
	`ocd_upd_man` `nvarchar`(50)  NULL,
	`ocd_upd_time` `datetime` NULL,
 CONSTRAINT `PK_oth_card` PRIMARY KEY CLUSTERED 
(
	`ocd_id` ASC
)WITH (IGNORE_DUP_KEY = OFF) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF

SET ANSI_PADDING ON
CREATE TABLE `pa_bank_case`(
	`cas_id` `bigint` NOT NULL,
	`cas_policy_man` `nvarchar`(50) NULL,
	`cas_day_paid` `varchar`(100) NULL,
	`cas_cla_date` `varchar`(100) NULL,
	`cas_cla_m` `varchar`(100) NULL,
	`cas_eff_date` `varchar`(100) NULL,
	`cas_premiums` `varchar`(100) NULL,
	`cas_manage_cost` `varchar`(100) NULL,
	`cas_penalty` `varchar`(100) NULL,
	`cas_fail_cost` `varchar`(100) NULL,
	`cas_is_wd` `varchar`(100) NULL,
	`cas_acc_name` `nvarchar`(100) NULL,
	`cas_house_type1` `nvarchar`(100) NULL,
	`cas_house_own1` `nvarchar`(100) NULL,
	`cas_hfax1` `varchar`(100) NULL,
	`cas_addr2` `nvarchar`(500) NULL,
	`cas_house_type2` `nvarchar`(100) NULL,
	`cas_house_own2` `nvarchar`(100) NULL,
	`cas_pho2` `varchar`(100) NULL,
	`cas_hfax2` `varchar`(100) NULL,
	`cas_com2` `nvarchar`(200) NULL,
	`cas_com2_addr` `nvarchar`(500) NULL,
	`cas_wpho2` `varchar`(100) NULL,
	`cas_fax1` `varchar`(100) NULL,
	`cas_fax2` `varchar`(100) NULL,
	`cas_com_type` `nvarchar`(50) NULL,
	`cas_com_date` `varchar`(100) NULL,
	`cas_tax_no` `varchar`(100) NULL,
	`cas_com_no` `varchar`(100) NULL,
	`cas_con_mob4b` `varchar`(100) NULL,
	`cas_con_wpho4` `varchar`(100) NULL,
	`cas_con_mob1b` `varchar`(100) NULL,
	`cas_con_mob2b` `varchar`(100) NULL,
	`cas_con_mob3b` `varchar`(100) NULL,
 CONSTRAINT `PK_pa_bank_case` PRIMARY KEY CLUSTERED 
(
	`cas_id` ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF

SET ANSI_PADDING ON
CREATE TABLE `js_bank_case`(
	`cas_id` `bigint` NOT NULL,
	`jsc_overdue_p` `varchar`(200) NULL,
	`jsc_un_overdue_p` `varchar`(200) NULL,
	`jsc_man` `nvarchar`(100) NULL,
	`jsc_cd` `varchar`(200) NULL,
	`jsc_is_married` `nvarchar`(50) NULL,
	`jsc_oth_pho` `varchar`(200) NULL,
	`jsc_ide` `nvarchar`(100) NULL,
	`jsc_home_prop` `nvarchar`(100) NULL,
	`jsc_live_with` `nvarchar`(100) NULL,
	`jsc_haddr_able` `varchar`(200) NULL,
	`jsc_waddr_able` `varchar`(200) NULL,
	`jsc_com_prop` `nvarchar`(100) NULL,
	`jsc_age1` `varchar`(50) NULL,
	`jsc_pos1` `nvarchar`(200) NULL,
	`jsc_age2` `varchar`(50) NULL,
	`jsc_pos2` `nvarchar`(200) NULL,
	`jsc_age3` `varchar`(50) NULL,
	`jsc_pos3` `nvarchar`(200) NULL,
	`jsc_cost` `varchar`(200) NULL,
	`jsc_posu` `nvarchar`(200) NULL,
 CONSTRAINT `PK_js_bank_case` PRIMARY KEY CLUSTERED 
(
	`cas_id` ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF

CREATE TABLE `project`(
	`pro_id` `bigint` AUTO_INCREMENT NOT NULL,
	`pro_user_code` `varchar`(50)  NULL,
	`pro_typ_id` `bigint` NULL,
	`pro_title` `nvarchar`(300)  NULL,
	`pro_state` `varchar`(50)  NULL,
	`pro_cre_date` `datetime` NULL,
	`pro_fin_date` `datetime` NULL,
	`pro_desc` `nvarchar`(max)  NULL,
	`pro_remark` `nvarchar`(max)  NULL,
	`pro_inp_user` `nvarchar`(50)  NULL,
	`pro_upd_user` `nvarchar`(50)  NULL,
	`pro_ins_date` `datetime` NULL,
	`pro_mod_date` `datetime` NULL,
	`pro_isdel` `char`(1)  NULL,
	`pro_cor_code` `bigint` NULL,
	`pro_period` `varchar`(50)  NULL,
	`pro_pro` `nvarchar`(300)  NULL,
	`pro_pro_log` `nvarchar`(max)  NULL,
 CONSTRAINT `PK_project` PRIMARY KEY CLUSTERED 
(
	`pro_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `wms_war_out`(
	`wwo_id` `bigint` AUTO_INCREMENT NOT NULL,
	`wwo_ord_code` `bigint` NULL,
	`wwo_title` `nvarchar`(max)  NULL,
	`wwo_stro_code` `varchar`(50)  NULL,
	`wwo_user_code` `varchar`(50)  NULL,
	`wwo_inp_date` `datetime` NULL,
	`wwo_out_date` `datetime` NULL,
	`wwo_state` `char`(1)  NULL,
	`wwo_remark` `nvarchar`(max)  NULL,
	`wwo_isdel` `char`(1)  NULL,
	`wwo_inp_name` `nvarchar`(50)  NULL,
	`wwo_alt_name` `nvarchar`(50)  NULL,
	`wwo_user_name` `nvarchar`(50)  NULL,
	`wwo_res_name` `nvarchar`(50)  NULL,
	`wwo_alt_date` `datetime` NULL,
	`wwo_code` `varchar`(50)  NULL,
	`wwo_app_isok` `char`(1)  NULL,
	`wwo_app_date` `datetime` NULL,
	`wwo_app_man` `nvarchar`(50)  NULL,
	`wwo_app_desc` `nvarchar`(max)  NULL,
	`wwo_can_date` `datetime` NULL,
	`wwo_can_man` `nvarchar`(50)  NULL,
 CONSTRAINT `PK_wms_war_out` PRIMARY KEY CLUSTERED 
(
	`wwo_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `r_wms_change`(
	`rwc_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rwc_pro_id` `bigint` NULL,
	`rwc_different` `decimal`(18, 2) NULL,
	`rmc_type` `varchar`(50)  NULL,
	`rmc_remark` `nvarchar`(max)  NULL,
	`rwc_wmc_code` `bigint` NULL,
	`rmc_wms_count` `decimal`(18, 2) NULL,
	`rmc_real_num` `decimal`(18, 2) NULL,
 CONSTRAINT `PK_r_wms_change` PRIMARY KEY CLUSTERED 
(
	`rwc_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `r_user_rig`(
	`rur_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rur_user_code` `varchar`(50)  NULL,
	`rur_rig_code` `varchar`(50)  NULL,
	`rur_type` `varchar`(50)  NULL,
 CONSTRAINT `PK_r_user_rig` PRIMARY KEY CLUSTERED 
(
	`rur_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `case_bat`(
	`cbat_id` `bigint` AUTO_INCREMENT NOT NULL,
	`cbat_code` `varchar`(50)  NULL,
	`cbat_typ_bid` `bigint` NULL,
	`cbat_date` `datetime` NULL,
	`cbat_type_id` `bigint` NULL,
	`cbat_backdate_p` `datetime` NULL,
	`cbat_backdate` `datetime` NULL,
	`cbat_ins_user` `nvarchar`(25)  NULL,
	`cbat_ins_date` `datetime` NULL,
	`cbat_state` `int` NULL,
	`cbat_num` `int` NULL,
	`cbat_mon` `decimal`(18, 2) NULL,
	`cbat_log` `nvarchar`(max)  NULL,
	`cbat_xls` `varchar`(max)  NULL,
	`cbat_up_date` `datetime` NULL,
	`cbat_remark` `nvarchar`(max)  NULL,
	`cbat_tips` `nvarchar`(200)  NULL,
	`cbat_area_id` `bigint` NULL,
	`cbat_target` `float` NULL,
 CONSTRAINT `PK__case_bat__0B5D7B41` PRIMARY KEY CLUSTERED 
(
	`cbat_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
END
SET ANSI_PADDING ON
CREATE TABLE `extra_inf`(
	`exi_id` `bigint` AUTO_INCREMENT NOT NULL,
	`exi_id_number` `varchar`(50) NULL,
	`exi_type` `nvarchar`(50) NULL,
	`exi_content` `text` NULL,
	`exi_cre_time` `datetime` NULL,
	`exi_cre_man` `nvarchar`(25) NULL,
	`exi_upd_time` `datetime` NULL,
	`exi_upd_man` `nvarchar`(25) NULL,
	`exi_name` `nvarchar`(50) NULL,
	`exi_remark` `text` NULL,
 CONSTRAINT `PK_extra_inf` PRIMARY KEY CLUSTERED 
(
	`exi_id` ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON `PRIMARY`
) ON `PRIMARY` TEXTIMAGE_ON `PRIMARY`

SET ANSI_PADDING OFF

SET ANSI_PADDING ON
CREATE TABLE `case_collection`(
	`cc_id` `bigint` AUTO_INCREMENT NOT NULL,
	`cc_cas_ids` `varchar`(max) NULL,
	`cc_cbat_id` `bigint` NULL,
	`cc_id_no` `varchar`(50) NULL,
 CONSTRAINT `PK_case_collection` PRIMARY KEY CLUSTERED 
(
	`cc_id` ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF

SET ANSI_PADDING ON
CREATE TABLE `user_area`(
	`uar_id` `bigint` AUTO_INCREMENT NOT NULL,
	`uar_user_code` `varchar`(50) NULL,
	`uar_area_id` `bigint` NULL,
 CONSTRAINT `PK_user_area` PRIMARY KEY CLUSTERED 
(
	`uar_id` ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF

CREATE TABLE `pro_stage`(
	`sta_id` `bigint` AUTO_INCREMENT NOT NULL,
	`sta_pro_id` `bigint` NULL,
	`sta_title` `nvarchar`(300)  NULL,
	`sta_aim` `nvarchar`(300)  NULL,
	`sta_start_date` `datetime` NULL,
	`sta_end_date` `datetime` NULL,
	`sta_remark` `nvarchar`(max)  NULL,
	`sta_ins_date` `datetime` NULL,
	`sta_mod_date` `datetime` NULL,
	`sta_isdel` `char`(1)  NULL,
	`sta_inp_user` `nvarchar`(50)  NULL,
	`sta_upd_user` `nvarchar`(50)  NULL,
 CONSTRAINT `PK_pro_stage` PRIMARY KEY CLUSTERED 
(
	`sta_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `sal_org`(
	`so_code` `varchar`(50)  NOT NULL,
	`so_name` `nvarchar`(50)  NULL,
	`so_con_area` `nvarchar`(max)  NULL,
	`so_loc` `nvarchar`(max)  NULL,
	`so_user_code` `varchar`(50)  NULL,
	`so_emp_num` `varchar`(50)  NULL,
	`so_resp` `nvarchar`(max)  NULL,
	`so_org_code` `varchar`(50)  NULL,
	`so_remark` `nvarchar`(max)  NULL,
	`so_isenabled` `char`(1)  NULL,
	`so_up_code` `varchar`(50)  NULL,
	`so_cost_center` `nvarchar`(100)  NULL,
	`so_org_nature` `nvarchar`(100)  NULL,
 CONSTRAINT `PK_sal_org` PRIMARY KEY CLUSTERED 
(
	`so_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `r_ord_pro`(
	`rop_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rop_ord_code` `bigint` NULL,
	`rop_pro_id` `bigint` NULL,
	`rop_num` `decimal`(18, 2) NULL,
	`rop_real_price` `decimal`(18, 2) NULL,
	`rop_remark` `nvarchar`(max)  NULL,
	`rop_price` `decimal`(18, 2) NULL,
	`rop_zk` `varchar`(50)  NULL,
	`rop_out_num` `decimal`(18, 2) NULL,
	`rop_real_num` `decimal`(18, 2) NULL,
 CONSTRAINT `PK_r_ord_pro` PRIMARY KEY CLUSTERED 
(
	`rop_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `message`(
	`me_code` `bigint` AUTO_INCREMENT NOT NULL,
	`me_title` `nvarchar`(100)  NULL,
	`me_content` `nvarchar`(max)  NULL,
	`me_se_no` `bigint` NULL,
	`me_date` `datetime` NULL,
	`me_issend` `char`(1)  NULL,
	`me_isdel` `char`(1)  NULL,
	`me_ins_user` `nvarchar`(50)  NULL,
	`me_rec_name` `nvarchar`(max)  NULL,
 CONSTRAINT `PK_message` PRIMARY KEY CLUSTERED 
(
	`me_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `lock_table`(
	`table_name` `varchar`(50)  NOT NULL,
	`table_max` `bigint` NULL,
 CONSTRAINT `PK_lock_table` PRIMARY KEY CLUSTERED 
(
	`table_name` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `r_mess_lim`(
	`rml_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rml_me_code` `bigint` NULL,
	`rml_se_no` `bigint` NULL,
	`rml_date` `datetime` NULL,
	`rml_isdel` `char`(1)  NULL,
	`rml_isread` `char`(1)  NULL,
	`rml_isreply` `char`(1)  NULL,
	`rml_rec_user` `nvarchar`(50)  NULL,
	`rml_state` `char`(1)  NULL,
 CONSTRAINT `PK_r_mess_lim` PRIMARY KEY CLUSTERED 
(
	`rml_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `wms_pro_type`(
	`wpt_id` `bigint` AUTO_INCREMENT NOT NULL,
	`wpt_name` `nvarchar`(50)  NULL,
	`wpt_desc` `nvarchar`(max)  NULL,
	`wpt_isenabled` `varchar`(10)  NULL,
	`wpt_up_id` `bigint` NULL,
 CONSTRAINT `PK_wms_pro_type` PRIMARY KEY CLUSTERED 
(
	`wpt_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `news`(
	`new_code` `bigint` AUTO_INCREMENT NOT NULL,
	`new_title` `nvarchar`(100)  NULL,
	`new_type` `nvarchar`(100)  NULL,
	`new_se_no` `bigint` NULL,
	`new_content` `nvarchar`(max)  NULL,
	`new_istop` `char`(1)  NULL,
	`new_date` `datetime` NULL,
	`new_ins_user` `nvarchar`(50)  NULL,
	`new_upd_user` `nvarchar`(50)  NULL,
	`new_upd_date` `datetime` NULL,
 CONSTRAINT `PK_news` PRIMARY KEY CLUSTERED 
(
	`new_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `lim_operate`(
	`ope_code` `varchar`(50)  NOT NULL,
	`ope_desc` `nvarchar`(max)  NULL,
 CONSTRAINT `PK_lim_operate` PRIMARY KEY CLUSTERED 
(
	`ope_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `schedule`(
	`sch_id` `bigint` AUTO_INCREMENT NOT NULL,
	`sch_type` `bigint` NULL,
	`sch_title` `nvarchar`(100)  NULL,
	`sch_start_date` `datetime` NULL,
	`sch_se_no` `bigint` NULL,
	`sch_start_time` `varchar`(50)  NULL,
	`sch_date` `datetime` NULL,
	`sch_state` `varchar`(50)  NULL,
	`sch_end_time` `varchar`(50)  NULL,
	`sch_ins_user` `nvarchar`(50)  NULL,
	`sch_upd_user` `nvarchar`(50)  NULL,
	`sch_upd_date` `datetime` NULL,
 CONSTRAINT `PK_schedule` PRIMARY KEY CLUSTERED 
(
	`sch_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `sal_invoice`(
	`sin_id` `bigint` AUTO_INCREMENT NOT NULL,
	`sin_ord_code` `bigint` NULL,
	`sin_con` `nvarchar`(max)  NULL,
	`sin_type` `bigint` NULL,
	`sin_mon` `decimal`(18, 2) NULL,
	`sin_date` `datetime` NULL,
	`sin_remark` `nvarchar`(max)  NULL,
	`sin_code` `varchar`(100)  NULL,
	`sin_isPaid` `char`(1)  NULL,
	`sin_isPlaned` `nvarchar`(50)  NULL,
	`sin_user_code` `nvarchar`(50)  NULL,
	`sin_resp` `nvarchar`(50)  NULL,
	`sin_mon_type` `nvarchar`(50)  NULL,
	`sin_alt_user` `nvarchar`(50)  NULL,
	`sin_cre_date` `datetime` NULL,
	`sin_alt_date` `datetime` NULL,
	`sin_isdel` `char`(1)  NULL,
	`sin_spo_id` `bigint` NULL,
	`sin_isrecieve` `char`(1)  NULL,
 CONSTRAINT `PK_sal_invoice` PRIMARY KEY CLUSTERED 
(
	`sin_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `report`(
	`rep_code` `bigint` AUTO_INCREMENT NOT NULL,
	`rep_title` `nvarchar`(100)  NULL,
	`rep_content` `nvarchar`(max)  NULL,
	`rep_se_no` `bigint` NULL,
	`rep_appro_content` `nvarchar`(max)  NULL,
	`rep_isappro` `char`(1)  NULL,
	`rep_date` `datetime` NULL,
	`rep_type` `bigint` NULL,
	`rep_isdel` `char`(1)  NULL,
	`rep_issend` `char`(1)  NULL,
	`rep_send_title` `nvarchar`(100)  NULL,
	`rep_ins_user` `nvarchar`(50)  NULL,
	`rep_rec_name` `nvarchar`(max)  NULL,
 CONSTRAINT `PK_report` PRIMARY KEY CLUSTERED 
(
	`rep_code` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `acc_trans`(
	`atr_id` `bigint` AUTO_INCREMENT NOT NULL,
	`atr_code` `varchar`(50)  NULL,
	`atr_date` `datetime` NULL,
	`atr_mon` `decimal`(18, 2) NULL,
	`atr_type_id` `bigint` NULL,
	`atr_in_aco` `bigint` NULL,
	`atr_out_aco` `bigint` NULL,
	`atr_remark` `nvarchar`(max)  NULL,
	`atr_isdel` `char`(1)  NULL,
	`atr_inp_user` `nvarchar`(50)  NULL,
	`atr_cre_date` `datetime` NULL,
	`atr_undo_user` `nvarchar`(50)  NULL,
	`atr_undo_date` `datetime` NULL,
	`atr_content` `nvarchar`(100)  NULL,
 CONSTRAINT `PK_acc_trans` PRIMARY KEY CLUSTERED 
(
	`atr_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `acc_line`(
	`acl_id` `bigint` AUTO_INCREMENT NOT NULL,
	`acl_aco_id` `bigint` NULL,
	`acl_type` `nvarchar`(100)  NULL,
	`acl_note_id` `varchar`(300)  NULL,
	`acl_mon` `decimal`(18, 2) NULL,
	`acl_cur_mon` `decimal`(18, 2) NULL,
	`acl_cre_date` `datetime` NULL,
	`acl_isInv` `char`(1)  NULL,
	`acl_content` `nvarchar`(100)  NULL,
	`acl_user` `nvarchar`(50)  NULL,
	`acl_other` `nvarchar`(100)  NULL,
 CONSTRAINT `PK_acc_line` PRIMARY KEY CLUSTERED 
(
	`acl_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `vis_record`(
	`vr_id` `bigint` AUTO_INCREMENT NOT NULL,
	`vr_state` `int` NULL,
	`vr_adr_id` `bigint` NULL,
	`vr_cas_id` `bigint` NULL,
	`vr_num` `int` NULL,
	`vr_typ_id1` `bigint` NULL,
	`vr_typ_id2` `bigint` NULL,
	`vr_typ_id3` `bigint` NULL,
	`vr_typ_id` `bigint` NULL,
	`vr_name` `nvarchar`(50)  NULL,
	`vr_sex` `nvarchar`(1)  NULL,
	`vr_age` `int` NULL,
	`vr_req` `nvarchar`(max)  NULL,
	`vr_remark` `nvarchar`(max)  NULL,
	`vr_report` `nvarchar`(max)  NULL,
	`vr_est_date` `datetime` NULL,
	`vr_rel_date` `datetime` NULL,
	`vr_app_user` `nvarchar`(25)  NULL,
	`vr_app_time` `datetime` NULL,
	`vr_bk_time` `datetime` NULL,
	`vr_rec_user` `nvarchar`(max)  NULL,
	`vr_adr` `nvarchar`(200)  NULL,
	`vr_rs` `varchar`(50) NULL,
	`vr_is_prt` `char`(1) NULL,
 CONSTRAINT `PK_vis_record` PRIMARY KEY CLUSTERED 
(
	`vr_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `sal_task`(
	`st_id` `bigint` AUTO_INCREMENT NOT NULL,
	`st_title` `nvarchar`(200)  NULL,
	`st_se_no` `bigint` NULL,
	`st_name` `nvarchar`(50)  NULL,
	`st_rel_date` `datetime` NULL,
	`st_fin_date` `datetime` NULL,
	`st_lev` `varchar`(50)  NULL,
	`st_cyc` `nvarchar`(50)  NULL,
	`st_type_id` `bigint` NULL,
	`st_stu` `char`(1)  NULL,
	`st_mon` `decimal`(18, 2) NULL,
	`st_tag` `nvarchar`(max)  NULL,
	`st_remark` `nvarchar`(max)  NULL,
	`st_change_date` `datetime` NULL,
	`st_log` `nvarchar`(max)  NULL,
	`st_isdel` `char`(1)  NULL,
	`st_fct_date` `datetime` NULL,
	`st_upd_user` `nvarchar`(50)  NULL,
	`st_start_date` `datetime` NULL,
 CONSTRAINT `PK_sal_task` PRIMARY KEY CLUSTERED 
(
	`st_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `vis_rec_ass`(
	`vra_id` `bigint` AUTO_INCREMENT NOT NULL,
	`vra_vr_id` `bigint` NULL,
	`vra_user_code` `varchar`(50)  NULL,
 CONSTRAINT `PK_vis_rec_ass` PRIMARY KEY CLUSTERED 
(
	`vra_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `acc_lock`(
	`table_name` `varchar`(50)  NOT NULL,
	`table_max` `bigint` NULL,
 CONSTRAINT `PK_acc_lock` PRIMARY KEY CLUSTERED 
(
	`table_name` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `comment`(
	`cot_id` `bigint` AUTO_INCREMENT NOT NULL,
	`cot_content` `nvarchar`(max)  NULL,
	`cot_cas_id` `bigint` NULL,
	`cot_user` `nvarchar`(25)  NULL,
	`cot_time` `datetime` NULL,
	`cot_state` `int` NULL,
 CONSTRAINT `PK_comment` PRIMARY KEY CLUSTERED 
(
	`cot_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `ta_lim`(
	`ta_lim_id` `bigint` AUTO_INCREMENT NOT NULL,
	`ta_se_no` `bigint` NULL,
	`ta_isdel` `char`(1)  NULL,
	`ta_task_id` `bigint` NULL,
	`ta_fin_date` `datetime` NULL,
	`ta_isfin` `char`(1)  NULL,
	`ta_desc` `nvarchar`(max)  NULL,
	`ta_name` `nvarchar`(50)  NULL,
 CONSTRAINT `PK_ta_lim` PRIMARY KEY CLUSTERED 
(
	`ta_lim_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `phone_list`(
	`phl_id` `bigint` AUTO_INCREMENT NOT NULL,
	`phl_state` `int` NULL,
	`phl_name` `nvarchar`(50)  NULL,
	`phl_num` `varchar`(50)  NULL,
	`phl_cas_id` `bigint` NULL,
	`phl_cat` `nvarchar`(50)  NULL,
	`phl_count` `int` NULL,
	`phl_remark` `nvarchar`(max)  NULL,
	`phl_isdel` `char`(1)  NULL,
	`phl_isnew` `int` NULL,
	`phl_upd_time` `datetime` NULL,
	`phl_rel` `nvarchar`(50) NULL,
 CONSTRAINT `PK_phone_list` PRIMARY KEY CLUSTERED 
(
	`phl_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `r_inq_pro`(
	`rqp_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rqp_inq_id` `bigint` NULL,
	`rqp_wpr_id` `bigint` NULL,
	`rqp_num` `decimal`(18, 2) NULL,
	`rqp_price` `decimal`(18, 2) NULL,
	`rqp_all_price` `decimal`(18, 2) NULL,
	`rqp_remark` `nvarchar`(max)  NULL,
 CONSTRAINT `PK_r_inq_pro` PRIMARY KEY CLUSTERED 
(
	`rqp_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `case_hp`(
	`ch_id` `bigint` AUTO_INCREMENT NOT NULL,
	`ch_chk_state` `int` NULL,
	`ch_typ` `int` NULL,
	`ch_text` `nvarchar`(max)  NULL,
	`ch_cat_1` `nvarchar`(20)  NULL,
	`ch_cat_2` `nvarchar`(20)  NULL,
	`ch_adr_id` `bigint` NULL,
	`ch_msg_state` `int` NULL,
	`ch_cas_id` `bigint` NULL,
	`ch_res` `nvarchar`(max)  NULL,
	`ch_app_user` `nvarchar`(20)  NULL,
	`ch_sur_user` `nvarchar`(20)  NULL,
	`ch_app_time` `datetime` NULL,
	`ch_sur_time` `datetime` NULL,
	`ch_remark` `nvarchar`(max)  NULL,
	`ch_cont_user` `nvarchar`(20)  NULL,
	`ch_adr` `nvarchar`(1000)  NULL,
	`ch_count` `int` NULL,
	`ch_upd_time` `datetime` NULL,
	`ch_upd_man` `nvarchar`(25) NULL,
 CONSTRAINT `PK_case_hp` PRIMARY KEY CLUSTERED 
(
	`ch_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `lim_role`(
	`rol_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rol_name` `nvarchar`(50)  NULL,
	`rol_lev` `int` NULL,
	`rol_desc` `varchar`(max)  NULL,
	`rol_grp_id` `bigint`,
 CONSTRAINT `PK_lim_role` PRIMARY KEY CLUSTERED 
(
	`rol_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `r_quo_pro`(
	`rup_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rup_quo_id` `bigint` NULL,
	`rup_wpr_id` `bigint` NULL,
	`rup_num` `decimal`(18, 2) NULL,
	`rup_price` `decimal`(18, 2) NULL,
	`rup_all_price` `decimal`(18, 2) NULL,
	`rup_remark` `nvarchar`(max)  NULL,
 CONSTRAINT `PK_r_quo_pro` PRIMARY KEY CLUSTERED 
(
	`rup_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `sal_paid_past`(
	`sps_id` `bigint` AUTO_INCREMENT NOT NULL,
	`sps_ord_code` `bigint` NULL,
	`sps_fct_date` `datetime` NULL,
	`sps_count` `int` NULL,
	`sps_type_id` `bigint` NULL,
	`sps_pay_type` `nvarchar`(50)  NULL,
	`sps_pay_mon` `decimal`(18, 2) NULL,
	`sps_mon_type` `nvarchar`(50)  NULL,
	`sps_user_code` `nvarchar`(50)  NULL,
	`sps_se_no` `bigint` NULL,
	`sps_isinv` `char`(1)  NULL,
	`sps_remark` `nvarchar`(max)  NULL,
	`sps_alt_user` `nvarchar`(50)  NULL,
	`sps_cre_date` `datetime` NULL,
	`sps_alt_date` `datetime` NULL,
	`sps_isdel` `char`(1)  NULL,
	`sps_code` `varchar`(300)  NULL,
	`sps_aco_id` `bigint` NULL,
	`sps_out_name` `nvarchar`(100)  NULL,
	`sps_content` `nvarchar`(100)  NULL,
	`sps_acc_type_id` `bigint` NULL,
	`sps_undo_date` `datetime` NULL,
	`sps_undo_user` `nvarchar`(50)  NULL,
	`sps_cus_id` `bigint` NULL,
 CONSTRAINT `PK_sal_paid_past` PRIMARY KEY CLUSTERED 
(
	`sps_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `case_paid`(
	`pa_id` `bigint` AUTO_INCREMENT NOT NULL,
	`pa_state` `int` NULL,
	`pa_cas_id` `bigint` NULL,
	`pa_ptp_d` `datetime` NULL,
	`pa_ptp_num` `decimal`(18, 2) NULL,
	`pa_cp_time` `datetime` NULL,
	`pa_cp_num` `decimal`(18, 2) NULL,
	`pa_comt_user` `nvarchar`(25)  NULL,
	`pa_comt_time` `datetime` NULL,
	`pa_paid_time` `datetime` NULL,
	`pa_paid_num` `decimal`(18, 2) NULL,
	`pa_sur_user` `nvarchar`(25)  NULL,
	`pa_sur_time` `datetime` NULL,
	`pa_sur_remark` `nvarchar`(max)  NULL,
	`pa_writer` `nvarchar`(25)  NULL,
	`pa_wri_time` `datetime` NULL,
	`pa_alt_user` `nvarchar`(25)  NULL,
	`pa_alt_time` `datetime` NULL,
	`pa_del_user` `nvarchar`(25)  NULL,
	`pa_del_time` `datetime` NULL,
	`pa_m_paid` `decimal`(18, 2) NULL,
	`pa_cpm_paid` `decimal`(18, 2) NULL,
	`pa_se_no` `bigint` NULL,
	`pa_cm_paid` `decimal`(18, 2) NULL,
	`pa_back_paid` `decimal`(18, 2) NULL,
	`pa_pback_paid` `decimal`(18, 2) NULL,
	`pa_last_debt_m` `decimal`(18,2) NULL,
	`pa_left_amt` `decimal`(18,2) NULL,
 CONSTRAINT `PK_paid` PRIMARY KEY CLUSTERED 
(
	`pa_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `sal_pur_ord`(
	`spo_id` `bigint` AUTO_INCREMENT NOT NULL,
	`spo_til` `nvarchar`(300)  NULL,
	`spo_code` `varchar`(300)  NULL,
	`spo_con_date` `datetime` NULL,
	`spo_sup_id` `bigint` NULL,
	`spo_type_id` `bigint` NULL,
	`spo_proj_id` `bigint` NULL,
	`spo_sum_mon` `decimal`(18, 2) NULL,
	`spo_paid_mon` `decimal`(18, 2) NULL,
	`spo_user_code` `varchar`(50)  NULL,
	`spo_content` `nvarchar`(max)  NULL,
	`spo_isend` `char`(1)  NULL,
	`spo_isdel` `char`(1)  NULL,
	`spo_remark` `nvarchar`(max)  NULL,
	`spo_inp_user` `nvarchar`(50)  NULL,
	`spo_cre_date` `datetime` NULL,
	`spo_alt_date` `datetime` NULL,
	`spo_alt_user` `nvarchar`(50)  NULL,
	`spo_app_date` `datetime` NULL,
	`spo_app_man` `nvarchar`(50)  NULL,
	`spo_app_desc` `nvarchar`(max)  NULL,
	`spo_app_isok` `char`(1)  NULL,
	`spo_se_no` `bigint` NULL,
 CONSTRAINT `PK_sal_pur_ord` PRIMARY KEY CLUSTERED 
(
	`spo_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `hurr_rec`(
	`hur_id` `bigint` AUTO_INCREMENT NOT NULL,
	`hur_cat` `varchar`(50)  NULL,
	`hur_cas_id` `bigint` NULL,
	`hur_oper` `nvarchar`(25)  NULL,
	`hur_op_time` `datetime` NULL,
	`hur_op_cont` `nvarchar`(max)  NULL,
	`hur_re_id` `bigint` NULL,
 CONSTRAINT `PK_hurr_rec` PRIMARY KEY CLUSTERED 
(
	`hur_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `wms_check`(
	`wmc_id` `bigint` AUTO_INCREMENT NOT NULL,
	`wmc_code` `varchar`(50)  NULL,
	`wmc_title` `nvarchar`(max)  NULL,
	`wmc_stro_code` `varchar`(50)  NULL,
	`wmc_user_code` `varchar`(50)  NULL,
	`wmc_date` `datetime` NULL,
	`wmc_state` `char`(1)  NULL,
	`wmc_remark` `nvarchar`(max)  NULL,
	`wmc_isdel` `char`(1)  NULL,
	`wmc_inp_name` `nvarchar`(50)  NULL,
	`wmc_alt_name` `nvarchar`(50)  NULL,
	`wmc_inp_date` `datetime` NULL,
	`wmc_alt_date` `datetime` NULL,
	`wmc_app_date` `datetime` NULL,
	`wmc_app_man` `nvarchar`(50)  NULL,
	`wmc_app_isok` `char`(1)  NULL,
	`wmc_app_desc` `nvarchar`(max)  NULL,
	`wmc_can_date` `datetime` NULL,
	`wmc_can_man` `nvarchar`(50)  NULL,
 CONSTRAINT `PK_wms_check` PRIMARY KEY CLUSTERED 
(
	`wmc_id` ASC
)WITH (IGNORE_DUP_KEY = OFF)
)
END

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `address` ADD  CONSTRAINT `DF_address_adr_num`  DEFAULT ((0)) FOR `adr_num`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `address` ADD  CONSTRAINT `DF_address_adr_check_app`  DEFAULT ((0)) FOR `adr_check_app`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `address` ADD  CONSTRAINT `DF_address_adr_mail_app`  DEFAULT ((0)) FOR `adr_mail_app`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `address` ADD  CONSTRAINT `DF_address_adr_vis_app`  DEFAULT ((0)) FOR `adr_vis_app`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `bank_case` ADD  CONSTRAINT `DF_bank_case_cas_m`  DEFAULT ((0)) FOR `cas_m`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `bank_case` ADD  CONSTRAINT `DF_bank_case_cas_ptp_m`  DEFAULT ((0)) FOR `cas_ptp_m`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `bank_case` ADD  CONSTRAINT `DF_bank_case_cas_cp_m`  DEFAULT ((0)) FOR `cas_cp_m`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `bank_case` ADD  CONSTRAINT `DF_bank_case_cas_paid_m`  DEFAULT ((0)) FOR `cas_paid_m`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `bank_case` ADD  CONSTRAINT `DF_bank_case_cas_gua_m`  DEFAULT ((0)) FOR `cas_gua_m`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `case_bat` ADD  CONSTRAINT `DF_case_bat_cbat_mon`  DEFAULT ((0)) FOR `cbat_mon`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `case_paid` ADD  CONSTRAINT `DF_case_paid_pa_ptp_num`  DEFAULT ((0)) FOR `pa_ptp_num`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `case_paid` ADD  CONSTRAINT `DF_case_paid_pa_cp_num`  DEFAULT ((0)) FOR `pa_cp_num`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `case_paid` ADD  CONSTRAINT `DF_case_paid_pa_paid_num`  DEFAULT ((0)) FOR `pa_paid_num`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `case_paid` ADD  CONSTRAINT `DF_case_paid_pa_m_paid`  DEFAULT ((0)) FOR `pa_m_paid`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `case_paid` ADD  CONSTRAINT `DF_case_paid_pa_cpm_paid`  DEFAULT ((0)) FOR `pa_cpm_paid`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `case_paid` ADD  CONSTRAINT `DF_case_paid_pa_cm_paid`  DEFAULT ((0)) FOR `pa_cm_paid`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `cus_cor_cus` ADD  CONSTRAINT `DF_cus_cor_cus_cor_isdelete`  DEFAULT ((1)) FOR `cor_isdelete`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `lim_user` ADD  CONSTRAINT `DF_lim_user_user_fail`  DEFAULT ((0)) FOR `user_fail`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `r_ord_pro` ADD  CONSTRAINT `DF_r_ord_pro_rop_real_price`  DEFAULT ((0.00)) FOR `rop_real_price`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `sal_all_task` ADD  CONSTRAINT `DF_sal_all_task_sat_mon`  DEFAULT ((0.00)) FOR `sat_paid_mon`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `sal_invoice` ADD  CONSTRAINT `DF_sal_invoice_sin_mon`  DEFAULT ((0.00)) FOR `sin_mon`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `sal_ord_con` ADD  CONSTRAINT `DF_sal_order_sod_sum_mon`  DEFAULT ((0.00)) FOR `sod_sum_mon`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `sal_ord_con` ADD  CONSTRAINT `DF_sal_ord_con_sod_paid_mon`  DEFAULT ((0.0)) FOR `sod_paid_mon`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `sal_ord_con` ADD  CONSTRAINT `DF_sal_order_sod_isfail`  DEFAULT ((0)) FOR `sod_isfail`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `sal_paid_plan` ADD  CONSTRAINT `DF_sal_paid_spd_pay_mon`  DEFAULT ((0.00)) FOR `spd_pay_mon`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `sal_task` ADD  CONSTRAINT `DF_sal_task_st_mon`  DEFAULT ((0.00)) FOR `st_mon`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `wms_product` ADD  CONSTRAINT `DF_wms_product_wpr_cost_prc`  DEFAULT ((0.00)) FOR `wpr_cost_prc`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `wms_product` ADD  CONSTRAINT `DF_wms_product_wpr_sale_prc`  DEFAULT ((0.00)) FOR `wpr_sale_prc`

End

IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id =  AND parent_object_id = )
Begin
ALTER TABLE `wms_shipment` ADD  CONSTRAINT `DF_wms_shipment_wsh_cost`  DEFAULT ((0.00)) FOR `wsh_cost`

CREATE TABLE `loan_season`(
	`lse_id` `bigint` AUTO_INCREMENT NOT NULL,
	`lse_name` `nvarchar`(50)  NULL,
 CONSTRAINT `PK_loan_season` PRIMARY KEY CLUSTERED 
(
	`lse_id` ASC
)WITH (IGNORE_DUP_KEY = OFF) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING ON
CREATE TABLE `loan_cus`(
	`lc_id` `bigint` AUTO_INCREMENT NOT NULL,
	`lc_name` `nvarchar`(50)  NULL,
	`lc_card_num` `varchar`(50)  NULL,
	`lc_card_type` `nvarchar`(20)  NULL,
	`lc_bank` `nvarchar`(100)  NULL,
	`lc_due_m` `decimal`(18, 2) NULL CONSTRAINT `DF__bank_cus__bc_m__084C046C`  DEFAULT ((0.00)),
	`lc_principal` `decimal`(18, 2) NULL CONSTRAINT `DF__bank_cus__bc_pri__094028A5`  DEFAULT ((0.00)),
	`lc_time_lim` `nvarchar`(50)  NULL,
	`lc_quality` `nvarchar`(20)  NULL,
	`lc_due_num` `varchar`(50)  NULL,
	`lc_overdue` `varchar`(50)  NULL,
	`lc_due_date` `datetime` NULL,
	`lc_end_date` `datetime` NULL,
	`lc_company` `nvarchar`(100)  NULL,
	`lc_com_addr` `nvarchar`(300)  NULL,
	`lc_com_pho` `varchar`(50)  NULL,
	`lc_home_addr` `nvarchar`(300)  NULL,
	`lc_home_pho` `varchar`(50)  NULL,
	`lc_manager` `nvarchar`(50)  NULL,
	`lc_num` `varchar`(50)  NULL,
	`lc_lse_id` `bigint` NULL,
	`lc_remark` `nvarchar`(200)  NULL,
	`lc_ins_user` `nvarchar`(25)  NULL,
	`lc_ins_time` `datetime` NULL,
	`lc_alt_user` `nvarchar`(25)  NULL,
	`lc_alt_time` `datetime` NULL,
	`lc_risk_adv` `nvarchar`(500)  NULL,
	`lc_risk_per` `nvarchar`(500)  NULL,
	`lc_risk_com` `nvarchar`(500)  NULL,
	`lc_risk_biz` `nvarchar`(500)  NULL,
	`lc_risk_que` `varchar`(50)  NULL,
	`lc_op_state` `nvarchar`(200)  NULL,
	`lc_risk_rs` `nvarchar`(500)  NULL,
 CONSTRAINT `PK_bank_cus` PRIMARY KEY CLUSTERED 
(
	`lc_id` ASC
)WITH (IGNORE_DUP_KEY = OFF) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF
ALTER TABLE `loan_cus`  WITH CHECK ADD  CONSTRAINT `FK_loan_cus_loan_season` FOREIGN KEY(`lc_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE

CREATE TABLE `loan_com`(
	`lcm_id` `bigint` AUTO_INCREMENT NOT NULL,
	`lcm_lse_id` `bigint` NULL,
	`lcm_company` `nvarchar`(100)  NULL,
	`lcm_content` `nvarchar`(max)  NULL,
	`lcm_ins_user` `nvarchar`(25)  NULL,
	`lcm_ins_time` `datetime` NULL,
	`lcm_alt_user` `nvarchar`(25)  NULL,
	`lcm_alt_time` `datetime` NULL,
 CONSTRAINT `PK_loan_com` PRIMARY KEY CLUSTERED 
(
	`lcm_id` ASC
)WITH (IGNORE_DUP_KEY = OFF) ON `PRIMARY`
) ON `PRIMARY`

ALTER TABLE `loan_com`  WITH CHECK ADD  CONSTRAINT `FK_loan_com_loan_season` FOREIGN KEY(`lcm_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE

SET ANSI_PADDING ON
CREATE TABLE `loan_per`(
	`lp_id` `bigint` AUTO_INCREMENT NOT NULL,
	`lp_lse_id` `bigint` NULL,
	`lp_card_num` `varchar`(50)  NULL,
	`lp_content` `nvarchar`(max)  NULL,
	`lp_ins_user` `nvarchar`(25)  NULL,
	`lp_ins_time` `datetime` NULL,
	`lp_alt_user` `nvarchar`(25)  NULL,
	`lp_alt_time` `datetime` NULL,
	`lp_name` `nvarchar`(50)  NULL,
 CONSTRAINT `PK_loan_per` PRIMARY KEY CLUSTERED 
(
	`lp_id` ASC
)WITH (IGNORE_DUP_KEY = OFF) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF
ALTER TABLE `loan_per`  WITH CHECK ADD  CONSTRAINT `FK_loan_per_loan_season` FOREIGN KEY(`lp_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE

SET ANSI_PADDING ON
CREATE TABLE `loan_police`(
	`lpol_id` `bigint` AUTO_INCREMENT NOT NULL,
	`lpol_lse_id` `bigint` NULL,
	`lpol_name` `nvarchar`(50)  NULL,
	`lpol_card_num` `varchar`(50)  NULL,
	`lpol_content` `nvarchar`(max)  NULL,
	`lpol_ins_user` `nvarchar`(25)  NULL,
	`lpol_ins_time` `datetime` NULL,
	`lpol_alt_user` `nvarchar`(25)  NULL,
	`lpol_alt_time` `datetime` NULL,
	`lpol_card_type` `nvarchar`(20)  NULL,
 CONSTRAINT `PK_loan_police` PRIMARY KEY CLUSTERED 
(
	`lpol_id` ASC
)WITH (IGNORE_DUP_KEY = OFF) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF
ALTER TABLE `loan_police`  WITH CHECK ADD  CONSTRAINT `FK_loan_police_loan_season` FOREIGN KEY(`lpol_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE

SET ANSI_PADDING ON
CREATE TABLE `loan_reg_inf`(
	`lreg_id` `bigint` AUTO_INCREMENT NOT NULL,
	`lreg_company` `nvarchar`(100)  NULL,
	`lreg_content` `nvarchar`(max)  NULL,
	`lreg_ins_user` `nvarchar`(25)  NULL,
	`lreg_ins_time` `datetime` NULL,
	`lreg_alt_user` `nvarchar`(25)  NULL,
	`lreg_alt_time` `datetime` NULL,
	`lreg_lse_id` `bigint` NULL,
	`lreg_state` `nvarchar`(50)  NULL,
	`lreg_last_year` `varchar`(50)  NULL,
	`lreg_boss_name` `nvarchar`(500)  NULL,
	`lreg_ord_code` `varchar`(50)  NULL,
	`lreg_law_man` `nvarchar`(50)  NULL,
 CONSTRAINT `PK_loan_reg_inf` PRIMARY KEY CLUSTERED 
(
	`lreg_id` ASC
)WITH (IGNORE_DUP_KEY = OFF) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF
ALTER TABLE `loan_reg_inf`  WITH CHECK ADD  CONSTRAINT `FK_loan_reg_inf_loan_season` FOREIGN KEY(`lreg_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE

SET ANSI_PADDING ON
CREATE TABLE `loan_sd_rec`(
	`lsd_id` `bigint` AUTO_INCREMENT NOT NULL,
	`lsd_lse_id` `bigint` NULL,
	`lsd_name` `nvarchar`(50)  NULL,
	`lsd_card_num` `varchar`(50)  NULL,
	`lsd_sear_num` `nvarchar`(50)  NULL,
	`lsd_date` `nvarchar`(50)  NULL,
	`lsd_m` `nvarchar`(50)  NULL,
	`lsd_ins_user` `nvarchar`(25)  NULL,
	`lsd_ins_time` `datetime` NULL,
	`lsd_alt_user` `nvarchar`(25)  NULL,
	`lsd_alt_time` `datetime` NULL,
 CONSTRAINT `PK_loan_sd_rec` PRIMARY KEY CLUSTERED 
(
	`lsd_id` ASC
)WITH (IGNORE_DUP_KEY = OFF) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF
ALTER TABLE `loan_sd_rec`  WITH CHECK ADD  CONSTRAINT `FK_loan_sd_rec_loan_season` FOREIGN KEY(`lsd_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE

SET ANSI_PADDING ON
CREATE TABLE `house_inf`(
	`hoi_id` `bigint` AUTO_INCREMENT NOT NULL,
	`hoi_lse_id` `bigint` NULL,
	`hoi_name` `nvarchar`(50)  NULL,
	`hoi_id_no` `varchar`(50)  NULL,
	`hoi_house_no` `nvarchar`(500)  NULL,
	`hoi_type` `nvarchar`(100)  NULL,
	`hoi_com` `nvarchar`(200)  NULL,
	`hoi_ins_user` `nvarchar`(50)  NULL,
	`hoi_ins_time` `datetime` NULL,
	`hoi_alt_user` `nvarchar`(50)  NULL,
	`hoi_alt_time` `datetime` NULL,
 CONSTRAINT `PR_house_inf` PRIMARY KEY CLUSTERED 
(
	`hoi_id` ASC
)WITH (IGNORE_DUP_KEY = OFF) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF

CREATE TABLE `neg_inf`(
	`nei_id` `bigint` AUTO_INCREMENT NOT NULL,
	`nei_lse_id` `bigint` NULL,
	`nei_name` `nvarchar`(100)  NULL,
	`nei_inf` `nvarchar`(500)  NULL,
	`nei_ins_user` `nvarchar`(50)  NULL,
	`nei_ins_time` `datetime` NULL,
	`nei_alt_user` `nvarchar`(50)  NULL,
	`nei_alt_time` `datetime` NULL,
 CONSTRAINT `PK_neg_inf` PRIMARY KEY CLUSTERED 
(
	`nei_id` ASC
)WITH (IGNORE_DUP_KEY = OFF) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING ON
CREATE TABLE `car_loan`(
	`cal_cas_id` `bigint` NULL,
	`cal_price` `varchar`(200) NULL,
	`cal_lice` `varchar`(200) NULL,
	`cal_make` `nvarchar`(200) NULL,
	`cal_vin` `varchar`(200) NULL,
	`cal_engine_no` `varchar`(200) NULL,
) ON `PRIMARY`

SET ANSI_PADDING OFF

create table sys_pref(
syp_id bigint primary key AUTO_INCREMENT NOT NULL,
syp_name	nvarchar(50),
syp_is_def	int,
syp_is_app	int,
syp_pwd_len	int,
syp_pwd_rule	varchar(50),
syp_pwd_upd_days	int,
syp_login_fail	int,
syp_offline_days	int,
syp_has_captcha int,
syp_hide_back	int,
syp_global_org	int,
syp_cl_no_hide int
)

 ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE `law_act`(
	`lwa_id` `bigint` AUTO_INCREMENT NOT NULL,
	`lwa_lwc_id` `bigint` NULL,
	`lwa_emp_id` `bigint` NULL,
	`lwa_content` `nvarchar`(max) NULL,
	`lwa_time` `datetime` NULL,
	`lwa_cre_man` `nvarchar`(50) NULL,
	`lwa_cre_time` `datetime` NULL,
	`lwa_upd_man` `nvarchar`(50) NULL,
	`lwa_upd_time` `datetime` NULL,
	`lwa_remark` `nvarchar`(max) NULL,
	`lwa_proc` `varchar`(50) NULL,
 CONSTRAINT `PK_law_act` PRIMARY KEY CLUSTERED 
(
	`lwa_id` ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF
CREATE TABLE `law_case`(
	`lwc_id` `bigint` AUTO_INCREMENT NOT NULL,
	`lwc_state` `varchar`(50) NULL,
	`lwc_name` `nvarchar`(100) NULL,
	`lwc_id_code` `varchar`(50) NULL,
	`lwc_consigner` `nvarchar`(100) NULL,
	`lwc_defendant` `nvarchar`(100) NULL,
	`lwc_cost` `decimal`(18, 2) NULL,
	`lwc_target` `nvarchar`(50) NULL,
	`lwc_emp_id` `bigint` NULL,
	`lwc_type_id` `bigint` NULL,
	`lwc_date` `datetime` NULL,
	`lwc_contact` `varchar`(200) NULL,
	`lwc_court_id` `bigint` NULL,
	`lwc_filing_date` `datetime` NULL,
	`lwc_no` `varchar`(100) NULL,
	`lwc_judge` `nvarchar`(50) NULL,
	`lwc_judge_contact` `varchar`(200) NULL,
	`lwc_fst_hearing_date` `datetime` NULL,
	`lwc_end_date` `datetime` NULL,
	`lwc_act_date` `datetime` NULL,
	`lwc_act_no` `varchar`(100) NULL,
	`lwc_act_end_date` `datetime` NULL,
	`lwc_remark` `nvarchar`(500) NULL,
	`lwc_cre_man` `nvarchar`(50) NULL,
	`lwc_cre_time` `datetime` NULL,
	`lwc_upd_man` `nvarchar`(50) NULL,
	`lwc_upd_time` `datetime` NULL,
	`lwc_paid` `decimal`(18, 2) NULL,
	`lwc_proc_st_id`	bigint NULL,
	`lwc_cas_id` bigint NULL,
	`lwc_legal_pay_date` `datetime` NULL,
	`lwc_presv_pay_date` `datetime` NULL,
	`lwc_asset_presv` `nvarchar`(max) NULL,
	`lwc_serv_rs` `nvarchar`(100) NULL,
	`lwc_judgment` `nvarchar`(max) NULL,
	`lwc_proc` `varchar`(50) NULL,
	`lwc_approve_time` `datetime` NULL,
	`lwc_approve_man` `nvarchar`(25) NULL,
 CONSTRAINT `PK_law_case` PRIMARY KEY CLUSTERED 
(
	`lwc_id` ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF

SET ANSI_PADDING ON
CREATE TABLE `law_paid`(
	`lwpa_id` `bigint` AUTO_INCREMENT NOT NULL,
	`lwpa_amt` `decimal`(18, 2) NULL,
	`lwpa_type_id` `bigint` NULL,
	`lwpa_date` `datetime` NULL,
	`lwpa_name` `nvarchar`(100) NULL,
	`lwpa_man` `nvarchar`(50) NULL,
	`lwpa_pay_med` `int` NULL,
	`lwpa_file_code` `varchar`(100) NULL,
	`lwpa_remark` `nvarchar`(500) NULL,
	`lwpa_cre_man` `nvarchar`(50) NULL,
	`lwpa_cre_time` `datetime` NULL,
	`lwpa_upd_man` `nvarchar`(50) NULL,
	`lwpa_upd_time` `datetime` NULL,
	`lwpa_lwc_id` `bigint` NULL,
 CONSTRAINT `PK_law_paid` PRIMARY KEY CLUSTERED 
(
	`lwpa_id` ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF

CREATE TABLE `lim_group`(
	`grp_id` `bigint` AUTO_INCREMENT NOT NULL,
	`grp_name` `nvarchar`(50) NULL,
	`grp_desc` `nvarchar`(200) NULL,
	`grp_cre_time` `datetime` NULL,
	`grp_cre_man` `nvarchar`(50) NULL,
	`grp_upd_time` `datetime` NULL,
	`grp_upd_man` `nvarchar`(50) NULL,
 CONSTRAINT `PK_lim_group` PRIMARY KEY CLUSTERED 
(
	`grp_id` ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON `PRIMARY`
) ON `PRIMARY`

CREATE TABLE `r_group_rig`(
	`rgr_id` `bigint` AUTO_INCREMENT NOT NULL,
	`rgr_grp_id` `bigint` NULL,
	`rgr_rig_code` `varchar`(50) NULL,
 CONSTRAINT `PK_r_group_rig` PRIMARY KEY CLUSTERED 
(
	`rgr_id` ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON `PRIMARY`
) ON `PRIMARY`


SET ANSI_PADDING ON
CREATE TABLE `cashbus_batch`(
	`id` `bigint` NOT NULL,
	`batchname` `varchar`(255) NOT NULL,
	`description` `text` NULL,
	`withdraw` `bit` NULL,
	`type` `varchar`(255) NULL,
	`withdrawTime` `datetime` NULL,
	`ext1` `varchar`(100) NULL,
	`ext2` `varchar`(100) NULL,
	`ext3` `varchar`(100) NULL,
 CONSTRAINT `PK_CashBusBatch_1` PRIMARY KEY CLUSTERED
(
	`id` ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON `PRIMARY`
) ON `PRIMARY` TEXTIMAGE_ON `PRIMARY`

SET ANSI_PADDING OFF
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'batchname'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次描述' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'description'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否撤案' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'withdraw'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'类型 weiwai, yuzhengxin, zhengshizhengxin' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'type'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'撤案日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'withdrawTime'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'现金巴士批次' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch'


SET ANSI_PADDING ON
CREATE TABLE `user_log`(
	`ulg_id` `bigint` AUTO_INCREMENT NOT NULL,
	`ulg_type`	`varchar`(50),
	`ulg_oper`	`nvarchar`(50),
	`ulg_op_time`	`datetime`,
	`ulg_op_content`	`nvarchar`(max),
	`ulg_user`	`varchar`(50),
 CONSTRAINT `PK_user_log` PRIMARY KEY CLUSTERED 
(
	`ulg_id` ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON `PRIMARY`
) ON `PRIMARY`

SET ANSI_PADDING OFF

CREATE TABLE `type_list_connect`(
	`tlc_id` `bigint` AUTO_INCREMENT NOT NULL,
	`parent_typ_id` `bigint` NOT NULL,
	`child_typ_id` `bigint` NOT NULL,
 CONSTRAINT `PK_type_list_connect` PRIMARY KEY CLUSTERED 
(
	`tlc_id` ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON `PRIMARY`
) ON `PRIMARY`



SET FOREIGN_KEY_CHECKS = 1;


-- ========================================
-- 创建索引优化查询性能
-- ========================================

-- 核心表索引
CREATE INDEX idx_sal_emp_name ON `sal_emp` (`se_name`);
CREATE INDEX idx_cus_cor_cus_user ON `cus_cor_cus` (`cor_user_code`);
CREATE INDEX idx_lim_user_enabled ON `lim_user` (`user_isenabled`);
CREATE INDEX idx_bank_case_state ON `bank_case` (`cas_state`);
CREATE INDEX idx_pho_red_cas_id ON `pho_red` (`pr_cas_id`);
CREATE INDEX idx_case_paid_cas_id ON `case_paid` (`pa_cas_id`);

SET FOREIGN_KEY_CHECKS = 1;

-- 数据库创建完成
SELECT 'CCDS完整数据库创建完成 - 包含所有106个表' as message;
SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'ccds';
