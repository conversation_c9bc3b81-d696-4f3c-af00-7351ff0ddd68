function showRecycleDate(){
	$("recycleDateTxt").className="";
	$("recycleDate").disabled = false;
}
function hideRecycleDate(){
	$("recycleDateTxt").className="lightGray";
	$("recycleDate").disabled = true;
	$("recycleDate").value="";
}

function toShowIDCheckInfo(casId){
	window.open('idCheckService.do?op=toShowIDCheckInfo&casId='+casId);
}

function addBankName(obj){
	var bankIdsEL = $N("bankIds");
	var namesToAdd = [];
	for(var i=0;i<bankIdsEL.length;i++){
		if(bankIdsEL[i].checked){
			namesToAdd.push($("bankIdLA"+$(bankIdsEL[i]).value).innerHTML);
		}
	}
	$("bankNames").value = namesToAdd.join(",");
}


function getPrCat(){
	var catStr = ["电话催收","外访","信函","辅助渠道"];
	var catValue = ['0','1','2','3'];
	return [catStr,catValue];
}
function convPrCat(prCat){
	return getTypeStr(getPrCat(),prCat);
}
function loadPrCat(selectorId,dfValue){
	createSelector(selectorId,getPrCat()[0],getPrCat()[1],dfValue);
}

//转换催收类别
/*function convPrCat(prCat){
	switch (prCat) { case 0: 	prCat = "电话催收"; break; case 1:		prCat = "外访"; break; case 2: 	prCat = "信函"; break; case 3:		prCat = "辅助渠道"; break;
	}
	return prCat;
}*/
//转换案件操作类别
function convHurCat(hurCat){
	switch (hurCat) { 
		case 'oVis': 	hurCat = "外访"; break; 
		case 'apMsg':	hurCat = "信函"; break;
		case 'CP':		hurCat = "CP管理"; break; 
		case 'tR':		hurCat = "催收小结"; break; 
		case 'waN':		hurCat = "警告"; break; 
		case 'comt':	hurCat = "评语"; break; 
		case 'cPho':	hurCat = "电话管理";break; 
		case 'tPho':	hurCat = "测试电话"; break; 
		case 'cAdd':	hurCat = "地址管理"; break; 
		case 'PTP':		hurCat = "PTP管理"; break; 
		case 'loMe':	hurCat = "登账"; break; 
		case 'casM':	hurCat = "案件管理"; break; 

		//已不使用
		case 'adCk':
		case 'houCk':
		case 'tsPl':
		case 'comCk':
		case 'mbCk':
		case 'telCk':
		case 'cotHr':
		case 'plHr':
		case 'mHur':
		case 'disCon':
		case 'serCon':
		case 'shtAp':
		case 'bakAp':
			
		case 'cHp': 
			hurCat = "案件协催"; break; 
		
		case 'phoHur': 	hurCat = "电话催收"; break;	
		case 'visMag': 	hurCat = "外访管理"; break;		
		case 'visRep': 	hurCat = "外访报告"; break;	
		case 'color': 	hurCat = "标色"; break;	
	}
	return hurCat;
}

function showPRInputDiv(phlNum,phlName,phlType,phlRel){
	if(($("phoneRec").value!=""||$('ptpNum').value!=""||$('endTim').value!="")){
		alert('请先保存或取消当前电催记录！');
		return false;
	}else{
		if(phlRel==undefined){ phlRel = '本人'; }
		$('phoneNum').value = phlNum;
		$('phoneName').value = phlName;
		$('phoneType').value = phlType;
		$('prRel').value = phlRel;
		return true;
	}
}




function loadRedBlueState(selectorId){
	var rbStates = ["全部","未标色","标红","标蓝","标橙","标紫","标棕"];
	//var rbValues = ["","4","5"];
	var rbValues = ["","-1","1","2","3","4","5"];
	createSelector(selectorId,rbStates,rbValues);
}

function loadPaidState(selectorId){
	var paidStates = ["全部","未还款","部分还款","未全部还款","全部还款","已结清"];
	var stValues = ["","0","1","4","2","3"];
	createSelector(selectorId,paidStates,stValues);
}

/*function loadCaseExcLim(selectorId){
	var excLims = ["","一手单","二手单","三手单","四手单","m1","m2","m3","关注","次级","可疑","损失"];
	createSelector(selectorId, excLims);
}*/

function getPhoeTypes(){
	return ["","单位电话","家庭电话","手机","联系人电话","其他电话"];
}
function loadPhoneType(selectorId){
	var phoneTypes = getPhoeTypes();
	createSelector(selectorId,phoneTypes);
}

function loadAddType(selectorId){
	var addTypes = ["单位地址","家庭地址","对账单地址","户籍地址","其他地址"];
	createSelector(selectorId,addTypes);
}


function getIsHost(){
	var txts = ["主卡","副卡"];
	var values = ["主卡","副卡"];
	return [txts,values];
}
function getIsHostStr(isHostStr){
	return getTypeStr(getIsHost(),isHostStr);
}
function loadIsHost(tdId,name,dfValue){
	var showTxts = [];
	showTxts[0] = getIsHost()[0][0]+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
	showTxts[1] = getIsHost()[0][1]+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
	createRadio(tdId,name,showTxts,getIsHost()[1],dfValue);
}


/* 
	新建、编辑弹出层
*/
function casePopDiv(n,arg){
	var width;
	var height;
	var idPre="";
	var titleTxt="";
	var tips="（带<span class='red'>*</span>为必填字段）";
	var hasTitle = true;
	var hasScroll = false;
	var url;
	switch(n){
	 	case 1:
		   	height=258;
		   	titleTxt="更新催收小结";
		   	url="caseAction.do?op=toTremark&casId="+arg[0]+"&tremark="+encodeURIComponent(arg[1]);	
		   	break;
		case 11://未使用
		    height =325;
		    titleTxt="警告";
		    url="caseAction.do?op=toShowWarn&wanr="+arg; 
		    break;  
		case 12:
		    height =258;
		    titleTxt="添加警告";
		    url="caseAction.do?op=updateWarn&casId="+arg[0]+"&warn="+encodeURIComponent(arg[1]); 
		    break;
		case 13:
		    height =285;
		    titleTxt="添加评语";
		    url="caseAction.do?op=toComment&casId="+arg; 
		    break; 
		case 2:
		    height =2580;
		    titleTxt="修改案件"+tips;
		    url="caseAction.do?op=toUpdateCase&casId="+arg; 
		    break; 
		case 'batColor':
		    height =135;
		    titleTxt="案件标色"+tips;
		    url="caseAction.do?op=toBatchColor"; 
		    break; 
			
		case 3:
			height=165;
			width='half';
			titleTxt="修改案件地区";
			url="caseAction.do?op=alertCaseAddress&casId="+arg;
			break;	
		case 31:
			height=105;
			width='half';
			titleTxt="修改催收状态";
			url="caseAction.do?op=toModHurState&casId="+arg;
			break;
		case 37:
			height=105;
			width='half';
			titleTxt="批量修改催收状态";
			url="caseAction.do?op=toBatHurState";
			break;
		case 310:
		   height=250;
			titleTxt="添加辅助催记";
			url="caseAction.do?op=toSavePrOfOth&casId="+arg;
			break;
		case 311:
			height=105;
			width='half';
			titleTxt="修改预计退案日";
			url="caseAction.do?op=toModPlanBackDate&casId="+arg;
			break;
		case 312:
		   height=250;
			titleTxt="添加协催记录"+tips;
			url="assitanceAction.do?op=toSaveAssChecked&caseId="+arg;
			break;
		case 313:
		   	height=320;
			titleTxt="新建案人数据"+tips;
			url="extraInfAction.do?op=toSaveExtraInfOfCase&casId="+arg;
			break;
			
		case 4:		
			height=310;
			titleTxt="新建联系电话"+tips;
			url="phoneListAction.do?op=toNewPhone&casId="+arg;
			break;
		
		case 6:	
			height=505;
			width=800;
			titleTxt="历史电催记录";
			url="phoneListAction.do?op=toListPhoRe&casId="+arg[0]+"&phone="+encodeURIComponent(arg[1]);
			hasScroll = true;
			break;
		case 7:		
			height=290;
			titleTxt="新建联系地址"+tips;
			url="caseAction.do?op=toNewAddress&casId="+arg;
			break;
		
		case 8:		
			height=150;
			width='half';
			titleTxt="新增PTP记录"+tips;
			url="caseAction.do?op=toNewPTP&casId="+arg;
			break;
		case 9:
		 	height=505;
		 	width=800;
		 	titleTxt="测试汇总";	  
		 	url="caseAction.do?op=toListPhoneTest&casId="+arg;
		 	hasScroll = true;
		 	break;
		case 10:
			height=264;
			width='half';
			titleTxt="PTP转为CP"+tips;
			parent.$('casePtpC').value=0;
			url="caseAction.do?op=turnToCP&paId="+arg[0]+"&ptpNum="+arg[1]+"&ptpTime="+arg[2];
			break;
		case 14:
		    width=250;
			height=136;
		    titleTxt="作废PTP";
		    parent.$('casePtpC').value=0;
			url="caseAction.do?op=dropConfirm&paId="+arg+"&delType=1";
			isDel = true;
			break;	
		case 15:
		 	height=264;
			width='half';
		 	titleTxt="新建CP"+tips;
		 	url="caseAction.do?op=toNewCP&casId="+arg;	
		 	break;
		/*case 16:
		    height=280;
		    titleTxt="申请核准地址"+tips;
		    url="assitanceAction.do?op=toSaveAddressAss&addId="+arg;
			break;*/	
		case 17:
		    height=405;
		    titleTxt="建议外访"+tips;
		    url="visRecordAction.do?op=toAddVisRec&addId="+arg;
			break;
		case 19:
		    height=340;
		    titleTxt="建议信函"+tips;
		    url="assitanceAction.do?op=toSaveMailAss&addId="+arg;
		    break;	
		/*case 20:
			width=250;
			height=136;
			titleTxt="撤销信函";
			url="assitanceAction.do?op=cancleConfirm&casId="+arg[0]+"&delType=1"+"&adrId="+arg[1];
			break;*/
		case 21:
			titleTxt="手动分案"+tips;
			height=200;
			width='half';
			url="caseAction.do?op=toBatchC";
			break;
		case 211:
			titleTxt="批次自动分案";
			height=540;
			hasScroll=false;
			url="caseAction.do?op=toSetAutoAssignEmpOfBat&batId="+arg;
			break;
		case 212:
			titleTxt="查询结果分案"+tips;
			height=200;
			width='half';
			url="caseAction.do?op=toBatchC&isAll=1";
			break;
		case 213:
			titleTxt="查询结果自动分案";//案件管理
			height=540;
			hasScroll=false;
			url="caseAction.do?op=getSearchArgToAutoAssign";
			break;
		case 214:
			titleTxt="查询结果自动分案";//部门案件
			height=540;
			hasScroll=false;
			url="caseAction.do?op=getSearchFormOfDepartment";
			break;
		case 22:
			height=505;
			width=800;
			titleTxt="地址操作历史";
			url="caseAction.do?op=toListAddHis&adrId="+arg;
			hasScroll = true;
			break;
		case 23:
		    height =285;
		    titleTxt="添加评语"+tips;
		    url="caseAction.do?op=toBatchCom"; 
		    break;
		/*case 25:
			height=620;
			titleTxt="选择要导出的字段";
			url="caseAction.do?op=toOutMyCase&type="+arg;
			break;*/
		case 27:
			height=190;
			titleTxt="修改地址备注";
			url="caseAction.do?op=toAltAddRe&adrId="+arg[0]+"&remark="+encodeURIComponent(arg[1]);
			break;	
		/*case 28:
			height=250;
			titleTxt="导出银行催收记录";
			url="caseAction.do?op=toOutPRByBank";
			break;*/

		case 'BAT_CASE_OUT_CHECKED':
			height=1450;
			titleTxt="导出选中案件";
			url="caseAction.do?op=toBatchOut&type=1";
			break; 
		case 'BAT_CASE_OUT_ALL':
			height=1450;
			titleTxt="导出查询结果案件";
			url="caseAction.do?op=toBatchOut&type=0";
			break;
		case 'CASE_OUT_CHECKED': 
			height = 1450;
			titleTxt="导出选中案件";
			url = "caseAction.do?op=toBatchSea&type=1";
			break;
		case 'CASE_OUT_ALL':
			height = 1450;
			titleTxt="导出查询结果案件";
			url = "caseAction.do?op=toBatchSea&type=0";
			break;
		case 30:
			height=190;
			titleTxt="修改联系电话备注";
			url="phoneListAction.do?op=toAltPhoneRe&phlId="+arg[0]+"&remark="+encodeURIComponent(arg[1]);
			break;
		case 32:
			height=110;
			width = 290;
			titleTxt = "修改"+getTipsDateTxt(VER_ID);
			url="caseAction.do?op=toUpTipTime&casId="+arg;
			break;
		case 33:
			height=350;
			titleTxt="编辑联系电话";
			url="phoneListAction.do?op=toUpdPhone&phoneId="+arg;
			break;
		case 34:
			height=280;
			titleTxt = "编辑联系地址";
			url = "caseAction.do?op=toUpdAddress&adrId="+arg;
			break;
		case 35:
			height=190;
			titleTxt="编辑操作内容";
			url="caseAction.do?op=toAltHRCont&hurId="+arg;
			break;
		case 36:
		    width=250;
			height=136;
		    titleTxt="作废CP";
		    parent.$('casePtpC').value=0;
			url="caseAction.do?op=dropConfirm&paId="+arg+"&delType=2";
			isDel = true;
			break;
		case 38:
		   height=220;
			titleTxt="编辑催收记录";
			url="caseAction.do?op=toUpdPhoRed&prId="+arg;
			break;
		case 39:
		   height=190;
			titleTxt="编辑评语内容";
			url="caseAction.do?op=toUpdComment&cotId="+arg;
			break;
		case 5:
			height=150;
			titleTxt="导出选中案件的催收记录";
			url="caseAction.do?op=toExpCollRec&caseIds="+getBacthIds("-",false)[0]+"&expType=selected";
			//url="caseAction.do?op=outSeaHur&caseIds="+getBacthIds("-",false)[0];
			break;
		case 52:
			height=150;
			titleTxt="导出选中案件的电话";
			url="phoneListAction.do?op=toExportPhones&casIds="+getBacthIds("-",false)[0];
			//url="caseAction.do?op=outSeaHur&caseIds="+getBacthIds("-",false)[0];
			break;
		/*case 51:
			height=150;
			titleTxt="导出案件催收记录";
			url="caseAction.do?op=toExpCaseCRec&casId="+arg;
			break;*/
		
		case 61:
			height=150;
			titleTxt = "导出所选催记";
			url="caseAction.do?op=toRecordOut&prIds="+getBacthIds("",false);
			break;	
		case 62: 
			height=150;
			titleTxt = "导出查询结果催记";
			url="caseAction.do?op=toRecordOut";
			break;
		case 63: 
			height=120;
			titleTxt = "导出本案催记";
			url="caseAction.do?op=toExportSingleCase&casId="+arg;
			break;
		
		case 'PLQ':
			height=270;
			width=380;
			titleTxt="电话归属地查询";
			url="http://www.ip138.com:8080/search.asp?action=mobile&mobile="+encodeURIComponent(arg);
			break;
		
		case 'IDQ':
			height=250;
			width=380;
			titleTxt="身份证归属地查询";
			//url="commonAction.do?op=toIDLocQuery&idNum="+encodeURIComponent(arg);
			url="http://id.weixingmap.com/main.htm?card="+encodeURIComponent(arg);
			break;
			
	 	case 'SMS':
		   	height=258;
		   	titleTxt="发送短信";
		   	url="assitanceAction.do?op=toSendMsgOfPhone&phone="+arg[0]+"&name="+arg[1]+"&rel="+arg[2]+"&conType="+arg[3]+"&casId="+arg[4];
		   	break;
			
			
		case 'APP_LAW':
			height=740;
			titleTxt="申请诉讼";
			url="lawAction.do?op=toSaveLawCaseByCas&casId="+arg;
			break;
			
		case 'BAT_APP_ASS':
			height=300;
			titleTxt="申请协催";
			url="assitanceAction.do?op=toBatchSaveAss&casIds="+getBacthIds("-",false)[0];
			break;
			
		case 'GET_REC_BY_CASE':
			height=500;
			width=800;
			titleTxt="查看案件录音记录";
			url="soundRecordAction.do?op=toListSoundRecOfCase&casId="+arg;
			hasScroll = true;
			break;
		case 'GET_REC_BY_PHONE':
			height=500;
			width=800;
			titleTxt="查看电话录音记录";
			url="soundRecordAction.do?op=toListSoundRecOfCase&callPhone="+arg;
			hasScroll = true;
			break;
	}
	createPopWindow(idPre,titleTxt,url,width,height,true,hasScroll,hasTitle);
}

/* 
	弹出层录入,编辑
*/
function hurryAssDiv(n,id,typName){
	var width;
	var height;
	var idPre="";
	var titleTxt="";
	var tips="（带<span class='red'>*</span>为必填字段）";
	var url;
	
	height =260;
	titleTxt="申请"+typName+tips;
	switch(n){
	case 10:
        height =285;
		//url="assitanceAction.do?op=toSaveAss&caseId="+id+"&typeId="+n;
		url="assitanceAction.do?op=toAppSms&casId="+id;
		break;
	default:
		url="assitanceAction.do?op=toSaveAss&caseId="+id+"&typeId="+n;
	}
	createPopWindow(idPre,titleTxt,url,width,height);
}

//批量操作
function batchOp(opCode,isCheck,args){
	var isToOp = true;
	if(isCheck==undefined || isCheck){
		if(!checkBoxIsEmpty("priKey")){
			isToOp = false;
		}
	}
	if(isToOp){
		switch(opCode){
		case 0://快速分案
			casePopDiv(21);
			break;
		case 1://批次自动分案
			casePopDiv(211,args);
			break;
		case 2://手动分案
			var casIds = getBacthIds("-",false)[0];
			if(casIds!=''){
				openPostWindow("caseAction.do",[["op","toAssignCases"],["casIds",casIds.substring(0,casIds.length-1)]],"assCaseWin");
			}
			break;
		case 3://暂停案件
			caseBatOpPopDiv(1);
			break;
		case 4://关闭案件
			caseBatOpPopDiv(2);
			break;
		case 5://退案
			caseBatOpPopDiv(4);
			break;
		case 6://恢复案件
			caseBatOpPopDiv(3);
			break;
		case 7://评语
			casePopDiv(23);
			break;
		case 8://修改催收状态
			casePopDiv(37);
			break;
		case 9://导出所有
			casePopDiv('BAT_CASE_OUT_ALL');
			break;
		case 10://导出所选
			casePopDiv('BAT_CASE_OUT_CHECKED');
			break;
		case 11://删除案件
			caseBatOpPopDiv(5);
			break;
		case 12://导出所有（案件查询）
			casePopDiv('CASE_OUT_ALL');
			break;
		case 13://导出选中（案件查询）
			casePopDiv('CASE_OUT_CHECKED');
			break;
		case 14://列表分案
			casePopDiv(212);
			break;
		case 15://导出选中案件催记
			casePopDiv(5);
			break;
		case 16://导出选中催记
			casePopDiv(61);
			break;	
		case 17://查询结果自动分案
			casePopDiv(213,args);
			break;
		case 18://批量标色
			casePopDiv('batColor');
			break;
		case 19://删除催记
			caseBatOpPopDiv(6);
			break;
		
		case 20://导出选中案件电话
			casePopDiv(52);
			break;
			
		case 21://批量申请协催
			casePopDiv('BAT_APP_ASS');
			break;
			
		case 22://部门案件自动分案
			casePopDiv(214);
			break;
		}
	}
}
/*
	判断批量分配时是否已经勾选
	type:要删除的实体类型
*/
/*function checkBatchC(n){
	if(checkBoxIsEmpty("priKey")){
		casePopDiv(n);
	}
}*/

/*
	判断批量暂停时是否已经勾选
	type:要删除的实体类型
*/
/*function batchStop(divType){
	if(checkBoxIsEmpty("priKey")){
		batchAddDiv1(divType);
	}
}*/

/*
	批量操作案件弹出层
*/
function caseBatOpPopDiv(n){
	var priKey= $N("priKey");
	var width=230;
	var height=120;
	var idPre="";
	var titleTxt="";
	switch(n){
		case 1:
			titleTxt="暂停案件";
			url="caseAction.do?op=toBatchStop&delType=1";
			break;
		case 2:
			titleTxt="关闭案件";
			url="caseAction.do?op=toBatchClose&delType=1";
			break;
		case 3:
			titleTxt="恢复案件";
			url="caseAction.do?op=toBatchRecov&delType=1";
			break;
		case 4:
			titleTxt = "退案";
			url = "caseAction.do?op=toBatchBack";
			break;
		case 5:
			titleTxt = "删除案件";
			url = "caseAction.do?op=toBatDelCase";
			break;
		case 6:
			titleTxt = "删除催记";
			url = "caseAction.do?op=toBatDelPhoRed";
			break;
	}
	createPopWindow(idPre,titleTxt,url,width,height,true,false);
}

function caseDelDiv(n,id,isIfrm){
	var width;
	var height;
	var idPre="";
	var titleTxt="";
	var url;
	
	switch(n){
		case 1:
			titleTxt="删除联系电话";
			break;
		case 2:
			titleTxt="删除联系地址";
			height=155;
			break;
		case 3:
			titleTxt="删除操作记录";
			break;
		case 4:
			titleTxt="删除催收记录";
			break;
		case 5:
			titleTxt = "删除评语";
			break;
	}
	createConfirmWindow(idPre,titleTxt,url,width,height,id,n,isIfrm);
}
//返回删除实体名称，url数组
function getDelObj(delType,id){
	var delObj = new Array();
	switch(parseInt(delType)){
		case 1:
			delObj[0]="该联系电话";
			delObj[1]="phoneListAction.do?op=delPhone&phlId="+id;
			break;
		case 2:
			delObj[0]="该联系地址（及其关联的协催记录和外访记录）";
			delObj[1]="caseAction.do?op=delAdd&adrId="+id;
			break;
		case 3:
			delObj[0]="该操作记录";
			delObj[1]="caseAction.do?op=delHurRec&hurId="+id;
			break;
		case 4:
			delObj[0]="该催收记录";
			delObj[1]="caseAction.do?op=delPhoRed&prId="+id;
			break;
		case 5:
			delObj[0]="该评语";
			delObj[1]="caseAction.do?op=delComment&cotId="+id;
			break;
	}
	return delObj;
}

//格式化ID输入框（只能输入数字 , -）
function formatIdRangeInput(obj){
	var n = obj.value;
	if(n!=""||n!=undefined){
		obj.value = n.replace(/([^,\-0-9]+)/g,'');
	}
}