/**
 * 加载通道下拉
 * @param selectorId
 */
function loadChannelList(selectorId, defaultChannel, defaultPhone){
	$("ctiPhone").value = "";//清空
	var serverObj = $(selectorId);
	var url="ctiServerAction.do";
	var pars="op=listChannel&serverId="+serverObj.value;
	new Ajax.Request(url,{
		method:'get',
		parameters:	pars,
		onSuccess:function(response){
			var jsonData = response.responseText;
			var channelObj = $("channelNo");
			if (jsonData != "") {
				if(jsonData.indexOf("error")!=0){
					jsonData = response.responseText.evalJSON();
					var listObj = jsonData.list;
					if(listObj!=undefined && listObj!=""){
						channelObj.options.length=0; 
						channelObj.add(new Option("无",""));
						for(var i=0; i<listObj.length; i++){
							var obj = listObj[i];
							var channelOption = new Option(obj.channelNo + "-" + obj.tel, obj.channelNo);
							if (defaultChannel === "" && defaultPhone !== undefined && defaultPhone !== "" && defaultPhone === obj.tel) {//旧版本未保存账号对应通道号，只有本机号码
								channelOption.selected = true;
							}
							channelObj.options.add(channelOption);
						}
					}
					if(defaultChannel !== undefined && defaultChannel !== "") {
						channelObj.value=defaultChannel;
					}
				}
				else{
					var errMsg	= jsonData.length > 6 ? jsonData.substring(6) : jsonData;
					channelObj.options.length=0; 
					channelObj.add(new Option(errMsg,""));
				}
			}
			else {
				channelObj.options.length=0; 
				channelObj.add(new Option("无法连接录音服务器",""));
			}
		},
		onfailure:function(transport){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}
/**
 * 获取本机号码保存（通道下拉文本）
 */
function setCtiPhone() {
	$("ctiPhone").value = "";//清空
	if($("channelNo").value != "") {
		var channelSelected = $("channelNo").options[$("channelNo").selectedIndex].text;
		if(channelSelected.indexOf("-") != -1) {
			$("ctiPhone").value = channelSelected.substring(channelSelected.indexOf("-")+1);
		}
	}
}

/*
	密码校验
*/
function toCheckValidPwd(pwdObj){
	if(pwdObj.value!=''){
		var url="userAction.do";
		var pars="op=checkPwdValid&pwd="+encodeURIComponent(pwdObj.value);
		new Ajax.Request(url,{
			method:'post',
			parameters:	pars,
			onSuccess:function(transport){
				var resp = transport.responseText;
				if(resp=="suc"){
					$('pwdCheckRs').value="";
					setErrStyle(pwdObj.id,false,'pwdValidDiv');
				}
				else{
					$('pwdCheckRs').value=resp;
					$('pwdValidDiv').innerHTML="&nbsp;<img class='imgAlign' src='images/content/fail.gif' alt='警告'/>&nbsp;"+getPwdCheckRs();
					setErrStyle(pwdObj.id,true,'pwdValidDiv');
				}
			},
			onfailure:function(transport){
				if (transport.status == 404)
					alert("您访问的url地址不存在！");
				else
					alert("Error: status code is " + transport.status);
			}
		});
	}
}
function getPwdCheckRs(){
	var rs = $('pwdCheckRs').value.split(',');
	var checkRs = "";
	if(rs.length>0){
		checkRs = "密码为"+rs[1]+"-20位";
		switch(rs[0]){
			case '0': checkRs +="的字母或数字或特殊符号"; break;
			case '1': checkRs +="，包含字母和数字"; break;
			case '2': checkRs +="，包含大小写字母和数字"; break;
			case '3': checkRs +="，包含字母和数字和特殊符号"; break;
			case '4': checkRs +="，包含大小写字母和数字和特殊符号"; break;
		}
	}
	return checkRs;
}


function getSypRule(){
	var sypRuleStr = ["字母或数字或特殊符号","包含字母和数字","包含大小写字母和数字","包含字母和数字和特殊符号","包含大小写字母和数字和特殊符号"];
	var sypRuleValue = ['0','1','2','3','4'];
	return [sypRuleStr,sypRuleValue];
}
function getSypRuleStr(sypRule){
	return getTypeStr(getSypRule(),sypRule);
}
function loadSypRule(selectorId,dfValue){
	createSelector(selectorId,getSypRule()[0],getSypRule()[1],dfValue);
}


/* 
	弹出层
*/
function addDiv(n,code){
	var width=680;
	var height=510;
	var idPre="";
	var titleTxt="";
	var tips="（带<span class='red'>*</span>为必填字段）";
	var isDel = false;
	var hasTitle = true;
	var hasScroll = false;
	switch(n){	
		case 'setSmsNum':
			if(code==""){
				alert("未勾选账号");
				return false;
			}
			else{
				titleTxt="设置短信额度";
				height=120;
				width=300;
				url="userAction.do?op=toSetSmsMaxNum&userCodes="+code;
				break;
			}
		
		/*case 1:
			titleTxt="添加下级账号"+tips;
			height=280;
			width=450;
			url="userAction.do?op=goAddUser&curUserCode="+code;
			break;*/	
			
		case 2:
			titleTxt="新建账号"+tips;
			height=420;
			width=450;
			url="userAction.do?op=goAddUser";
			break;
				
        case 3:
			titleTxt="编辑账号信息"+tips;
			height=420;
			width=450;
			url="userAction.do?op=goUpdate&curUserCode="+code;
			break;	
			
		case 4:
			titleTxt="关闭账号";
			url="userAction.do?op=delConfirm&code="+code+"&delType=1";
			isDel = true;
			break;
			
		//case 5:
			//titleTxt="添加同级账号"+tips;
			//height=350;
			//width=500;
			//url="userAction.do?op=goAddEmp&userCode="+code+"&sub=sub";
			//break;	
	    
		//------------------职位管理------------------------

		case 6:
			titleTxt="修改职位"+tips;
			height=270;
			width=420;
			url="userAction.do?op=goUpdateRole&rolId="+code;
			break;
		case 7:
			titleTxt="删除职位";
			url="userAction.do?op=delConfirm&code="+code+"&delType=2";
			isDel = true;
			break;	
		
		case 8:
			titleTxt="添加职位"+tips;
			height=270;
			width=420;
			url="userAction.do?op=toSaveRole";
			break;
			
		case 9:
			url="system/roleLev.jsp";
			width=258;
			height=52;
			hasTitle = false;
			break;
			//修改授权信息
		case 10:
			titleTxt="导入授权信息"+tips;
			height=150;
			width=300;
			url="system/updateSystem.jsp";
			break;
			
		case 11:
			titleTxt="设置账号区域";
			height=265;
			width=300;
			url="caseAction.do?op=toListUserArea&userCode="+code;
			break;
			
		//------------------权限组管理------------------------
		case 20:
			titleTxt="添加权限组";
			height=635;
			url="userAction.do?op=toSaveLimGroup";
			break;
		case 21:
			titleTxt="修改权限组";
			height=635;
			url="userAction.do?op=toSaveLimGroup&grpId="+code;
			break;
		case 22:
			titleTxt="删除权限组";
			url="userAction.do?op=delConfirm&code="+code+"&delType=limGrp";
			isDel = true;
			break;	
			
		case 'sms_pr':	
			height=505;
			width=1200;
			titleTxt="短信发送记录";
			url="userAction.do?op=toListSmsPrByEmp&seId="+code;
			hasScroll = true;
			break;
		
		case 'bindUser':	
			height=260;
			width=null;//使用默认宽度
			titleTxt="绑定外访设备"+tips;
			url="mobDeviceAction.do?op=toSaveMobDevice&userCode="+code;
	}
	if(isDel){
		width=250;
		height=136;
	}
	createPopWindow(idPre,titleTxt,url,width,height,true,hasScroll,hasTitle);
}

/* 判断登录名*/
function checkLoginName(oldName){
	var name=$("login").value;
	var url = "userAction.do?op=checkLoginName&loginName="+name+"&oldName="+oldName;
	request.open("GET", url, true);
	request.onreadystatechange =getInfo;
	request.send(null);
} 
function getInfo() {
	 if (request.readyState == 4)
	  if (request.status == 200)
	       {
	           var response = request.responseText;
	           response=response.replace(/[\r\n]/g,"");
	           var span=$("info");
	           if(response!=null&&response!="")
	           {
	             var rpc_str="<span class='red'>"+response+"</span>";
	             span.innerHTML=rpc_str;
	             $("Submit").disabled=true;
	           }
	           else
	           {
	              span.innerHTML="";
	              $("Submit").disabled= false;
	           }
	     }
	       
	    else if (request.status == 404)
	         alert("您访问的url地址不存在！");
	    else
	         alert("Error: status code is " + request.status);
}
function cancelBatchSys(divType,delName){
	var priKey= $N("priKey");
	var flag=false;
	for(var i=0;i<priKey.length;i++){ 
		if(priKey[i].checked==true){ 
			flag=true; 
		}
	} 
	if(!flag){
		alert("请选择要操作的"+delName+"！");
		return false;	
	}
	 batchAddDiv1(divType);
}
function batchAddDiv1(n){
	var width=230;
	var height=120;
	var idPre="";
	var titleTxt="";
	switch(n){
		case 1:
			titleTxt="删除账号日志";
			url="userAction.do?op=toDeleteUserLog";
			break;
	}
	createPopWindow(idPre,titleTxt,url,width,height,true,false);
}