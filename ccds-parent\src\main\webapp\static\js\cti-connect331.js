function connectCTI(phone){
	createProgressBar();
	var url = "connectCTI.do?op=connectToUCSServie";
	var pars = [];
	pars.phone = phone;
	pars.serverIP = "";
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var rs = response.responseJSON;
			if(rs.code=='0'){
				alert("拨号成功");
			}
			else{
				alert("拨号失败["+rs.code+"-"+rs.msg+"]")
			}
			closeProgressBar();
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}