body{
	background:#fff url(../images/login/simple/loginbg.gif) repeat-x;
}

a:link,a:visited {
	text-decoration:none;
	color:#999;
}
a:hover {
	text-decoration:underline;
	color:#666;
}
a:active {
	text-decoration:none;
	color:#666;
}

#contentbox{
	background: url(../images/login/simple/formbg.gif) 0 90px no-repeat;
	margin-left: auto;
   	margin-right: auto;
	width:850px;
	height:660px;
	text-align:center;
}

#logo {
	height:280px;
	background:url(../images/login/normal/login_logo.png) center 210px no-repeat;
}

#loginLayer {
	margin-left: auto;
   	margin-right: auto;
	width:400px;
	height:240px;
	*height:290px;
	padding:30px 0 10px 0;
	text-align:left;
}
#login,#reg{
	width:100%;
	height:80px;
	padding:0;
	/*float:left;*/
}
.loginInpName {
	font-size:18px;
	color:#151b58;
	width:80px;
	padding: 0 10px 0 0;
}
.loginInpRow {
	padding:0 0 20px 0;
	text-align:right;
	width:290px;
}
#txt_regName , #txt_regCode{
	width:70px;
	height:32px;
	/*float:left;*/
	background-repeat:no-repeat;
	background-position: 0 5px;
	font-size:18px;
}
/*#txt_loginName {
	background-image:url(../images/login/simple/login_name.gif);
}
#txt_paswd {
	background-image:url(../images/login/simple/login_pwd.gif);
}
#txt_regName {
	background-image:url(../images/login/simple/reg_name.gif);
}
#txt_regCode {
	background-image:url(../images/login/simple/reg_code.gif);
}*/
#loginName,#pwd,#regName,#regcode, #captchaLayer {
	/*margin:0 0 20px 0;
	padding:5px;*/
	width:200px;
	/*float:left;*/
}
#loginName input,#pwd input,#regName input,#regcode input , #captchaLayer input{
	background:none;
	width:100%;
	border:none;
	font-size:16px;
	/*height:20px;*/
}

#loginBtnLayer {
	padding:20px 0 0 90px;
}

#loginBut, #regBut{
	margin:0 0 5px 0;
	width:100px;
	height:30px;
	font-size:18px;
	font-weight:bold;
	text-align:center;
}

/*
#loginBut, #regBut{
	float:left;
	width:100px;
	height:85px;
	font-size:19px;
	font-weight:bold;
	text-align:center;
}*/

.redInput {
	border:#ff0000 1px solid;
	background-color:#fde9e4;
}
.orgInput {
	border:#fa9512 1px solid;
	background-color:#fdf8df;
}
.grayInput {
	border:#354c9a 1px solid;
	background-color:#fff;
}
#loginTips,#regTips,#submitTips1,#submitTips2 {
	margin-top:4px;
	padding:4px;
	height:auto;
	font-size:12px;
	background-color:#ff4800;
	color:#fff;
	text-align:left;
}
#submitTips1,#submitTips2 {
	background-color:#215aca;
	color:#fff;
}

#leftLayer {
	display:none;
	width:0;
}

#botTips {
	clear:both;
	margin-left: auto;
   	margin-right: auto;
	color:#FF9900;
	padding:10px;
	border-bottom:#CCC 1px solid;
	text-align:right;
	width:80%;
}

#botInf {
	margin-left: auto;
   	margin-right: auto;
	padding:10px;
	color:#999999;
	font-size:12px;
	text-align:right;
	width:80%;
}
#captchaLayer {
	width:84px;
}
#captchaImgLayer {
	padding:8px 0 0 0;
}
#captchaImg{
	width:80px; 
	height:22px;
}