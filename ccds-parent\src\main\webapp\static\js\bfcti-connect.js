/**
 * 用于JS脚本中判断是否启用BF模块
 * @returns {Boolean}
 */
function HAS_BF_MOD_FUNC(){
	return true;
}

/*
 *  根据IP获取本机号码  
 */
function getLocalPhoneByIP(localPhoneLayer){
	if(document.getElementById(localPhoneLayer)!=null){
		var url = "ctiServerAction.do?op=getLocalPhone";
		var pars = [];
		new Ajax.Request(url,{
			method		:	'post',
			parameters	:	pars,
			onSuccess	:	function(response){
				var rs = response.responseText;
				if(rs.indexOf("0")==0){
					$(localPhoneLayer).innerHTML=rs.substring(2);
				}
				else{
					if(rs.indexOf("error")==0){
						rs = rs.substring(6);
					}
					$(localPhoneLayer).innerHTML=rs;
				}
//				closeProgressBar();
			},
			onfailure 	: 	function(response){
				if (transport.status == 404)
					alert("您访问的url地址不存在！");
				else
					alert("Error: status code is " + transport.status);
			}
		});
	}
}

/*
 * 拨号
 */
function connectCTI(phone, resendCount){
//	createProgressBar();
	var docObj = document;
	if($("caseId")==null){
		docObj = parent.document;
	}
	if (resendCount === undefined) {
		docObj.getElementById("dialInfo").innerHTML="<span class='waiting'>正在发送号码...</span>";
	}
	docObj.getElementById("phoneToDialOfBot").value=phone;
	var casId = docObj.getElementById("caseId").value;
	var url = "ctiServerAction.do?op=invokeDial";
	var pars = [];
	pars.casId = casId;
	pars.callPhone = phone;
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var rs = response.responseText;
			$("isTalk").value = "0";//初始化未通话状态
			var dialInfoObj = $("dialInfo");
			if(dialInfoObj==null){
				dialInfoObj = parent.document.getElementById("dialInfo");
			}
			if(rs === "0"){
				dialInfoObj.innerHTML="<span class='suc'>拨号成功</span>";
				$("isTalk").value = "1";//标记通话状态，此值为1时不能直接关闭页面（需先挂机）
			}
			else if(rs.indexOf("-1") == 0) {//未摘机,等待摘机
				dialInfoObj.innerHTML="<span class='waiting'>号码已发送，请摘机</span>";
				if (resendCount === undefined) {
					resendCount = 0;
				}
				resendCount++;
				if (resendCount < 10) {//最多重新请求10次
					setTimeout("connectCTI('" + encodeString(phone) + "'," + resendCount + ")",1000);//1s后重新请求
				}
				else {
					dialInfoObj.innerHTML="<span class='err'>长时间未摘机，请重新拨号</span>";
				}
			}
			else{
				dialInfoObj.innerHTML="<span class='err'>拨号失败！"+rs+"</span>";
			}
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}

/*
 * 挂机
 */
function toHangUp(){
//	createProgressBar();
	$("dialInfo").innerHTML="<span class='waiting'>正在停止录音...</span>";
	var url = "ctiServerAction.do?op=invokeHungUp";
	var pars = [];
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			$("isTalk").value = "0";
			var rs = response.responseText;
			if(rs=="0"){
				$("dialInfo").innerHTML="<span class='hangUp'>已停止录音</span>";
			}
			else{
				if(rs.indexOf("error")==0){
					rs = rs.substring(6);
				}
				$("dialInfo").innerHTML="<span class='err'>停止录音失败！"+rs+"</span>";
			}
//			closeProgressBar();
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}