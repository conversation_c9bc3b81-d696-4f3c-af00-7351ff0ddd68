/* 
	新建、编辑弹出层
*/
function mobPopDiv(n,arg){
	var width;
	var height;
	var idPre="";
	var titleTxt="";
	var tips="（带<span class='red'>*</span>为必填字段）";
	var url;			
	switch(n){
		case 1:
			height=260;
			titleTxt="添加外访设备"+tips;
			url="mobDeviceAction.do?op=toSaveMobDevice";
			break;
		case 11:
			height=260;
			titleTxt="修改外访设备"+tips;
			url="mobDeviceAction.do?op=toSaveMobDevice&mdvId="+arg;
			break;
	}
	createPopWindow(idPre,titleTxt,url,width,height);		
}

/* 
	删除弹出确认层
*/
function mobDelDiv(n,id){
	var width;
	var height;
	var idPre="";
	var titleTxt="";
	var url;
	
	switch(n){
		case 1:
			titleTxt="删除外访设备";
			url = "mobDeviceAction.do?op=toDeleteMobDevice&mdvId="+id;
			height = 200;
			break;
	}
	createConfirmWindow(idPre,titleTxt,url,width,height);
}

function toBatchOpOfCheckin(opType){
	var idPre="";
	var titleTxt="";
	switch(opType){
		case 'delCheckin':
			titleTxt="删除签到记录";
			break;
	}
	createBatchOpConfirm(idPre, titleTxt, opType);
}
function getBatchOpInfo(opType){
	var opInfo = new Array();
	switch(opType){
	case 'delCheckin':
			opInfo[0] = "确定要删除选中签到记录吗？";
			opInfo[1] = "checkinAction.do";
			opInfo[2] = "deleteCheckin";
			break;
	}
	return opInfo;
}
