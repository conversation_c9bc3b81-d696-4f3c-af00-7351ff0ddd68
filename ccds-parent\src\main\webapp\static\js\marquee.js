var marqueeContent;   //滚动内容
var marqueeInterval;  //定时器[0]滚动定时器[1]更新文本位置定时器
var marqueeId;//内容数组索引
var marqueeDelay=3000;
var marqueeHeight=20;
function initMarquee(marqueeLayerId) {
	marqueeId=0;
	if(marqueeInterval!=undefined){
		clearInterval(marqueeInterval[0]);
		clearInterval(marqueeInterval[1]);
	}
	marqueeInterval=new Array();
	
	if(marqueeContent==null){
		$(marqueeLayerId).innerHTML='<div id=marqueeBox style="overflow:hidden;height:'+marqueeHeight+'px"><div style="color:#bbb;">最近没有新闻公告&nbsp;</div></div>';
	}
	else{
		var str=marqueeContent[0];
		$(marqueeLayerId).innerHTML='<div id=marqueeBox style="overflow:hidden;height:'+marqueeHeight+'px" onmouseover="clearInterval(marqueeInterval[0])" onmouseout="marqueeInterval[0]=setInterval(\'startMarquee()\',marqueeDelay)"><div>'+str+'</div></div>';
		if(marqueeContent.length>1){
			marqueeId++;
			marqueeInterval[0]=setInterval("startMarquee()",marqueeDelay);
		}
	}
}
function startMarquee() {
	var str=marqueeContent[marqueeId];
	marqueeId++;
	if(marqueeId>=marqueeContent.length) marqueeId=0;
	if(marqueeBox.childNodes.length==1) {
		var nextLine=document.createElement('DIV');
		nextLine.innerHTML=str;
		marqueeBox.appendChild(nextLine);
	}
	else {
		marqueeBox.childNodes[0].innerHTML=str;
		marqueeBox.appendChild(marqueeBox.childNodes[0]);
		marqueeBox.scrollTop=0;
	}
	clearInterval(marqueeInterval[1]);
	marqueeInterval[1]=setInterval("scrollMarquee()",20);
}	
function scrollMarquee() {
	marqueeBox.scrollTop++;
	if(marqueeBox.scrollTop%marqueeHeight==(marqueeHeight-1)){
		clearInterval(marqueeInterval[1]);
	}	
}