server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    max-threads: 800
    min-spare-threads: 30

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: **********************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
  # 移除JPA配置，项目使用MyBatis
  profiles:
    active: dev
  application:
    name: ccds
  messages:
    basename: i18n/messages
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  # JSP视图配置
  mvc:
    view:
      prefix: /WEB-INF/jsp/
      suffix: .jsp

mybatis:
  type-aliases-package: com.frsoft.ccds.**.domain
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  config-location: classpath:mybatis/mybatis-config.xml

# 日志配置
logging:
  level:
    com.frsoft.ccds: debug
    org.springframework: warn
    org.mybatis: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/ccds.log

# PageHelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 系统配置
ccds:
  # 文件上传路径
  profile: D:/upload/ccds
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

