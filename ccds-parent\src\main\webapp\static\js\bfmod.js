function formatSecond(totalSeconds){
	return formatSecondSingle(totalSeconds);//默认转换方式
}
/**
 * 秒转为时分秒
 * @param totalSeconds
 * @returns {String}
 */
function formatSecondFull(totalSeconds){
	var second = parseInt(totalSeconds);// 秒
	if(!isNaN(second)){
	    var minute = 0;// 分
	    var hour = 0;// 小时
	    if(second > 60) {
	        minute = parseInt(second/60);
	        second = parseInt(second%60);
	        if(minute > 60) {
	            hour = parseInt(minute/60);
	            minute = parseInt(minute%60);
	        }
	    }
	    var result = (hour>0?(hour+"小时"):"")+(minute>0?(minute+"分"):"")+second+"秒";
	    return result;
	}
	else{
		return "";
	}
}

/**
 * 秒转为秒或分钟（超过一分钟转为分钟）
 * @param totalSeconds
 * @returns {String}
 */
function formatSecondSingle(totalSeconds){
	var second = parseInt(totalSeconds);// 秒
	if(!isNaN(second)){
	    var result = "";// 分
	    if(second > 60) {
	    	result = parseInt(second/60) + "分钟";
	    }
	    else{
	    	result = second + "秒";
	    }
	    return result;
	}
	else{
		return "";
	}
}

function getCallType(callType){
	var callTypeStr = "";
	if(callType != undefined && callType != ""){
		if(callType=='1'){
			callTypeStr = "<span class='red'>呼入</span>";
		}
		else if(callType=='2'){
			callTypeStr = "<span class='deepGreen'>呼出</span>";
		}
	}
	return callTypeStr;
}


/**
 * 生成录音文件下载链接（相对路径可在线查看，绝对路径只能下载）
 * @param filePath
 * @param id
 * @returns {String}
 */
function getRecFile(filePath,id){
	if(filePath != "" && $("getDownloadAccess")!=null&&$("getDownloadAccess").style.display!="none"){//无权限则此元素display==none
		var recFileA = "";
		var serverIP = "";
		if($("serverId")!=null){
			serverIP = $("ctiServer"+$("serverId").value).value;
		}
		else{
			serverIP = $("curServerIP").value;
		}

		//filePath = "/records/demo.wav";//for [DEMO]
		if(filePath.indexOf('/')>0){
			recFileA = "<a href='http://"+serverIP+"/download?id="+id+"' target='_self'>[点击下载]</a>";//绝对路径改为文件下载
		}
		else{
			recFileA = "<a href='http://"+serverIP+filePath+"' target='_blank'>[点击查看]</a>"
		}
		return recFileA;
	}
	else{
		return "";
	}
}

/* 
	新建、编辑弹出层
*/
function ctiPopDiv(n,arg){
	var width;
	var height;
	var idPre="";
	var titleTxt="";
	var tips="（带<span class='red'>*</span>为必填字段）";
	var url;			
	switch(n){
		case 1:
			height=150;
			titleTxt="添加录音服务器"+tips;
			url="ctiServerAction.do?op=toSaveCtis";
			break;
		case 11:
			height=150;
			titleTxt="修改录音服务器"+tips;
			url="ctiServerAction.do?op=toSaveCtis&ctisId="+arg;
			break;
		
		case 2:
			width=300;
			height=170;
			titleTxt="更新通道"+tips;
			url="ctiServerAction.do?op=toSaveChannel&channelNo="+arg[0]+"&tel="+arg[1]+"&ip="+arg[2];//+"&uCode="+arg[2]+"&uName="+arg[3];
			break;
		case 21:
			width=230;
			height=120;
			titleTxt="清空通道";
			url="ctiServerAction.do?op=toCleanChannel&channelNo="+arg[0];
			break;
			
		case 3:
			height=200;
			titleTxt="导出录音";
			url="soundRecordAction.do?op=toExportSoundRec";
			break;
	}
	createPopWindow(idPre,titleTxt,url,width,height);		
}


function ctiDelDiv(n,id,isIfrm){
	var width;
	var height;
	var idPre="";
	var titleTxt="";
	var url;
	
	switch(n){
		case 1:
			titleTxt="删除录音服务器";
			break;
	}
	createConfirmWindow(idPre,titleTxt,url,width,height,id,n,isIfrm);
}
//返回删除实体名称，url数组
function getDelObj(delType,id){
	var delObj = new Array();
	switch(parseInt(delType)){
		case 1:
			delObj[0]="该录音服务器";
			delObj[1]="ctiServerAction.do?op=deleteCtis&ctisId="+id;
			break;
	}
	return delObj;
}