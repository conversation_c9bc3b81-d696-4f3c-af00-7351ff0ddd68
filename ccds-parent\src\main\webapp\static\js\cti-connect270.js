function connectCTI(phone){
	createProgressBar();
	var url = "connectCTI.do?op=connectToUniproud";
	var pars = [];
	pars.phone = phone;
//	pars.serverIP = "*************";
//	pars.account = "";
	pars.serverIP = "**************";
	pars.pwd = "1";
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var rs = response.responseText.evalJSON();
			if(rs.success){
				if(rs.result==1){
					alert("拨号成功");
				}
				else if(rs.result==-1){
					alert("拨号失败[分机未上限]");
				}
				else if(rs.result==-2){
					alert("拨号失败[分机正在通话中]");
				}
				else{
					alert("拨号失败["+rs.result+"]");
				}
			}
			else{
				alert("拨号失败["+rs.code+"-"+rs.msg+"-"+rs.errors+"]")
			}
			closeProgressBar();
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}

/*function connectCTI(phone){
	createProgressBar();
	var url = "connectCTI.do?op=connectToJust";
	var pars = [];
	pars.phone = phone;
	pars.serverIP = "";
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var rs = response.responseText;
			if(rs.indexOf("|")!=-1){
				var rsArray = rs.split("|");
				if(rsArray[0]=="0"){
					alert("拨号成功！");
				}
			}
			else{
				alert("拨号失败！"+rs);
			}
			closeProgressBar();
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}*/