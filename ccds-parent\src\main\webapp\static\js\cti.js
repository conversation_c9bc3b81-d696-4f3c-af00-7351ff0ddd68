var LOCAL_PHO_CK_NAME = "localPhoNum";
var LOCAL_CTIIP_CK_NAME = "localCtiIp";

function initCTIIfm(hasCti,verId){
	if(hasCti=="1"&&(verId=="3"||verId=="111"||verId=="160"||verId=="175"||verId=="257"||verId=="320"||verId=="389"||verId=="419"||verId=="477")){//verId=="117"){
		if(verId=="389"){
			parent.indexFrmSet.rows="84,*,100";
		}
		else if(verId=="477"){
			parent.indexFrmSet.rows="84,*,60";
		}
		else{
			parent.indexFrmSet.rows="84,*,50";
		}
		var cframe = parent.ctiFrame;
		cframe.location="connectCTI.do?op=toCTIFrame";
		/*
		if (cframe.attachEvent){ 
			cframe.attachEvent("onload", function(){ 
				loginOfBJXB(); 
			}); 
		} else { 
			cframe.onload = function(){ 
				loginOfBJXB(); 
			}; 
		} 
		*/ 
	}
}
/*
function loginOfBJXB(){//登录
	var params = {};
	params.hotLine = "";  
	params.cno =  ""; 
	params.pwd =  ""; 
	params.bindTel =  ""; 
	params.bindType = "";
	params.initStatus =  "";
	//if(params.bindType == "3"){
		//params.sipIp = document.getElementById('sipIp').value;
		//params.sipPwd = document.getElementById('sipPwd').value;
	}
	parent.ctiFrame.contentWindow.executeAction('doLogin',  params);
}
*/
function initCTIBtn(){
	if(document.getElementById("phoneNum")!=null) document.getElementById("phoneNum").style.width="90px";
	if(isShowHungUp(document.getElementById("verId").value)){
		document.getElementById("callBtnLayer").innerHTML="<button class='inputBoxAlign' onClick='toDial()' title='点击拨号' style='font-size:10px;width:30px;'>拨号</button>&nbsp;<button class='inputBoxAlign' onClick='toHungUp()' title='点击停止录音' style='font-size:10px;width:30px;'>停止录音</button>";
	}
	else{
		document.getElementById("callBtnLayer").innerHTML="<button class='inputBoxAlign' onClick='toDial()' title='点击拨号' style='background:url(images/content/dial.gif) 2px 2px no-repeat #fff; padding:2px 0 0 10px;'>拨号</button>";
	}
	document.getElementById("callBtnLayer").style.display="";
}

function toDial(phoNum){
	if(phoNum==undefined||phoNum==""){
		if(document.getElementById("phnTxt")!=null&&document.getElementById("phnTxt").style.display!="none"){
			phoNum = document.getElementById("phnTxt").innerText;
		}
		else if(document.getElementById("phoneNum")!=null){
			phoNum = document.getElementById("phoneNum").value;
		}
		else{
			alert("拨出号码为空！");
			return;
		}
	}
	if(phoNum!=""){
		connectCTI(phoNum);
	}
}


function saveLocalPhoNum(obj){
	waitSubmit(obj);
	var url = "userAction.do";
	var pars = $("setCtiInf").serialize(true);
	pars.op = "updateCtiInf";
	new Ajax.Request(url,{
		method : 'post',
		parameters : pars,
		onSuccess : function(response){
			var resp = response.responseText;
			if(resp=='1'){
				alert("保存成功");
			}
			else{
				alert("保存失败，请重新保存");
			}
			restoreSubmit(obj);
		},
		onfailure : function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}

function connectCCIC(phone){
	var url = "connectCTI.do?op=connectToCCIC";
	var pars = [];
	pars.phone = phone;
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var rs = response.responseText.evalJSON();
			var rsStr = "操作失败";
			switch(rs.res){
				case '0': rsStr = "座席已接听"; break;
				case '1': rsStr = "呼叫座席失败"; break;
				case '2': rsStr = "参数不正确"; break;
				case '3': rsStr = "用户验证没有通过"; break;
				case '4': rsStr = "账号被停用"; break;
				case '5': rsStr = "资费不足"; break;
				case '6': rsStr = "指定的业务尚未开通"; break;
				case '7': rsStr = "电话号码不正确"; break;
				case '8': rsStr = "座席工号（cno）不存在"; break;
				case '9': rsStr = "座席状态不为空闲，可能未登录或忙"; break;
				case '10': rsStr = "其他错误"; break;
				case '11': rsStr = "电话号码为黑名单"; break;
				case '12': rsStr = "座席不在线"; break;
				case '13': rsStr = "座席正在通话/呼叫中"; break;
				case '14': rsStr = "透传号码不正确"; break;
			}
			alert(rsStr);
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}

/*

function getLocalPhoNum(){
	var lpn = readCk(LOCAL_PHO_CK_NAME);
	if(lpn==undefined||lpn==null){
		lpn="";
	}
	return lpn;
}
function getCtiIP(){
	var cip = readCk(LOCAL_CTIIP_CK_NAME);
	if(cip==undefined||cip==null){
		cip="";
	}
	return cip;
}

function loadLocalPhoAndIP(){
	if(document.getElementById("localPhoNum")!==null){
		document.getElementById("localPhoNum").value = getLocalPhoNum();
	}
	if(document.getElementById("ctiIP")!==null){
		document.getElementById("ctiIP").value = getCtiIP();
	}
}

function setLocalPhoNum(localPhoNum){
	saveCk(LOCAL_PHO_CK_NAME, localPhoNum, 24*30);
}

function setCtiIP(ctiIP){
	saveCk(LOCAL_CTIIP_CK_NAME, ctiIP, 24*30);
}

*/