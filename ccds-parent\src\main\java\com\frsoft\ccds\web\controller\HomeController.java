package com.frsoft.ccds.web.controller;

import com.frsoft.ccds.common.core.controller.BaseController;
import com.frsoft.ccds.common.core.domain.AjaxResult;
import com.frsoft.ccds.system.service.IStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

/**
 * 首页控制器
 * 
 * <AUTHOR>
 */
@Controller
public class HomeController extends BaseController {
    
    @Autowired
    private IStatisticsService statisticsService;
    
    /**
     * 首页
     */
    @GetMapping("/index")
    public String index(ModelMap mmap) {
        // 获取首页统计数据
        Map<String, Object> statistics = statisticsService.getHomeStatistics();
        mmap.put("statistics", statistics);
        return "index";
    }
    
    /**
     * 获取首页统计数据
     */
    @PostMapping("/home/<USER>")
    @ResponseBody
    public AjaxResult getHomeStatistics() {
        Map<String, Object> statistics = statisticsService.getHomeStatistics();
        return AjaxResult.success("获取成功", statistics);
    }
    
    /**
     * 获取案件统计数据
     */
    @PostMapping("/home/<USER>")
    @ResponseBody
    public AjaxResult getCaseStatistics() {
        Map<String, Object> statistics = statisticsService.getCaseStatistics();
        return AjaxResult.success("获取成功", statistics);
    }
    
    /**
     * 获取催收统计数据
     */
    @PostMapping("/home/<USER>")
    @ResponseBody
    public AjaxResult getCollectionStatistics(@RequestParam(required = false) String startDate,
                                            @RequestParam(required = false) String endDate) {
        Map<String, Object> statistics = statisticsService.getCollectionStatistics(startDate, endDate);
        return AjaxResult.success("获取成功", statistics);
    }
    
    /**
     * 获取还款统计数据
     */
    @PostMapping("/home/<USER>")
    @ResponseBody
    public AjaxResult getPaymentStatistics(@RequestParam(required = false) String startDate,
                                         @RequestParam(required = false) String endDate) {
        Map<String, Object> statistics = statisticsService.getPaymentStatistics(startDate, endDate);
        return AjaxResult.success("获取成功", statistics);
    }
    
    /**
     * 获取员工绩效统计
     */
    @PostMapping("/home/<USER>")
    @ResponseBody
    public AjaxResult getEmployeePerformance(@RequestParam(required = false) String startDate,
                                           @RequestParam(required = false) String endDate) {
        Map<String, Object> statistics = statisticsService.getEmployeePerformance(startDate, endDate);
        return AjaxResult.success("获取成功", statistics);
    }
    
    /**
     * 获取逾期分析数据
     */
    @PostMapping("/home/<USER>")
    @ResponseBody
    public AjaxResult getOverdueAnalysis() {
        Map<String, Object> analysis = statisticsService.getOverdueAnalysis();
        return AjaxResult.success("获取成功", analysis);
    }
    
    /**
     * 获取回收率统计
     */
    @PostMapping("/home/<USER>")
    @ResponseBody
    public AjaxResult getRecoveryRateStatistics(@RequestParam(required = false) String startDate,
                                              @RequestParam(required = false) String endDate) {
        Map<String, Object> statistics = statisticsService.getRecoveryRateStatistics(startDate, endDate);
        return AjaxResult.success("获取成功", statistics);
    }
    
    /**
     * 系统信息页面
     */
    @GetMapping("/system/info")
    public String systemInfo(ModelMap mmap) {
        // 获取系统信息
        Map<String, Object> systemInfo = getSystemInfo();
        mmap.put("systemInfo", systemInfo);
        return "system/info";
    }
    
    /**
     * 获取系统信息
     */
    @PostMapping("/system/info/data")
    @ResponseBody
    public AjaxResult getSystemInfoData() {
        Map<String, Object> systemInfo = getSystemInfo();
        return AjaxResult.success("获取成功", systemInfo);
    }
    
    /**
     * 获取系统信息
     */
    private Map<String, Object> getSystemInfo() {
        Map<String, Object> systemInfo = new java.util.HashMap<>();
        
        // JVM信息
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        systemInfo.put("jvmTotalMemory", formatBytes(totalMemory));
        systemInfo.put("jvmUsedMemory", formatBytes(usedMemory));
        systemInfo.put("jvmFreeMemory", formatBytes(freeMemory));
        systemInfo.put("jvmMemoryUsage", Math.round((double)usedMemory / totalMemory * 100));
        
        // 系统信息
        systemInfo.put("osName", System.getProperty("os.name"));
        systemInfo.put("osVersion", System.getProperty("os.version"));
        systemInfo.put("javaVersion", System.getProperty("java.version"));
        systemInfo.put("javaVendor", System.getProperty("java.vendor"));
        
        // 应用信息
        systemInfo.put("appName", "催收系统");
        systemInfo.put("appVersion", "1.0.0");
        systemInfo.put("springBootVersion", "2.7.18");
        
        return systemInfo;
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return Math.round(bytes / 1024.0) + " KB";
        } else if (bytes < 1024 * 1024 * 1024) {
            return Math.round(bytes / (1024.0 * 1024.0)) + " MB";
        } else {
            return Math.round(bytes / (1024.0 * 1024.0 * 1024.0)) + " GB";
        }
    }
}
