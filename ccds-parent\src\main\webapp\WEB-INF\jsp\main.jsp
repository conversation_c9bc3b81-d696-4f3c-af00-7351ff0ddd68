<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>催收管理系统 - 主页</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .logout-btn {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        .logout-btn:hover {
            background-color: #c0392b;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        .menu-item {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
            text-decoration: none;
            color: #2c3e50;
        }
        .menu-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            text-decoration: none;
            color: #2c3e50;
        }
        .menu-item h4 {
            margin: 10px 0;
            color: #3498db;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        .stat-item {
            flex: 1;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>催收管理系统</h1>
        <div class="user-info">
            <span>欢迎，${user.userName}！</span>
            <a href="/logout" class="logout-btn">退出登录</a>
        </div>
    </div>

    <div class="container">
        <div class="dashboard">
            <div class="card">
                <h3>系统概览</h3>
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-number" id="totalCases">-</div>
                        <div class="stat-label">总案件数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="activeCases">-</div>
                        <div class="stat-label">活跃案件</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="todayRecords">-</div>
                        <div class="stat-label">今日记录</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>快速操作</h3>
                <p>选择下面的功能模块开始工作：</p>
                <ul>
                    <li>案件管理：查看和管理银行案件</li>
                    <li>催收记录：记录和查看催收活动</li>
                    <li>数据统计：查看各种统计报表</li>
                </ul>
            </div>
        </div>

        <div class="menu-grid">
            <a href="/case/listCase" class="menu-item">
                <h4>📋 案件管理</h4>
                <p>查看案件列表<br>案件分配<br>案件详情</p>
            </a>

            <a href="/case/listRecord" class="menu-item">
                <h4>📞 催收记录</h4>
                <p>催收记录管理<br>通话记录<br>跟进情况</p>
            </a>

            <a href="/case/statistics" class="menu-item">
                <h4>📊 数据统计</h4>
                <p>催收统计<br>业绩报表<br>数据分析</p>
            </a>

            <a href="/system/user" class="menu-item">
                <h4>👥 用户管理</h4>
                <p>用户信息<br>权限管理<br>密码修改</p>
            </a>

            <a href="/system/config" class="menu-item">
                <h4>⚙️ 系统设置</h4>
                <p>系统配置<br>参数设置<br>数据备份</p>
            </a>

            <a href="/help" class="menu-item">
                <h4>❓ 帮助中心</h4>
                <p>使用说明<br>常见问题<br>技术支持</p>
            </a>
        </div>
    </div>

    <script>
        // 页面加载时获取统计数据
        window.onload = function() {
            loadStatistics();
        };

        function loadStatistics() {
            // 这里可以通过AJAX获取实际的统计数据
            // 暂时使用模拟数据
            document.getElementById('totalCases').textContent = '1,234';
            document.getElementById('activeCases').textContent = '567';
            document.getElementById('todayRecords').textContent = '89';
        }
    </script>
</body>
</html>
