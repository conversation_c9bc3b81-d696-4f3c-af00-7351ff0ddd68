

function connectCTI(phone){
	var casId = $("caseId")!=null?$("caseId").value:parent.document.getElementById("caseId").value;
	var ajaxParas = ({
        url: "http://127.0.0.1:10000/agent/call",              //请求地址
        type: "POST",                       //请求方式
        data: "dst="+encodeURIComponent(phone)+"&csid="+casId+"&callback=",        //请求参数
        dataType: "json",
        callback: "callback",
        success: function (jsonData) {
        	var rs = jsonData;
        	if(rs.code=="0"){
				alert("拨号成功");
			}
			else{
				alert("拨号失败！"+getErrMsg(rs.code));
			}
        },
        fail: function (jsonData) {
        	var rs = jsonData;
            alert(rs.message);
        }
    });
	
	jsonp(ajaxParas);
}

function jsonp(options) {
    options = options || {};
    if (!options.url || !options.callback) {
        throw new Error("参数不合法");
    }

    //创建 script 标签并加入到页面中
    var callbackName = ('jsonp_' + Math.random()).replace(".", "");
    var oHead = document.getElementsByTagName('head')[0];
    options.data+= callbackName;
    var params = options.data;
    var oS = document.createElement('script');
    oHead.appendChild(oS);

    //创建jsonp回调函数
    window[callbackName] = function (json) {
        oHead.removeChild(oS);
        //clearTimeout(oS.timer);
        window[callbackName] = null;
        options.success && options.success(json);
    };
    //发送请求
    oS.src = options.url + '?' + params;

    //超时处理
   /* if (options.time) {
        oS.timer = setTimeout(function () {
            window[callbackName] = null;
            oHead.removeChild(oS);
            options.fail && options.fail({ message: "超时" });
        }, time);
    }*/
};

function getErrMsg(code){
	var errMsg = "";
	switch(code){
	case 200: errMsg ="请求参数不足或取值有误";break;
	case 204: errMsg ="空闲状态不支持的请求";break;
	case 205: errMsg ="振铃状态不支持的请求";break;
	case 206: errMsg ="通话状态不支持的请求";break;
	case 207: errMsg ="话后状态不支持的请求";break;
	case 208: errMsg ="小休状态不支持的请求";break;
	case 209: errMsg ="处理状态不支持的请求";break;
	case 210: errMsg ="未登录状态不支持的请求";break;
	case 211: errMsg ="UAS 连接失败";break;
	case 212: errMsg ="UAS 内部错误";break;
	case 220: errMsg ="CM 连接失败";break;
	case 221: errMsg ="CM 内部错误";break;
	case 222: errMsg ="CM 返回错误：参数有误";break;
	case 223: errMsg ="CM 返回错误：企业ID 有误";break;
	case 224: errMsg ="CM 返回错误：调用API 失败";break;
	case 250: errMsg ="SID 无效";break;
	case 1000: errMsg ="服务器内部错误（如数据库连接失败等）";break;
	case 1001: errMsg ="请求参数不足或取值有误";break;
	case 1002: errMsg ="身份验证失败（如用户名、密码错误）";break;
	case 1003: errMsg ="账号已在其他地方登录";break;
	case 1004: errMsg ="企业不存在";break;
	case 1005: errMsg ="企业状态异常";break;
	case 1006: errMsg ="企业余额不足";break;
	case 1007: errMsg ="电话号码有误";break;
	case 1008: errMsg ="获取前缀失败";break;
	case 1009: errMsg ="直线号不存在";break;
	case 1010: errMsg ="分机号不存在";break;
	case 1011: errMsg ="分机号未授权";break;
	case 1012: errMsg ="号码不存在";break;
	case 1013: errMsg ="号码重复";break;
	case 1014: errMsg ="工号不存在";break;
	default: errMsg=code;
	}
	return errMsg;
}
