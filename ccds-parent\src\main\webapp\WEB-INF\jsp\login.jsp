﻿﻿<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%@ taglib uri="http://struts.apache.org/tags-bean"  prefix="bean"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
String isHavLogin=(String)request.getAttribute("isHavLogin");
  if(isHavLogin==null||isHavLogin.equals("")){
  		out.print("<script type='text/javascript'>window.location.href='userAction.do?op=start'</script>");
		out.flush();
  }
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
	<!--  v3.6.81 @2017.5.10  -->
    <base href="<%=basePath%>"></base>
    <title><bean:message key="SYS_NAME"/></title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="robots" content="nofollow" />
	<meta name="robots" content="noarchive" />
	<link rel="stylesheet" type="text/css" href="css/style.css">
    <link rel="stylesheet" type="text/css" href="css/<bean:message key="CSS_LOGIN"/>">
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript" src="js/md5.js"></script>
    <script type="text/javascript">
		function refreshCaptcha(){
			$('captchaImg').src='jcaptcha?' + Math.random();
		}
		
    	function changeInput(args){
			args[0].parentNode.className=(args[1]=="1")?"orgInput":"grayInput"; 
		}
		
		function check(){
			var error="";
		  	if(isEmpty("lname")){
				error+="未输入登录名!<br/>";
				$("loginName").focus();
				$("loginName").className="redInput";
		  	}
			if(isEmpty("lpwd")){
				error+="未输入密码!<br/>";
				$("pwd").focus();
				$("pwd").className="redInput";
			}
			if($("captchaLayer")!=null&&isEmpty("captchaCode")){
				error+="未输入验证码!<br/>";
				$("captchaLayer").focus();
				$("captchaLayer").className="redInput";
			}
			if(error!=""){
				$("loginTips").show();
				$("loginTips").innerHTML=error;
				return false;
			}else{
				$("pwdToUpd").value = hex_md5($("lpwd").value);
				$("loginTips").hide();
				$("submitTips1").show();
		  		return $("login").submit();
		  	}
		}
		
		function check2(){
			var error="";
			if(isEmpty("cusName")){
				error+="未输入注册名称!<br/>";
				$("regName").focus();
				$("regName").className="redInput";
			}
			if(isEmpty("password")){   
				error+="未输入授权码!<br/>";
				$("regCode").focus();
				$("regCode").className="redInput";
			}
			if(error!=""){
				$("regTips").show();
				$("regTips").innerHTML=error;
				return false;
			}
			else{
				$("regTips").hide();
				$("submitTips2").show();
				return $("reg").submit();
			}				  
		}
		
		function submitForm(){  
			if(event.keyCode==13){  
				if("${isReg}"!=null&&"${isReg}"=="1"){
					$("regBut").click();
					//check2();
				}
				else {
					$("loginBut").click();
					//check();
				}
			}  
		}  
		
		window.onload=function(){
			if("${isReg}"!=null&&"${isReg}"=="1"){
				$("loginForm").hide();
				$("regForm").show();
			}
			else{
				$("loginForm").show();
				$("regForm").hide();
			}
			
			if("${msg}"!=null&&"${msg}"!=""){
				if("${isReg}"!=null&&"${isReg}"=="1"){
					$("regTips").show();
					$("regName").className="redInput";
					$("regCode").className="redInput";
					$("regTips").innerHTML="${msg}";
					$("method").value="updateSystem";//跳转到更新方法
				}
				else{
					if("${msg}".indexOf('验证码')!=-1){
						$("captchaLayer").className="redInput";
					}
					else{
						$("loginName").className="redInput";
						$("pwd").className="redInput";
					}
					$("loginTips").show();
					$("loginTips").innerHTML="${msg}";
				}
			}
		}
  </script>
  </head>
  <body>
    <div id="contentbox">
        <div id="logo">&nbsp;</div>
        <div id="loginLayer">
            <div id="leftLayer">
                <ul id="leftInf">
                    <li><img src="images/login/normal/loginAd1.png" style="vertical-align:middle"/>&nbsp;&nbsp;安全稳定的千万级信贷数据管理</li>
                    <li><img src="images/login/normal/loginAd2.png" style="vertical-align:middle"/>&nbsp;&nbsp;高效快捷的协同催收流程</li>
                    <li><img src="images/login/normal/loginAd3.png" style="vertical-align:middle"/>&nbsp;&nbsp;全面多样的催收报表和催收报告</li>
                </ul>
            </div>
            <div id="loginForm">
                <form id="login" action="userAction.do" method="post">
                    <input type="hidden" name="op" value="doLogin" />
                    <input type="hidden" name="userCode" value="" />
                    <input type="hidden" id="pwdToUpd" name="pwd" />
  					<input type="hidden" id="macAddr" name="macAddr" />
                   	<div class="loginInpRow">
                        <span class="loginInpName">登录名&nbsp;</span>
                        <span id="loginName" class="grayInput" >
                            <input type="text" name="loginName" maxlength="30" id="lname" onFocus="changeInput([this,1])" onBlur="stringFilter(this,changeInput,[this,0])" onKeyPress="submitForm()" value="<c:out value="${lName}"/>"/>
                        </span>
                    </div>
                    <div class="loginInpRow">
                        <span class="loginInpName">密码&nbsp;</span>
                        <span id="pwd" class="grayInput" >
                            <input type="password" maxlength="20" id="lpwd" onFocus="changeInput([this,1])" onBlur="stringFilter(this,changeInput,[this,0])" onKeyPress="submitForm()" value="<c:out value="${lpwd}"/>"/>
                        </span>
                    </div>
                    <c:if test="${SYS_PREF.sypHasCaptcha!=0}">
                    <div class="loginInpRow" style="padding-bottom:0;">
                        <span class="loginInpName">验证码&nbsp;</span>
                        <span id='captchaLayer' class="grayInput"><input type='text' id='captchaCode' class="inputBoxAlign" name='j_captcha_response' onFocus="changeInput([this,1])"  onBlur="changeInput([this,0])" onKeyPress="submitForm()" />
    </span>&nbsp;&nbsp;&nbsp;<span id='captchaImgLayer'><img id='captchaImg' class="inputBoxAlign hand" src="jcaptcha" onclick="refreshCaptcha()" title="点击刷新">&nbsp;<a href='javscript:void(0)' onclick="refreshCaptcha();return false;">刷新</a></span>
    				</div>
                    </c:if>
                    <div id='loginBtnLayer'><button id="loginBut" onClick="check()">登&nbsp;&nbsp;录</button></div>
                    <div id="submitTips1" style="display:none">登录中,请稍候...</div>
                    <div id="loginTips" style="display:none">&nbsp;</div>
                </form>
                <div class="HackBox"></div>
            </div>
            <div id="regForm" style="display:none;">
                <form id="reg" action="userAction.do" method="post">
                    <input type="hidden" id="method" name="op" value="iniSystem"/>
                    <input type="hidden" name="isLoginPage" value="1"/>
                    <div id="txt_regName" title="注册名称">注册名称&nbsp;</div>
                    <div id="regName" class="grayInput" >
                        <input type="text"  name="cusName" id="cusName" onFocus="changeInput([this,1])" onBlur="stringFilter(this,changeInput,[this,0])" onKeyPress="submitForm()"/>
                    </div>
                    <div id="txt_regCode" title="授权码">授权码&nbsp;</div>
                    <div id="regCode" class="grayInput" >
                        <input type="password" name="password" id="password" onFocus="stringFilter(this,changeInput,[this,1])" onBlur="changeInput([this,0])" onKeyPress="submitForm()"/>
                    </div>
                </form>
                <button id="regBut" onClick="check2()">激&nbsp;&nbsp;活</button>
                <div class="HackBox"></div>
                <div id="submitTips2" style="display:none">激活中,请稍候...</div>
                <div id="regTips" style="display:none">&nbsp;</div>
            </div>
        </div>
        <div id="botTips" style="border-bottom:0">请使用IE6以上浏览器使用本系统</div>
        <div id="botInf" ><!--<span style="float:left">客服热线:&nbsp;025&nbsp;-&nbsp;57720859</span>-->Copyright&nbsp;&nbsp;©&nbsp;&nbsp;南京枫准软件技术有限公司<br/>技术支持:&nbsp;025&nbsp;-&nbsp;57720859</div>
  </div>
  <iframe src="common/getMACAddr_D.html" style="display:none"></iframe><!-- 默认不加载,使用时去除_D -->
  </body>
</html>

