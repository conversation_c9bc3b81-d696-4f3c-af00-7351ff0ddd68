-- MySQL兼容的大量测试数据导入脚本 (正确版本)
-- 基于实际数据库表结构创建
USE ccds;

-- 清理现有数据
DELETE FROM case_paid WHERE pa_id > 0;
DELETE FROM pho_red WHERE pr_id > 0;
DELETE FROM bank_case WHERE cas_id > 5;

-- 插入大量案件数据 (bank_case表) - 使用正确的字段名
INSERT IGNORE INTO bank_case (cas_id, cas_code, cas_group, cas_state, cas_name, cas_phone, cas_m, cas_paid_m, cas_se_no, cas_ins_time, cas_ins_user, cas_alt_time, cas_alt_user, cas_remark, cas_id_no, cas_address, cas_bank, cas_card_no) VALUES
-- 工商银行案件 (6-15)
(6, 'CASE006', 'A组', 1, '孙美丽', '***********', 45000.00, 10000.00, 1, '2024-03-01 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期45天，正在协商', '320101198506061234', '江苏省苏州市姑苏区观前街303号', '工商银行', '6222021234567890006'),
(7, 'CASE007', 'A组', 1, '周文杰', '***********', 90000.00, 20000.00, 2, '2024-01-25 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期100天，失联状态', '320101198507071234', '江苏省苏州市吴中区东吴南路404号', '工商银行', '6222021234567890007'),
(8, 'CASE008', 'A组', 1, '吴晓东', '***********', 75000.00, 25000.00, 3, '2024-02-20 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期80天，承诺还款', '320101198508081234', '江苏省苏州市相城区相城大道505号', '工商银行', '6222021234567890008'),
(9, 'CASE009', 'A组', 1, '郑小芳', '***********', 55000.00, 0.00, 4, '2024-03-10 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期65天，家庭困难', '320101198509091234', '江苏省苏州市昆山市前进西路606号', '工商银行', '6222021234567890009'),
(10, 'CASE010', 'A组', 1, '刘大明', '13800138010', 100000.00, 0.00, 5, '2024-01-05 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期110天，资产查封', '320101198510101234', '江苏省苏州市张家港市杨舍镇707号', '工商银行', '6222021234567890010'),

-- 建设银行案件 (11-20)
(11, 'CASE011', 'B组', 1, '陈志华', '13800138011', 60000.00, 30000.00, 1, '2024-03-20 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期55天，积极配合', '320101198511111234', '江苏省无锡市梁溪区中山路808号', '建设银行', '6227001234567890011'),
(12, 'CASE012', 'B组', 1, '林小玲', '13800138012', 85000.00, 0.00, 2, '2024-02-05 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期95天，经济纠纷', '320101198512121234', '江苏省无锡市滨湖区太湖大道909号', '建设银行', '6227001234567890012'),
(13, 'CASE013', 'B组', 1, '黄建军', '13800138013', 70000.00, 24000.00, 3, '2024-03-05 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期70天，分期还款', '320101198601011234', '江苏省无锡市新吴区长江路1010号', '建设银行', '6227001234567890013'),
(14, 'CASE014', 'B组', 1, '许大海', '13800138014', 95000.00, 0.00, 4, '2024-02-28 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期85天，法律程序', '320101198602021234', '江苏省无锡市惠山区惠山大道1111号', '建设银行', '6227001234567890014'),
(15, 'CASE015', 'B组', 1, '谢小燕', '13800138015', 40000.00, 0.00, 5, '2024-04-01 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期40天，刚开始催收', '320101198603031234', '江苏省无锡市江阴市澄江中路1212号', '建设银行', '6227001234567890015'),

-- 农业银行案件 (16-25)
(16, 'CASE016', 'C组', 1, '马文龙', '13800138016', 110000.00, 0.00, 1, '2023-12-15 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期130天，高额欠款', '320101198604041234', '江苏省常州市天宁区延陵西路1313号', '农业银行', '6228481234567890016'),
(17, 'CASE017', 'C组', 1, '杨小梅', '13800138017', 65000.00, 18000.00, 2, '2024-02-18 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期75天，还款计划', '320101198605051234', '江苏省常州市钟楼区南大街1414号', '农业银行', '6228481234567890017'),
(18, 'CASE018', 'C组', 1, '朱志勇', '13800138018', 80000.00, 0.00, 3, '2024-01-28 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期90天，联系中断', '320101198606061234', '江苏省常州市新北区太湖东路1515号', '农业银行', '6228481234567890018'),
(19, 'CASE019', 'C组', 1, '何小丽', '13800138019', 50000.00, 8000.00, 4, '2024-03-12 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期60天，医疗费用', '320101198607071234', '江苏省常州市武进区湖塘镇1616号', '农业银行', '6228481234567890019'),
(20, 'CASE020', 'C组', 1, '冯大强', '13800138020', 75000.00, 0.00, 5, '2024-01-12 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期105天，生意失败', '320101198608081234', '江苏省常州市金坛区金城镇1717号', '农业银行', '6228481234567890020'),

-- 交通银行案件 (21-30)
(21, 'CASE021', 'D组', 1, '蒋小华', '13800138021', 55000.00, 22000.00, 1, '2024-03-25 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期50天，新案件', '320101198609091234', '江苏省镇江市京口区中山东路1818号', '交通银行', '6222601234567890021'),
(22, 'CASE022', 'D组', 1, '韩志明', '13800138022', 90000.00, 0.00, 2, '2024-01-08 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期115天，投资失败', '320101198610101234', '江苏省镇江市润州区金山路1919号', '交通银行', '6222601234567890022'),
(23, 'CASE023', 'D组', 1, '曹小红', '13800138023', 70000.00, 0.00, 3, '2024-02-12 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期85天，离婚纠纷', '320101198611111234', '江苏省镇江市丹徒区上党镇2020号', '交通银行', '6222601234567890023'),
(24, 'CASE024', 'D组', 1, '邓大伟', '13800138024', 85000.00, 0.00, 4, '2024-01-30 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期95天，房产抵押', '320101198612121234', '江苏省镇江市丹阳市云阳镇2121号', '交通银行', '6222601234567890024'),
(25, 'CASE025', 'D组', 1, '范小玲', '13800138025', 45000.00, 15000.00, 5, '2024-03-08 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期65天，教育支出', '320101198701011234', '江苏省镇江市扬中市三茅镇2222号', '交通银行', '6222601234567890025'),

-- 招商银行案件 (26-35)
(26, 'CASE026', 'E组', 1, '高志强', '13800138026', 100000.00, 0.00, 1, '2024-01-03 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期120天，跨省案件', '320101198702021234', '浙江省杭州市西湖区文三路2323号', '招商银行', '6225881234567890026'),
(27, 'CASE027', 'E组', 1, '胡小燕', '13800138027', 60000.00, 20000.00, 2, '2024-02-25 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期70天，创业失败', '320101198703031234', '浙江省杭州市拱墅区莫干山路2424号', '招商银行', '6225881234567890027'),
(28, 'CASE028', 'E组', 1, '姜大明', '13800138028', 75000.00, 0.00, 3, '2024-02-15 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期80天，股票亏损', '320101198704041234', '浙江省杭州市江干区钱江路2525号', '招商银行', '6225881234567890028'),
(29, 'CASE029', 'E组', 1, '孔小丽', '13800138029', 55000.00, 12000.00, 4, '2024-03-18 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期55天，医疗开支', '320101198705051234', '浙江省杭州市下城区体育场路2626号', '招商银行', '6225881234567890029'),
(30, 'CASE030', 'E组', 1, '雷志华', '13800138030', 80000.00, 0.00, 5, '2024-01-25 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期90天，装修贷款', '320101198706061234', '浙江省杭州市上城区解放路2727号', '招商银行', '6225881234567890030'),

-- 中国银行案件 (31-40)
(31, 'CASE031', 'F组', 1, '李大龙', '13800138031', 95000.00, 0.00, 1, '2024-01-15 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期110天，上海案件', '320101198707071234', '上海市浦东新区陆家嘴环路2828号', '中国银行', '6217861234567890031'),
(32, 'CASE032', 'F组', 1, '刘小梅', '13800138032', 65000.00, 25000.00, 2, '2024-02-22 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期75天，消费贷款', '320101198708081234', '上海市黄浦区南京东路2929号', '中国银行', '6217861234567890032'),
(33, 'CASE033', 'F组', 1, '毛志勇', '13800138033', 70000.00, 0.00, 3, '2024-03-02 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期65天，房贷逾期', '320101198709091234', '上海市静安区南京西路3030号', '中国银行', '6217861234567890033'),
(34, 'CASE034', 'F组', 1, '倪小芳', '13800138034', 50000.00, 35000.00, 4, '2024-04-05 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期45天，信用卡债务', '320101198710101234', '上海市徐汇区淮海中路3131号', '中国银行', '6217861234567890034'),
(35, 'CASE035', 'F组', 1, '欧阳华', '13800138035', 85000.00, 0.00, 5, '2024-01-18 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期100天，经营贷款', '320101198711111234', '上海市长宁区延安西路3232号', '中国银行', '6217861234567890035'),

-- 民生银行案件 (36-45)
(36, 'CASE036', 'G组', 1, '潘大伟', '13800138036', 120000.00, 0.00, 1, '2023-12-10 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期140天，北京案件', '320101198712121234', '北京市朝阳区建国门外大街3333号', '民生银行', '6226221234567890036'),
(37, 'CASE037', 'G组', 1, '秦小玲', '13800138037', 75000.00, 28000.00, 2, '2024-02-08 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期85天，科技公司', '320101198801011234', '北京市海淀区中关村大街3434号', '民生银行', '6226221234567890037'),
(38, 'CASE038', 'G组', 1, '任志明', '13800138038', 90000.00, 0.00, 3, '2024-01-22 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期95天，金融从业', '320101198802021234', '北京市西城区金融大街3535号', '民生银行', '6226221234567890038'),
(39, 'CASE039', 'G组', 1, '宋小红', '13800138039', 60000.00, 0.00, 4, '2024-02-28 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期70天，商业贷款', '320101198803031234', '北京市东城区王府井大街3636号', '民生银行', '6226221234567890039'),
(40, 'CASE040', 'G组', 1, '唐大强', '13800138040', 105000.00, 0.00, 5, '2024-01-02 00:00:00', 'admin', '2024-06-24 00:00:00', 'admin', '逾期125天，投资亏损', '320101198804041234', '北京市丰台区丽泽金融商务区3737号', '民生银行', '6226221234567890040');

-- 插入大量催收记录数据 (pho_red表) - 使用正确的字段名
INSERT IGNORE INTO pho_red (pr_id, pr_cas_id, pr_se_no, pr_con_type, pr_time, pr_contact, pr_content, pr_name, pr_rel) VALUES
-- 案件6的催收记录
(26, 6, 1, '电话', '2024-03-02 09:15:00', '***********', '客户态度良好，承认欠款，表示因为孩子教育费用导致资金紧张。', '孙美丽', '本人'),
(27, 6, 1, '电话', '2024-03-10 14:30:00', '***********', '客户提出分期还款申请，希望分4期偿还。已向上级汇报，等待审批。', '孙美丽', '本人'),
(28, 6, 1, '电话', '2024-03-15 11:20:00', '***********', '分期方案获得批准，客户同意每月还款10000元。已发送分期协议。', '孙美丽', '本人'),

-- 案件7的催收记录
(29, 7, 2, '电话', '2024-01-26 09:30:00', '***********', '多次拨打客户电话无人接听，疑似更换号码或故意回避。', '周文杰', '本人'),
(30, 7, 2, '电话', '2024-02-01 14:00:00', '13900139007', '联系到客户朋友，得知客户因生意失败外出打工，暂时无法还款。', '朋友', '朋友'),
(31, 7, 2, '上门', '2024-02-10 10:00:00', '江苏省苏州市吴中区东吴南路404号', '上门拜访客户住址，邻居反映客户已搬走，留下新地址信息。', '邻居', '邻居'),
(32, 7, 2, '电话', '2024-03-01 13:45:00', '***********', '终于联系到客户，客户承认困难，表示正在努力筹款，请求宽限时间。', '周文杰', '本人'),

-- 案件8的催收记录
(33, 8, 3, '电话', '2024-02-21 09:15:00', '***********', '客户态度良好，承认欠款，表示近期会还款，但需要时间筹集资金。', '吴晓东', '本人'),
(34, 8, 3, '电话', '2024-03-01 15:20:00', '***********', '客户提出分期还款申请，希望分3期偿还。已向上级汇报，等待审批。', '吴晓东', '本人'),
(35, 8, 3, '电话', '2024-03-10 11:10:00', '***********', '分期方案获得批准，客户同意每月还款25000元。已发送分期协议。', '吴晓东', '本人'),

-- 案件11的催收记录
(36, 11, 1, '电话', '2024-03-21 10:30:00', '13800138011', '客户主动联系，表示已准备部分资金，希望先还一部分。', '陈志华', '本人'),
(37, 11, 1, '电话', '2024-04-01 13:30:00', '13800138011', '客户支付30000元，承诺剩余款项分2期支付。态度诚恳。', '陈志华', '本人'),

-- 案件13的催收记录
(38, 13, 3, '电话', '2024-03-06 09:45:00', '13800138013', '客户态度良好，承认欠款，表示因为房屋装修导致资金紧张。', '黄建军', '本人'),
(39, 13, 3, '电话', '2024-03-20 15:20:00', '13800138013', '客户提出分期还款申请，希望分3期偿还。已向上级汇报，等待审批。', '黄建军', '本人'),
(40, 13, 3, '电话', '2024-04-01 11:10:00', '13800138013', '分期方案获得批准，客户同意每月还款12000元。已发送分期协议。', '黄建军', '本人'),

-- 案件17的催收记录
(41, 17, 2, '电话', '2024-02-19 14:20:00', '13800138017', '客户态度配合，表示愿意按计划还款，已制定详细还款计划。', '杨小梅', '本人'),
(42, 17, 2, '电话', '2024-03-15 10:30:00', '13800138017', '跟进还款计划执行情况，客户按时支付第一期款项。', '杨小梅', '本人'),

-- 案件19的催收记录
(43, 19, 4, '电话', '2024-03-13 16:00:00', '13800138019', '客户说明因医疗费用导致资金困难，提供了医院证明。', '何小丽', '本人'),
(44, 19, 4, '电话', '2024-04-10 11:20:00', '13800138019', '客户医疗费用问题缓解，主动联系还款8000元。', '何小丽', '本人'),

-- 案件21的催收记录
(45, 21, 1, '电话', '2024-03-26 09:30:00', '13800138021', '新案件首次联系，客户态度良好，承认欠款，表示会尽快处理。', '蒋小华', '本人'),
(46, 21, 1, '电话', '2024-04-15 14:45:00', '13800138021', '客户主动联系，表示已筹集到部分资金，希望先还一部分。', '蒋小华', '本人'),

-- 案件25的催收记录
(47, 25, 5, '电话', '2024-03-09 10:15:00', '13800138025', '客户说明因子女教育费用导致资金紧张，态度诚恳。', '范小玲', '本人'),
(48, 25, 5, '电话', '2024-04-20 15:30:00', '13800138025', '客户教育费用压力缓解，主动还款15000元。', '范小玲', '本人'),

-- 案件27的催收记录
(49, 27, 2, '电话', '2024-02-26 11:40:00', '13800138027', '客户创业项目遇到困难，但表示有信心解决，请求给予时间。', '胡小燕', '本人'),
(50, 27, 2, '电话', '2024-05-10 09:20:00', '13800138027', '客户创业项目有起色，主动还款20000元，承诺继续还款。', '胡小燕', '本人');

-- 插入大量还款记录数据 (case_paid表) - 使用正确的字段名
INSERT IGNORE INTO case_paid (pa_id, pa_cas_id, pa_paid_time, pa_paid_num, pa_writer, pa_wri_time, pa_sur_remark, pa_state) VALUES
-- 案件6的还款记录
(31, 6, '2024-04-01 10:30:00', 10000.00, 'admin', '2024-04-01 10:30:00', '分期还款第一期，客户按协议执行', 1),

-- 案件7的还款记录
(32, 7, '2024-03-15 15:20:00', 20000.00, 'admin', '2024-03-15 15:20:00', '找到客户后的首次还款', 1),

-- 案件8的还款记录
(33, 8, '2024-04-01 13:15:00', 25000.00, 'admin', '2024-04-01 13:15:00', '客户履行承诺，大额还款', 1),

-- 案件11的还款记录
(34, 11, '2024-04-01 10:20:00', 30000.00, 'admin', '2024-04-01 10:20:00', '客户积极配合，主动还款', 1),

-- 案件13的还款记录
(35, 13, '2024-04-20 14:50:00', 12000.00, 'admin', '2024-04-20 14:50:00', '分期还款第一期', 1),
(36, 13, '2024-05-20 16:10:00', 12000.00, 'admin', '2024-05-20 16:10:00', '分期还款第二期', 1),

-- 案件17的还款记录
(37, 17, '2024-03-15 11:30:00', 18000.00, 'admin', '2024-03-15 11:30:00', '客户按还款计划执行', 1),

-- 案件19的还款记录
(38, 19, '2024-04-10 13:20:00', 8000.00, 'admin', '2024-04-10 13:20:00', '医疗费用缓解后的还款', 1),

-- 案件21的还款记录
(39, 21, '2024-04-15 15:40:00', 22000.00, 'admin', '2024-04-15 15:40:00', '新案件快速回款', 1),

-- 案件25的还款记录
(40, 25, '2024-04-20 09:25:00', 15000.00, 'admin', '2024-04-20 09:25:00', '教育支出缓解后还款', 1),

-- 案件27的还款记录
(41, 27, '2024-05-10 12:15:00', 20000.00, 'admin', '2024-05-10 12:15:00', '创业项目有起色后还款', 1),

-- 案件29的还款记录
(42, 29, '2024-04-15 14:35:00', 12000.00, 'admin', '2024-04-15 14:35:00', '医疗费用问题解决后还款', 1),

-- 案件32的还款记录
(43, 32, '2024-04-20 10:50:00', 25000.00, 'admin', '2024-04-20 10:50:00', '消费贷款部分还款', 1),

-- 案件34的还款记录
(44, 34, '2024-05-22 16:20:00', 35000.00, 'admin', '2024-05-22 16:20:00', '信用卡债务大额还款', 1),

-- 案件37的还款记录
(45, 37, '2024-04-18 11:45:00', 28000.00, 'admin', '2024-04-18 11:45:00', '科技公司项目回款', 1);

-- 更新锁表的最大ID
UPDATE lock_table SET table_max = 1000 WHERE table_name = 'bank_case';
UPDATE lock_table SET table_max = 5000 WHERE table_name = 'pho_red';
UPDATE lock_table SET table_max = 500 WHERE table_name = 'case_paid';

COMMIT;

-- 显示导入结果
SELECT '=== 大量测试数据导入完成 ===' AS message;
SELECT '案件数量' AS type, COUNT(*) AS count FROM bank_case;
SELECT '催收记录数量' AS type, COUNT(*) AS count FROM pho_red;
SELECT '还款记录数量' AS type, COUNT(*) AS count FROM case_paid;
SELECT '总还款金额' AS type, SUM(pa_paid_num) AS total_amount FROM case_paid;
