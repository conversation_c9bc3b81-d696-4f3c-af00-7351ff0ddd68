function connectCTI(phone){
	var url = "connectCTI.do?op=connectToMVB";
	var pars = [];
	pars.phone = phone;
	pars.server = "192.168.0.251";
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var rs = response.responseText.evalJSON();
			if(rs.errcode=="0"){
				alert("拨号成功！");
			}
			else{
				var errMsg = rs.message;
				var rsData = rs.data;
				for(var i=0; i<rsData.length; i++){
					switch(rsData[i].reason){
					case "1": errMsg += " 已挂机 "; break;
					case "3": errMsg += " 振铃但无应答 "; break;
					case "4": errMsg += " 已摘机 "; break;
					case "5": errMsg += " 正忙 "; break;
					case "8": errMsg += " 阻塞 "; break; 
					}
				}
				alert("拨号失败！"+errMsg);
			}
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}