function connectCTI(phone){
	createProgressBar();
	var url = "connectCTI.do?op=connectToNVice";
	var pars = [];
	pars.phone = phone;
	pars.serverIP = "web0.9vice.com:8088";//"er03.9vice.com";
	//pars.exten = "5452";
	new Ajax.Request(url,{
		method		:	'post',
		parameters	:	pars,
		onSuccess	:	function(response){
			var rs = response.responseText;
			var rsText = '';
			switch(rs){
			case 'AUTHFAILED':
				rsText='认证失败';
				break;
			case 'NOREG':
				rsText='分机未注册';
				break;
			case 'OTHERS':
				rsText='其它错误';
				break;
			case 'INITERROR':
				rsText='初始化错误';
				break;
			case 'SUCCESSFUL':
				rsText='成功激活';
				break;
			}
			alert(rsText);
			closeProgressBar();
		},
		onfailure 	: 	function(response){
			if (transport.status == 404)
				alert("您访问的url地址不存在！");
			else
				alert("Error: status code is " + transport.status);
		}
	});
}