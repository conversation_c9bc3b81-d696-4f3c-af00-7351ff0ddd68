<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a871f935-76df-4ffa-9808-6d809a7370d6" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/sql/test-data-simple.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/jsp/case/simple-listRecord.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/config/DruidConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/config/DruidConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/config/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/config/SecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/config/WebMvcConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/config/WebMvcConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/controller/LoginController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/controller/LoginController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/controller/PhoRedController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/controller/PhoRedController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/controller/SysUserController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/controller/SysUserController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/domain/SysUser.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/domain/SysUser.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/service/impl/AuthServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/service/impl/AuthServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/service/impl/SysUserServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/service/impl/SysUserServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/web/controller/HomeController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/web/controller/HomeController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/system/SysUserMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/system/SysUserMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/WEB-INF/jsp/case/listCase.jsp" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="PUSH_TAGS">
      <GitPushTagMode>
        <option name="argument" value="--tags" />
        <option name="title" value="All" />
      </GitPushTagMode>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\devops\repository" />
        <option name="mavenHome" value="$PROJECT_DIR$/../../devops/apache-maven-3.9.1" />
        <option name="useMavenConfig" value="true" />
        <option name="userSettingsFile" value="D:\devops\apache-maven-3.9.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="jdkForImporter" value="1.8" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="delegateBuildToMaven" value="true" />
    <option name="jreName" value="1.8" />
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectId" id="2ylGokXoYsKe4p5tTokO7DB2TWd" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "ASKED_SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "com.codeium.enabled": "true",
    "jdk.selected.JAVA_MODULE": "17",
    "last_opened_file_path": "D:/cuishou/ccds-parent/pom.xml",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "preferences.keymap",
    "spring.configuration.checksum": "9218ae5ee70ff00a01513a4d1e2aef35",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.CCDSApplication">
    <configuration name="CcdsApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.frsoft.ccds.CcdsApplication" />
      <module name="ccds-web" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.frsoft.ccds.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="CCDS-maven" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="CCDS-maven" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CCDSApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ccds" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.frsoft.ccds.CCDSApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.CcdsApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="a871f935-76df-4ffa-9808-6d809a7370d6" name="更改" comment="" />
      <created>1744435260925</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744435260925</updated>
      <workItem from="1750401926177" duration="865000" />
      <workItem from="1750403743107" duration="3475000" />
      <workItem from="1750407539717" duration="1866000" />
      <workItem from="1750735980102" duration="3376000" />
      <workItem from="1750742991385" duration="126000" />
      <workItem from="1750743145853" duration="275000" />
      <workItem from="1750743651902" duration="6000" />
      <workItem from="1750743728010" duration="78000" />
      <workItem from="1750743819368" duration="29000" />
      <workItem from="1750743860069" duration="5754000" />
    </task>
    <task id="LOCAL-00001" summary="init">
      <option name="closed" value="true" />
      <created>1750402234272</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750402234272</updated>
    </task>
    <task id="LOCAL-00002" summary="修改成单体项目">
      <option name="closed" value="true" />
      <created>1750743995243</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1750743995243</updated>
    </task>
    <task id="LOCAL-00003" summary="修改成单体项目-启动成功，开始整合jsp">
      <option name="closed" value="true" />
      <created>1750749398052</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1750749398052</updated>
    </task>
    <task id="LOCAL-00004" summary="修改成单体项目-启动成功，开始整合jsp">
      <option name="closed" value="true" />
      <created>1750749475851</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1750749475851</updated>
    </task>
    <task id="LOCAL-00005" summary="排除不必要的文件">
      <option name="closed" value="true" />
      <created>1750749602295</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1750749602295</updated>
    </task>
    <option name="localTasksCounter" value="6" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="init" />
    <MESSAGE value="修改成单体项目" />
    <MESSAGE value="修改成单体项目-启动成功，开始整合jsp" />
    <MESSAGE value="排除不必要的文件" />
    <option name="LAST_COMMIT_MESSAGE" value="排除不必要的文件" />
  </component>
</project>