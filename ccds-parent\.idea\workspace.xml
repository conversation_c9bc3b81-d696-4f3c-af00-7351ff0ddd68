<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a871f935-76df-4ffa-9808-6d809a7370d6" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/mysql57-complete.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/MIGRATION_COMPLETE_REPORT.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/advanced_mssql_to_mysql.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/convert_mssql_to_mysql.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/fix-database.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/fix-simple.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/init.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/install.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/mysql-migration.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/mysql57-complete-clean.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/mysql57-complete-final.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/mysql57-complete.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/rebuild-database.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/test-data.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/core/mapper/BaseMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/core/service/BaseService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/core/service/impl/BaseServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/exception/BusinessException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/exception/ServiceException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/utils/CacheUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/utils/LogUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/utils/SecurityUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/utils/ValidationUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/controller/BankCaseController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/controller/LoginController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/controller/PhoRedController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/service/IAuthService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/service/IPhoRedService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/service/IStatisticsService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/service/impl/AuthServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/service/impl/PhoRedServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/service/impl/StatisticsServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/web/controller/HomeController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/jsp/case/listCase.jsp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/jsp/case/listRecord.jsp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/jsp/login.jsp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/CssOrgStruct.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/att.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/bfmod.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/datatables.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/desk.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/dtree.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/guide.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/import.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/login.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/login_simple.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/menu.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/nav.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/oa.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/rec.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/sta.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/style.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/typ.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/css/wms.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/acc_app.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/add.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/add_cell.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/advSearch.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/alert.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/approve.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/arrow_join.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/attIconS.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/attach.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/attachNo.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/ball.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/bigAlert.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/bigFail.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/bigSuc.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/blueArr.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/blueBlock.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/blueCircle.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/blueTableTh.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/bluebutton.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/bluebutton2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/bluebutton3.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/cake.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/calendar10.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/calendar7.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/calendarT.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/cancel.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/case_back.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/case_imp.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/close1.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/close2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/closebutton.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/closebutton2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/coins.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/colFocus.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/colNormal.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/comment_edit.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/cusSuc.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/database.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/database_go.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/date_edit.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/dbError.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/db_ok.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/del.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/detail.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/dial.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/doing.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/edit.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/editCoin.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/email.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/email_go.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/error.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/execute.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/fail.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/finish.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/finish.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/forward.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/forwardPage.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/goldenBall.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/gradualLine.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/grayArr.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/grayBack.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/grayButtonBg.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/graybg.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/green_execute.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/green_go.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/group_go.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/help.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/hide.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/invalid.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/leftArr.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/lock.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/lock_open.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/lorry.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/man.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/markGray.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/markRed.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/menu_r_border.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/mesforward.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/mesreply.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/miniLock.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/modTop.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/modTop1.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/modTop2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/modTop3.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/modTop4.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/more1.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/more2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/msn.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/new.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/newass.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/newass_gray.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/newint.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/newint_gray.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/newpaid.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/newpaid_gray.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/newpr.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/newpr_gray.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/noImg.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/no_read.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/package.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/page_excel.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/page_green.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/plugin.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/popTop_left.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/popTop_mid.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/popTop_right.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/popWinBg.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/popWinC1.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/popWinC2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/qq.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/qucikIcon1.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/qucikIcon2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/qucikIcon3.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/qucikIcon4.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/qucikIcon5.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/qucikIcon6.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/qucikIcon7.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/query.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/read.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/recycle.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/redCircle.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/reddatabase.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/refresh.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/refresh2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/refuse.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/restore.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/rightArr.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/search.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/search2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/selected.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/sendMes.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/showDesc.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/showmore.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/smalladd.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/smallpage.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/sort_asc.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/sort_desc.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/sound.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/suc.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/tel1.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/tel2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/tel3.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/thIcon.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/time.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/tips.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/tipsIcon1.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/tipsIcon2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/tipsIcon3.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/titleSearchMid.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/toLeft.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/tofinish.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/topTab1.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/topTab2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/topTab3.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/upButton.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/uploadFail.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/uploadSuc.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/url.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/view_rel.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/warn.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/web.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/wheel.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/whiteAdd.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/whiteChange1.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/whiteChange2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/whiteClose.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/whiteEdit.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/whiteHide.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/whiteHide2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/whiteLock.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/whiteMore.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/whiteSave.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/whiteShow.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/whiteShow2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/whiteTree.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/woman.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/working.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/wout_hidden.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/wpr_pro.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/wrong.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/content/xpTab.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/ass/assMail1.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/ass/assMail2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/ass/bankCp.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/ass/bankCp2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/ass/caseAss.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/ass/caseAss2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/ass/extraInf.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/ABC.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/BJJH.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/BJRCB.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/BOC.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/BOCOM.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/BOSH.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/CCB.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/CEB.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/CFP.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/CGB.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/CIB.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/CITI.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/CMB.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/CMBC.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/CZB.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/GCS.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/HCCB.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/HFB.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/HMC.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/ICBC.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/JSBC.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/MBP.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/NBCB.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/NJBC.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/NNBK.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/NWD.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/OHBC.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/PA.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/PSBC.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/SAIC.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/SHDZZY.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/SHYJ.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/SPDB.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/SYYK.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/TYT.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/UAF.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/UC.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/VC.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/YHBC.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/YX.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/banks/ZXWD.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/dirAss.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/expFile.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/impCase.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/logo-3.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/searCase1.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/searCase2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/bCase/showCase.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/hr/emp.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/hr/emp2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/hur/myCase.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/hur/searMyCase.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/hur/stat.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/stat/stat.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/vis/checkin.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/vis/map.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/vis/mobile_phone.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/vis/my_vis.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/vis/un_vis.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/vis/vis1.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/vis/vis2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dsIcon/vis/vis3.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/base.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/empnode_m.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/empnode_w.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/empty.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/folder.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/folderopen.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/join.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/joinbottom.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/line.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/minus.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/minusbottom.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/nolines_minus.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/nolines_plus.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/orgnode.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/orgopen.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/page.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/plus.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/dtree/plusbottom.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/abjlx.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/amoney.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/arecord.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/bjlx.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/dot.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/loading.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/money.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/new.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/new1.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/phone_b.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/phone_gray.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/phone_r.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/phone_y.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/record.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/gif/uploading.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/acc/acc.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/acc/account_in.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/acc/account_inv.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/acc/account_out.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/acc/account_tran.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/bfmod/chart.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/bfmod/rec.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/bfmod/server.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/bfmod/telephone.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/cus/contact.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/cus/cus.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/cus/cus_act.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/cus/cus_opp.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/cus/cus_serv.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/desktop.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/hr/emp.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/hr/emp2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/hr/empMange.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/hr/org.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/hr/pay.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/nav/deskTop.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/nav/editInf.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/nav/exit.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/nav/mail.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/nav/mail_new.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/nav/option.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/nav/rep.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/nav/sch.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/nav/task.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/oa/mes.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/oa/news.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/oa/rep.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/oa/sch.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/oa/task.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/pro/pro_act.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/pro/pro_sta.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/pro/pro_task.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/pro/project.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sal/contract.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sal/ord.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sal/ord2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sal/product.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sal/sal_sta.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sal/sal_target.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/server/done_server.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/server/un_server.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sup/inq.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sup/pur_app.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sup/pur_suc.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sup/sup.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sup/sup_con.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sys/data.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sys/group.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sys/limUser.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sys/org.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sys/recycle.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sys/role.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sys/secure.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sys/stro.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sys/template.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sys/type.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/sys/userLog.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/wms/get_wms.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/wms/set_wms.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/wms/wms_all.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/wms/wms_change.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/wms/wms_check.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/wms/wms_in.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/wms/wms_line.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/wms/wms_out.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/icon/wms/wms_show.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/login/frsoftlogo.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/login/normal/loginAd1.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/login/normal/loginAd2.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/login/normal/loginAd3.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/login/normal/login_logo.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/login/normal/login_name.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/login/normal/login_pwd.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/login/normal/loginbg.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/login/normal/reg_code.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/login/normal/reg_name.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/login/simple/formbg.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/login/simple/loginbg.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/menu/menuBottom.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/menu/menuBottom.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/menu/menuClose.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/menu/menuClose2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/menu/menuTop.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/menu/menuTop.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/menu/showMenu.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/menu/showMenu.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/menu/showMenu2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/menu/showMenu2.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/nav/nav_logo.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/nav/normal/hideButton.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/nav/normal/menu.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/nav/normal/menu_cur.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/nav/normal/menu_over.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/nav/normal/menu_r_border.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/nav/normal/nav_back.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/nav/normal/nav_bg.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/images/nav/normal/showButton.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/FusionCharts.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/JsOrgStruct.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/bdcalendar110.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/bfcti-connect.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/bfmod.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/bootstrap-datetimepicker.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/bootstrap-tabdrop.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/case.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/caseAss.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/caseBat.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cashbus.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/chooseBrow.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/choosePro.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/common.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect111.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect114.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect117.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect154.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect160.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect168.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect175.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect190.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect202.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect230.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect242.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect257.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect270.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect3.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect32.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect320.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect331.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect344.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect35.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect352.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect353.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect364.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect380.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect389.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect419.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect450.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect477.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect63.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect66.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect67.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect92.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti-connect94.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/cti.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/datatable.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/desk.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/dtree.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/formCheck.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/hr.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/hurry.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/jquery.datatables.min-basic.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/jquery.datatables.min-extend.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/jquery.slimscroll.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/jquery1.8.1.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/marquee.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/mathUtils.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/md5.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/mob.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/mobGps.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/nav.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/oa.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/ocx.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/orgtree.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/prototype.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/scripts.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/sms-connect102.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/sms-connect125.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/sms-connect134.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/sms-connect230.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/sms-connect324.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/sms-connect352.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/sms-connect408.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/sms-connect469.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/sms-connect477.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/sta.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/sys.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/template.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/type.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/vis.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/webapp/static/js/wms.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/com/frsoft/ccds/common/utils/SecurityUtilsTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/test/java/com/frsoft/ccds/common/utils/ValidationUtilsTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/logs/ccds.log" beforeDir="false" afterPath="$PROJECT_DIR$/logs/ccds.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/core/controller/BaseController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/core/controller/BaseController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/core/domain/AjaxResult.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/core/domain/AjaxResult.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/core/domain/model/LoginUser.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/common/core/domain/model/LoginUser.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/config/GlobalExceptionHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/config/GlobalExceptionHandler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/mapper/BankCaseMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/mapper/BankCaseMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/service/IBankCaseService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/service/IBankCaseService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/service/impl/BankCaseServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/system/service/impl/BankCaseServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/web/controller/LoginController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/frsoft/ccds/web/controller/system/BankCaseController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="PUSH_TAGS">
      <GitPushTagMode>
        <option name="argument" value="--tags" />
        <option name="title" value="All" />
      </GitPushTagMode>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\devops\repository" />
        <option name="mavenHome" value="$PROJECT_DIR$/../../devops/apache-maven-3.9.1" />
        <option name="useMavenConfig" value="true" />
        <option name="userSettingsFile" value="D:\devops\apache-maven-3.9.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="jdkForImporter" value="1.8" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="delegateBuildToMaven" value="true" />
    <option name="jreName" value="1.8" />
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectId" id="2ylGokXoYsKe4p5tTokO7DB2TWd" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "com.codeium.enabled": "true",
    "jdk.selected.JAVA_MODULE": "17",
    "last_opened_file_path": "D:/cuishou/ccds-parent/pom.xml",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "preferences.keymap",
    "spring.configuration.checksum": "9218ae5ee70ff00a01513a4d1e2aef35",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.CCDSApplication">
    <configuration name="CcdsApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.frsoft.ccds.CcdsApplication" />
      <module name="ccds-web" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.frsoft.ccds.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="CCDS-maven" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="CCDS-maven" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CCDSApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ccds" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.frsoft.ccds.CCDSApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.CcdsApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="a871f935-76df-4ffa-9808-6d809a7370d6" name="更改" comment="" />
      <created>1744435260925</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744435260925</updated>
      <workItem from="1750401926177" duration="865000" />
      <workItem from="1750403743107" duration="3475000" />
      <workItem from="1750407539717" duration="1866000" />
      <workItem from="1750735980102" duration="3376000" />
      <workItem from="1750742991385" duration="126000" />
      <workItem from="1750743145853" duration="275000" />
      <workItem from="1750743651902" duration="6000" />
      <workItem from="1750743728010" duration="78000" />
      <workItem from="1750743819368" duration="29000" />
      <workItem from="1750743860069" duration="3719000" />
    </task>
    <task id="LOCAL-00001" summary="init">
      <option name="closed" value="true" />
      <created>1750402234272</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750402234272</updated>
    </task>
    <task id="LOCAL-00002" summary="修改成单体项目">
      <option name="closed" value="true" />
      <created>1750743995243</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1750743995243</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="init" />
    <MESSAGE value="修改成单体项目" />
    <option name="LAST_COMMIT_MESSAGE" value="修改成单体项目" />
  </component>
</project>