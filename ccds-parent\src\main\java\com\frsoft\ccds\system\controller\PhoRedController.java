package com.frsoft.ccds.system.controller;

import com.frsoft.ccds.common.core.controller.BaseController;
import com.frsoft.ccds.common.core.domain.AjaxResult;
import com.frsoft.ccds.common.core.page.TableDataInfo;
import com.frsoft.ccds.system.domain.PhoRed;
import com.frsoft.ccds.system.service.IPhoRedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.util.List;

/**
 * 催收记录控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/case")
public class PhoRedController extends BaseController {
    
    @Autowired
    private IPhoRedService phoRedService;
    
    /**
     * 催收记录列表页面
     */
    @GetMapping("/listRecord")
    public String listRecord(Model model, HttpSession session) {
        // 检查用户是否已登录
        Object user = session.getAttribute("user");
        if (user == null) {
            return "redirect:/login";
        }
        
        return "case/listRecord";
    }
    
    /**
     * 获取催收记录列表数据
     */
    @PostMapping("/recordList")
    @ResponseBody
    public TableDataInfo recordList(PhoRed phoRed) {
        startPage();
        List<PhoRed> list = phoRedService.selectPhoRedList(phoRed);
        return getDataTable(list);
    }
    
    /**
     * 根据案件ID获取催收记录
     */
    @GetMapping("/recordList/{casId}")
    @ResponseBody
    public TableDataInfo recordListByCasId(@PathVariable("casId") Long casId) {
        List<PhoRed> list = phoRedService.selectPhoRedListByCasId(casId);
        return getDataTable(list);
    }
    
    /**
     * 催收记录详情页面
     */
    @GetMapping("/recordDetail/{prId}")
    public String recordDetail(@PathVariable("prId") Long prId, Model model) {
        PhoRed phoRed = phoRedService.selectPhoRedByPrId(prId);
        model.addAttribute("phoRed", phoRed);
        return "case/recordDetail";
    }
    
    /**
     * 新增催收记录页面
     */
    @GetMapping("/addRecord")
    public String addRecord(@RequestParam(required = false) Long casId, Model model) {
        if (casId != null) {
            model.addAttribute("casId", casId);
        }
        return "case/addRecord";
    }
    
    /**
     * 新增保存催收记录
     */
    @PostMapping("/addRecord")
    @ResponseBody
    public AjaxResult addRecordSave(@RequestBody PhoRed phoRed) {
        return toAjax(phoRedService.insertPhoRed(phoRed));
    }
    
    /**
     * 修改催收记录页面
     */
    @GetMapping("/editRecord/{prId}")
    public String editRecord(@PathVariable("prId") Long prId, Model model) {
        PhoRed phoRed = phoRedService.selectPhoRedByPrId(prId);
        model.addAttribute("phoRed", phoRed);
        return "case/editRecord";
    }
    
    /**
     * 修改保存催收记录
     */
    @PostMapping("/editRecord")
    @ResponseBody
    public AjaxResult editRecordSave(@RequestBody PhoRed phoRed) {
        return toAjax(phoRedService.updatePhoRed(phoRed));
    }
    
    /**
     * 删除催收记录
     */
    @PostMapping("/removeRecord")
    @ResponseBody
    public AjaxResult removeRecord(String ids) {
        return toAjax(phoRedService.deletePhoRedByPrIds(convertToLongArray(ids)));
    }
    
    /**
     * 根据员工编号查询催收记录
     */
    @GetMapping("/recordListByEmployee/{seNo}")
    @ResponseBody
    public TableDataInfo recordListByEmployee(@PathVariable("seNo") Long seNo) {
        List<PhoRed> list = phoRedService.selectPhoRedListBySeNo(seNo);
        return getDataTable(list);
    }
    
    /**
     * 催收记录统计
     */
    @GetMapping("/recordStatistics")
    @ResponseBody
    public AjaxResult recordStatistics(@RequestParam(required = false) Long seNo,
                                      @RequestParam(required = false) String startDate,
                                      @RequestParam(required = false) String endDate) {
        // 这里可以添加统计逻辑
        return AjaxResult.success();
    }
    
    /**
     * 导出催收记录
     */
    @PostMapping("/exportRecord")
    @ResponseBody
    public AjaxResult exportRecord(PhoRed phoRed) {
        // 这里可以添加导出逻辑
        return AjaxResult.success("导出成功");
    }
}
